﻿Imports ProtoBuf
Imports Utilities

<PetaPoco.PrimaryKey("ID_CLIENT_HISTORIQUE")>
<ProtoContract>
Partial Public Class client_historique
    Implements IHasID
    <ProtoMember(1)> Public Property id_client_historique As Integer
    <ProtoMember(2)> Public Property id_client As Integer Implements IHasID.id
    <ProtoMember(3)> Public Property date_debut As DateTime
    <ProtoMember(4)> Public Property date_fin As DateTime
    <ProtoMember(5)> Public Property rabais_volume_lbs As Decimal
    <ProtoMember(6)> Public Property rabais_actif As Boolean
    <ProtoMember(7)> Public Property rabais_type As Integer
    <ProtoMember(8)> Public Property rabais_montant As Decimal
    <ProtoMember(9)> Public Property cout_emballage As Decimal
    <ProtoMember(10)> Public Property com_emballage As String
    <ProtoMember(11)> Public Property prix_vente_inclut_transport As Boolean
    <ProtoMember(12)> Public Property cout_transport_lbs_us As Decimal
    <ProtoMember(13)> Public Property taux_commission As Decimal?
    <ProtoMember(14)> Public Property taux_commission_lbs As Decimal?
    <ProtoMember(15)> Public Property ID_DEVISE_TAUX_COMMISSION_LBS As Integer


End Class