﻿Imports ProtoBuf

<ProtoContract> <PetaPoco.PrimaryKey("id_commande_machine")>
Partial Public Class commande_machine
    <ProtoMember(1)> Public Property id_commande_machine As Integer
    <ProtoMember(2)> Public Property id_commande As Integer
    <ProtoMember(3)> Public Property id_machine As Integer
    <ProtoMember(5)> Public Property date_debut_setup As DateTime?
    <ProtoMember(6)> Public Property date_fin_setup As DateTime?
    <ProtoMember(7)> Public Property date_debut_commande As DateTime?
    <ProtoMember(8)> Public Property date_fin_commande As DateTime?
    <ProtoMember(11)> Public Property cedule_order As Integer
    <ProtoMember(25)> Public Property temps_setup_lb As Decimal
    '<PetaPoco.Ignore> Public ReadOnly Property Rouleaux As New List(Of rouleau)
    '<PetaPoco.Ignore> Public ReadOnly Property Scrap As New List(Of commande_machine_scrap)
End Class
