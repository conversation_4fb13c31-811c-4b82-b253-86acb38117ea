Imports System.Globalization
Imports System.IO
Imports System.Net.Mime.MediaTypeNames
Imports System.Runtime
Imports System.Runtime.Caching
Imports System.Runtime.ConstrainedExecution
Imports System.Xml.Serialization
Imports Utilities
Imports DualID = System.Int64
Imports IdYearMonth = System.Int64

Public Module pexReport_Data ' Module relié au DB PolyExpert. Collections + Fonctions qui contiennent ou retourne des structures de tables.

#If DEBUG Then
    Public COMMANDE_MACHINE_CHECK As Integer = -1 '247863 '  258838    ' 260698 ' 257206
#End If

#If DEBUG Then
    Public ppdb As PetaPoco.Database = GetDB()
    Public ppdb_report As PetaPoco.Database = GetDB("PEReport") ' DevRpt - vmSQLDev14; PEReport - live
    Public bForceReadFromDB As Boolean = True
#Else
    Public ppdb As PetaPoco.Database = GetDB()
    Public ppdb_report As PetaPoco.Database = GetDB("PEReport")
    Public bForceReadFromDB As Boolean = True
#End If

    Public rouleaux As List(Of rouleau)
    Public scrap As List(Of commande_machine_scrap)
    Public scrap_rouleau As List(Of commande_machine_scrap)
    Public scrap_pourc As List(Of commande_machine_scrap)
    Public lstCM As List(Of commande_machine)
    Public lstSVG As List(Of std_variable_globale)

    '' Lookups
    Public dicCmdMacMult As Dictionary(Of Integer, List(Of commande_machine))
    Public dicCmdStr As Dictionary(Of Integer, List(Of commande_structure))
    Public dicRsnCouHis As Dictionary(Of Integer, List(Of resine_cout_historique))
    Public dicStdRsnHis As Dictionary(Of Integer, List(Of std_resine_historique))
    Public dicStdPvRsnHis As Dictionary(Of Integer, List(Of std_pv_resine_historique))
    Public lstStdPvRsnHis As List(Of std_pv_resine_historique)
    Public dicRsnTypHis As Dictionary(Of Integer, List(Of resine_type_historique))
    Public lstRsnTypHis As List(Of resine_type_historique)
    Public dicTauxChange As Dictionary(Of DateTime, taux_change)
    Public lstCommande As List(Of commande)
    Public dicCommande As Dictionary(Of Integer, commande)
    Public dicPV As Dictionary(Of Integer, PV)
    Private dicPVHisMens As Dictionary(Of Integer, List(Of pv_historique_mensuel))
    Public dicProduit As Dictionary(Of Integer, produit)
    Public dicRouleau_Pal As Dictionary(Of Integer, List(Of rouleau))
    Public dicMachine As Dictionary(Of Integer, machine)

    Public dicVendeur As Dictionary(Of Integer, vendeur)
    Public dicClient As Dictionary(Of Integer, client)
    Public dicClasse As Dictionary(Of Integer, classe)
    Public dicMarche As Dictionary(Of Integer, marche)
    Public dicProduitCateg As Dictionary(Of Integer, produit_categ)
    Public dicProduitClient As Dictionary(Of DualID, produit_client)
    Public dicTerritoire As Dictionary(Of Integer, territoire)
    Public dicForme As Dictionary(Of Integer, forme)
    Public dicDevise As Dictionary(Of Integer, devise)
    Public dicSecteur As Dictionary(Of Integer, listesecteur)
    Public dicStatut As Dictionary(Of Integer, statut)
    Public dicStatutExped As Dictionary(Of Integer, statut_expedition)
    Public dicStatutFact As Dictionary(Of Integer, statut_facturation)
    Public dicStdProduitClient As Dictionary(Of (id_std_prd_cli As Integer, annee As Integer, mois As Integer), std_produit_client)
    Public dicStdProduit As Dictionary(Of (id_prd As Integer, annee As Integer, mois As Integer), std_produit)
    Public dicCmdPal_Cmd As Dictionary(Of Integer, List(Of commande_palette))
    Public dicCmdPal_Itm As Dictionary(Of Integer, List(Of commande_palette)) ' key = facture_item
    Public dicCmdPal_Req As Dictionary(Of Integer, List(Of commande_palette)) ' key = id_requisition_transport
    Public dicCmdMac_Cmd As Dictionary(Of Integer, List(Of commande_machine)) ' key = id_commande
    Public dicTypeRejetDispo As Dictionary(Of Integer, type_rejet_dispo)

    Private lstStdTaux As List(Of std_taux)
    Private lstCliHis As List(Of client_historique)

    Private lstVendeur_Histo As List(Of vendeur_historique)

    Private lstStdPVCou As List(Of std_pv_cout)
    Private dicStdPvCou As Dictionary(Of String, List(Of std_pv_cout))
    Private lstStdCatScg As List(Of std_categorie_surcharge)

    Public lstFacture As List(Of facture)
    Public dicFacture As Dictionary(Of Integer, facture)
    Public dicFactureCmd As Dictionary(Of Integer, List(Of facture))
    Public lstFactureMensuel As List(Of pexFactureMensuel)
    Public dicFactureMensuel As Dictionary(Of String, pexFactureMensuel)

    Public lstFactureItem As List(Of facture_item)
    Public dicFactureItem As Dictionary(Of Integer, facture_item) ' key = id_facture_item
    Public dicFactureItem_Fac As Dictionary(Of Integer, List(Of facture_item)) ' key = id_Facture

    Public lstPlainte As List(Of plainte)
    Public dicPlainte As Dictionary(Of Integer, plainte)
    Public dicPlainteEntente As Dictionary(Of Integer, plainte_entente)
    Public dicPlainteEntenteByPlt As Dictionary(Of Integer, List(Of plainte_entente))

    Public dicCRT As Dictionary(Of Integer, commande_requisition_transport)
    Public dicRT As Dictionary(Of Integer, requisition_transport)

    Public dicStdCoutTransportSecteur As Dictionary(Of Integer, List(Of std_cout_transport_secteur)) ' key = id_adresse
    Public dicStdCoutTransportAdresse As Dictionary(Of Integer, List(Of std_cout_transport_adresse)) ' key = id_adresse
    Public dicAdresseClientHistorique As Dictionary(Of DualID, List(Of adresse_client_historique)) ' key = (id_client, id_adresse)

    Public dicLSH As Dictionary(Of Integer, List(Of ListeSecteurHistorique)) ' key = id_secteur
    Public dicExpedition As Dictionary(Of Integer, expedition)
    Public dicTransport As Dictionary(Of Integer, transport)

    Public ReadOnly GlobalCache As MemoryCache = MemoryCache.Default
#If DEBUG Then
    Public cachePath As String = "G:\WSPex.cache\" '-todo- .ini param
    Public outPath As String = "G:\WSPex.out\" '-todo- .ini param
    'Public cachePath As String = "C:\tmp\WSPex.cache\" '-todo- .ini param
    'Public outPath As String = "C:\tmp\WSPex.out\" '-todo- .ini param
#Else
    Public cachePath As String = "C:\WebServices\WSPex\Cache\" '-todo- .ini param
    Public outPath As String = "C:\WebServices\WSPex\Out\" '-todo- .ini param
#End If
    Public EmailToError As String

    Public Devise_can As Integer
    Public devise_us As Integer

    'Public setPrecision As New NumberFormatInfo() 'test forcer precision de tous les decimal à 8 decimales. - marche pas

    Public Sub InitCaches()
        EmailToError = INI.ReadKey("Config", "EmailToError")

        'setPrecision.NumberDecimalDigits = 8 'test forcer precision de tous les decimal à 8 decimales. - marche pas

        ppdb.CommandTimeout = 500
        Log($"###")
        Log($"### Début = {Now}")
        lstCommande = GetData(Of commande)(ppdb, "", cachePath, bForceReadFromDB)
        dicCommande = lstCommande.ToDictionary(Function(k) k.id_commande, Function(v) v)
        Log($" - dicCmd={dicCommande.Count:0,0}")

        Dim lstPV = GetData(Of PV)(ppdb, "", cachePath, bForceReadFromDB)
        dicPV = lstPV.ToDictionary(Function(k) k.id_pv, Function(v) v)
        Log($" - dicPV={dicPV.Count:0,0}")

        Dim lstPVHisMens = GetData(Of pv_historique_mensuel)(ppdb, "", cachePath, bForceReadFromDB) '-todo- where id_client = 200251
        dicPVHisMens = lstPVHisMens.GroupBy(Function(x) x.id_pv).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)
        Log($" - dicPVHisMens={dicPVHisMens.Count:0,0}")

        Dim lstProduit = GetData(Of produit)(ppdb, "", cachePath, bForceReadFromDB)
        dicProduit = lstProduit.ToDictionary(Function(k) k.id_produit, Function(v) v)
        Log($" - dicPrd={dicProduit.Count:0,0}")

        Dim sql = <a><![CDATA[
select * from COMMANDE_STRUCTURE            where no_ordre = 3 and dateheure is not null and id_commande_machine is not null and id_commande_machine > 0
union all
select * from COMMANDE_STRUCTURE_HISTORIQUE where no_ordre = 3 and dateheure is not null and id_commande_machine is not null and id_commande_machine > 0	
]]></a>.Value.Trim
        Dim lstCmdStr = GetData(Of commande_structure)(ppdb, sql, cachePath, bForceReadFromDB)
        dicCmdStr = lstCmdStr.GroupBy(Function(x) x.id_commande_machine).ToDictionary(Function(k) CInt(k.Key), Function(v) v.OrderBy(Function(x) x.dateheure).ThenBy(Function(x) x.id_commande_structure).ToList)
        Log($" - dicCmdStr={dicCmdStr.Count:0,0}")

        Dim lstRsnCouHis = GetData(Of resine_cout_historique)(ppdb, "", cachePath, bForceReadFromDB)
        dicRsnCouHis = lstRsnCouHis.GroupBy(Function(x) x.id_resine).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)
        Log($" - dicRCH={dicRsnCouHis.Count:0,0}")

        Dim lstStdRsnHis = GetData(Of std_resine_historique)(ppdb, "", cachePath, bForceReadFromDB)
        dicStdRsnHis = lstStdRsnHis.GroupBy(Function(x) x.id_resine).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)
        Log($" - dicRCH={dicStdRsnHis.Count:0,0}")

        Dim lstTauxChange = GetData(Of taux_change)(ppdb, "SELECT * FROM taux_change WITH (NOLOCK)", cachePath, bForceReadFromDB)
        dicTauxChange = lstTauxChange.ToDictionary(Function(k) k.datejour, Function(v) v)
        Log($" - dicTC={dicTauxChange.Count:0,0}")

        sql = <a><![CDATA[
select id_commande, id_commande_machine from commande_machine 
where DATE_DEBUT_SETUP is not null and id_commande in 
(select id_commande from commande_machine where DATE_DEBUT_SETUP is not null group by id_commande having count(id_commande_machine) > 1) 
and id_commande_machine not in (select distinct ID_COMMANDE_MACHINE from rouleau where ID_COMMANDE = commande_machine.id_commande and ID_CONFORMITE < 2 and poids > 0)
order by id_commande, id_commande_machine
]]></a>.Value.Trim
        Dim lstCmdMacMult As List(Of commande_machine) = ppdb.Fetch(Of commande_machine)(sql)
        dicCmdMacMult = lstCmdMacMult.GroupBy(Function(x) x.id_commande).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.id_commande).ToList)

        Dim lstMachine = GetData(Of machine)(ppdb, "", cachePath, bForceReadFromDB)
        dicMachine = lstMachine.ToDictionary(Function(k) k.id_machine, Function(v) v)

        dicVendeur = GetData(Of vendeur)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_vendeur, Function(v) v)
        dicClient = GetData(Of client)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_client, Function(v) v)
        dicClasse = GetData(Of classe)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_classe, Function(v) v)
        dicMarche = GetData(Of marche)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_marche, Function(v) v)
        dicProduitCateg = GetData(Of produit_categ)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_produit_categ, Function(v) v)
        dicProduitClient = GetData(Of produit_client)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) GetDualIdKey(k.id_produit, k.id_client), Function(v) v)
        dicTerritoire = GetData(Of territoire)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_territoire, Function(v) v)
        dicForme = GetData(Of forme)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_forme, Function(v) v)
        dicDevise = GetData(Of devise)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_devise, Function(v) v)
        dicSecteur = GetData(Of listesecteur)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_secteur, Function(v) v)
        dicStatut = GetData(Of statut)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_statut, Function(v) v)
        dicStatutExped = GetData(Of statut_expedition)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_statut_exped, Function(v) v)
        dicStatutFact = GetData(Of statut_facturation)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_statut_fact, Function(v) v)
        dicStdProduitClient = GetData(Of std_produit_client)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) (k.id_produit_client, k.annee, k.mois), Function(v) v)
        dicStdProduit = GetData(Of std_produit)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) (k.id_produit, k.annee, k.mois), Function(v) v)
        dicTypeRejetDispo = GetData(Of type_rejet_dispo)(ppdb, "", cachePath, bForceReadFromDB).ToDictionary(Function(k) k.id_type_rejet_dispo, Function(v) v)

        rouleaux = GetData(Of rouleau)(ppdb, "where id_raison is null and poids_net > 0", cachePath, bForceReadFromDB, True) '-todo- db/src filter	

        Try
            devise_us = CType(dicDevise.Where(Function(x) x.Value.nom = "US").SingleOrDefault.Value, devise).id_devise
            Log($"La devise US est = {devise_us}")
            Devise_can = CType(dicDevise.Where(Function(x) x.Value.nom = "CAN").SingleOrDefault.Value, devise).id_devise

            Log($"La devise CAN est = {Devise_can}")
        Catch ex As Exception
            InformDev($"ERREUR: PAS trouvé le #devise Us et/ou Can : {ex.Message}  StackTrace:= {ex.StackTrace}")
        End Try






        sql = <a><![CDATA[
select cms.id_commandemachinescrap, cms.id_commande, cms.id_commande_machine, cms.id_machine, cms.id_rouleau, 
       cms.date_scrap, cms.poids_scrap, cms.type_setup, cms.id_type_rejet_dispo, cms.rejet_rouleau, cms.actif, cms.date_annule,
       trd.id_resine id_rsn_rcl
  from commande_machine_scrap cms
  left join type_rejet_dispo trd on trd.id_type_rejet_dispo = cms.id_type_rejet_dispo
 where cms.id_rouleau is null
   and cms.actif = 1 
   and cms.poids_scrap <> 0 
   and cms.date_annule is null
]]></a>.Value.Trim ' était (cms.type_setup = 1 or cms.id_rouleau is null)
        scrap = GetData(Of commande_machine_scrap)(ppdb, sql, cachePath, bForceReadFromDB, True) '-todo- db/src filter	



        sql = <a><![CDATA[
select cms.id_commandemachinescrap, cms.id_commande, cms.id_commande_machine, cms.id_machine, cms.id_rouleau, 
       cms.date_scrap, cms.poids_scrap, cms.type_setup, cms.id_type_rejet_dispo, cms.rejet_rouleau, cms.actif, cms.date_annule,
       trd.id_resine id_rsn_rcl
  from commande_machine_scrap cms
  left join type_rejet_dispo trd on trd.id_type_rejet_dispo = cms.id_type_rejet_dispo
 where cms.id_rouleau is not null
   and cms.actif = 1 
   and cms.poids_scrap <> 0 
   and cms.date_annule is null
]]></a>.Value.Trim
        scrap_rouleau = GetData(Of commande_machine_scrap)(ppdb, sql, cachePath, bForceReadFromDB, True)

        sql = <a><![CDATA[
select distinct coalesce(r.id_commande, cm.id_commande) as id_commande, cm.ID_COMMANDE_MACHINE
, cm.ID_MACHINE, cm.ID_EMPLOYE, cm.DATE_DEBUT_SETUP, cm.DATE_FIN_SETUP, cm.DATE_DEBUT_COMMANDE, cm.DATE_FIN_COMMANDE, cm.NB_UP, cm.FEED_MACHINE, cm.CEDULE_ORDER 
, cm.PROCHAIN_CHANG, cm.EN_ATTENTE, cm.CPT_NO_BATCH, cm.ID_CORE, cm.LAYFLAT, cm.DateCreationAuto, cm.ForcerChangementScreen, cm.ChangementScreenCompleter 
, cm.NbRouleauAProduire, cm.FEED_MACHINE_M, cm.LAYFLAT_M, cm.TEMPS_SETUP, cm.TEMPS_SETUP_DESC, cm.TEMPS_SETUP_LB, cm.INFO_CED_NB_LB_SETUP, cm.INFO_CED_COUT_SETUP 
, cm.t_stamp_debut_commande, cm.t_stamp_fin_commande
from commande_machine cm
left join rouleau r on cm.ID_COMMANDE_MACHINE = r.ID_COMMANDE_MACHINE
]]></a>.Value.Trim
        lstCM = GetData(Of commande_machine)(ppdb, sql, cachePath, bForceReadFromDB)

        lstSVG = GetData(Of std_variable_globale)(ppdb, "", cachePath, bForceReadFromDB)

        dicCmdMac_Cmd = lstCM.GroupBy(Function(x) x.id_commande).ToDictionary(Function(k) k.Key, Function(v) v.OrderByDescending(Function(x) x.date_fin_commande).ToList)
        Log($"dicCmdMacCmd={dicCmdMac_Cmd.Count:0,0}")

        lstFacture = GetData(Of facture)(ppdb, "", cachePath, bForceReadFromDB)
        Log($"lstFacture={lstFacture.Count:0,0}")
        dicFacture = lstFacture.ToDictionary(Function(k) k.id_facture, Function(v) v)
        dicFactureCmd = lstFacture.GroupBy(Function(x) x.id_commande).ToDictionary(Function(k) k.Key, Function(v) v.OrderByDescending(Function(x) x.date_facturation).ToList)
        Log($"dicFacture={dicFacture.Count:0,0}")

        sql = <a><![CDATA[

;with FACT as (

select 

 prc.id_produit_client       as [ProduitClientID]

,year(fac.date_facturation)  as [Annee]

,month(fac.date_facturation) as [Mois]

,sum(
case when upper(itm.unite) = 'LBS'    then itm.quantite_expedie
     when upper(itm.unite) = 'KG'     then round(itm.quantite_expedie * 2.2046, 4)
     when upper(itm.unite) = 'PIEDS'  then round(iif(cmd.longueur = 0, 0, cmd.poids / cmd.longueur) * itm.quantite_expedie, 4)
     when upper(itm.unite) = 'METRES' then round(iif(cmd.longueur = 0, 0, cmd.poids / cmd.longueur) * itm.quantite_expedie * 0.3049, 4)
     when upper(itm.unite) = 'ROL'    then round(itm.quantite_expedie * cmd.poids_rouleau_cal, 4)
     else 0
     end 
     ) as [FactureLbs]    
from      facture         fac
left join facture_item    itm on itm.id_facture  = fac.id_facture
left join commande        cmd on cmd.id_commande = fac.id_commande
left join pv              pv  on  pv.id_pv       = cmd.id_pv
left join produit_client  prc on prc.id_produit  =  pv.id_produit
                             and prc.id_client   =  pv.id_client
where prc.id_produit_client is not null
  and upper(itm.code_produit) not in (PRD_EXCLUS_POUR_CALCUL_POIDS)
  and fac.[type] = 'Facture'  
group by
 prc.id_produit_client
,year(fac.date_facturation)
,month(fac.date_facturation)
), RET as (
select 
 prc.id_produit_client       as [ProduitClientID]
,year(ori.date_facturation)  as [Annee]
,month(ori.date_facturation) as [Mois]
,sum(case when upper(itm.unite) = 'LBS'    then itm.quantite_expedie
          when upper(itm.unite) = 'KG'     then round(itm.quantite_expedie * 2.2046, 4)
          when upper(itm.unite) = 'PIEDS'  then round(iif(cmd.longueur = 0, 0, cmd.poids / cmd.longueur) * itm.quantite_expedie, 4)
          when upper(itm.unite) = 'METRES' then round(iif(cmd.longueur = 0, 0, cmd.poids / cmd.longueur) * itm.quantite_expedie * 0.3049, 4)
          when upper(itm.unite) = 'ROL'    then round(itm.quantite_expedie * cmd.poids_rouleau_cal, 4)
          else 0
          end) as [RetourLbs]    
from      facture         ret
left join facture_item    itm on itm.id_facture  = ret.id_facture
left join facture         ori on ori.id_facture  = ret.id_facture_retour
left join commande        cmd on cmd.id_commande = ori.id_commande
left join pv              pv  on  pv.id_pv       = cmd.id_pv
left join produit_client  prc on prc.id_produit  =  pv.id_produit
                             and prc.id_client   =  pv.id_client
where ret.[type] = 'Retour'
  and ret.id_facture_retour is not null
  and itm.id_type_retour = 6
  and upper(itm.code_produit) not in (PRD_EXCLUS_POUR_CALCUL_POIDS)
group by
 prc.id_produit_client
,year(ori.date_facturation)
,month(ori.date_facturation)
 
), SOMMAIRE as (
select FACT.ProduitClientID, FACT.Annee, FACT.Mois, FACT.FactureLbs - Coalesce(RET.RetourLbs,0) FactureLbs
from FACT
left join RET on RET.ProduitClientID = FACT.ProduitClientID
             and RET.Annee           = FACT.Annee
             and RET.Mois            = FACT.Mois
)
select * from SOMMAIRE where FactureLbs > 0
]]></a>.Value.Trim                                                        'AUTRE', 'RESINE', 'SURCHARGE', 'TRANSPORT', 'CORES'
        sql = sql.Replace("PRD_EXCLUS_POUR_CALCUL_POIDS", String.Join(",", PRD_EXCLUS_POUR_CALCUL_POIDS.Select(Function(x) $"'{x}'")))

        lstFactureMensuel = GetData(Of pexFactureMensuel)(ppdb, sql, cachePath, bForceReadFromDB)
        Log($"lstFactureMensuel={lstFacture.Count:0,0}")
        dicFactureMensuel = lstFactureMensuel.ToDictionary(Function(k) $"{k.ProduitClientID}_{k.Annee}_{k.Mois}", Function(v) v)
        Log($"dicFactureMensuel={dicFactureMensuel.Count:0,0}")

        lstFactureItem = GetData(Of facture_item)(ppdb, "", cachePath, bForceReadFromDB)
        Log($"lstFactureItem={lstFactureItem.Count:0,0}")
        dicFactureItem = lstFactureItem.ToDictionary(Function(k) k.id_facture_item, Function(v) v)
        dicFactureItem_Fac = lstFactureItem.GroupBy(Function(x) x.id_facture).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        Log($"dicFactureItem_FAc={dicFactureItem_Fac.Count:0,0}")

        '* Mettre le coût du mélange à 10 000 pour repérer les prix manquants.
        sql = <a><![CDATA[
			SELECT		id_std_pv_cout, pv, date_debut, date_fin, COALESCE(cout, 10000) as cout, id_std_categorie, tmp, sous_marche_auto, id_pv_std
			FROM		std_pv_cout
]]></a>.Value.Trim
        lstStdPVCou = GetData(Of std_pv_cout)(ppdb, sql, cachePath, bForceReadFromDB)
        Log($"lstStdPVCou={lstStdPVCou.Count:0,0}")
        dicStdPvCou = lstStdPVCou.GroupBy(Function(x) ("" & x.pv).TrimEnd.ToUpperInvariant).ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        lstStdTaux = GetData(Of std_taux)(ppdb, "", cachePath, bForceReadFromDB)
        Log($"lstStdTaux={lstStdTaux.Count:0,0}")

        lstStdCatScg = GetData(Of std_categorie_surcharge)(ppdb, "", cachePath, bForceReadFromDB)
        Log($"lstStdCatScg={lstStdCatScg.Count:0,0}")

        lstCliHis = GetData(Of client_historique)(ppdb, "order by id_client, date_debut", cachePath, bForceReadFromDB)
        Log($"lstCliHis={lstCliHis.Count:0,0}")

        lstVendeur_Histo = GetData(Of vendeur_historique)(ppdb, "", cachePath, bForceReadFromDB)
        Log($"lstVendeur_Histo={lstVendeur_Histo.Count:0,0}")


        ' Pour le calcul RequisitionFacture, on calcule le poids net différemment pour les commandes facturées en rouleaux;
        ' on inclut 2 champs "readonly" a la fin de la table afin de ne pas avoir à faire des lookups à chaque palette...
        sql = <a><![CDATA[
select 
 pal.*
,itm.unite             as fac_itm_unite
,cmd.poids_rouleau_cal as cmd_poids_rou_cal
from commande_palette pal
left join facture_item     itm on itm.id_facture_item = pal.id_facture_item
left join commande         cmd on cmd.id_commande     = pal.id_commande
]]></a>.Value.Trim
        Dim lstCmdPal = GetData(Of commande_palette)(ppdb, sql, cachePath, bForceReadFromDB)
        dicCmdPal_Cmd = lstCmdPal.GroupBy(Function(x) x.id_commande).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        dicCmdPal_Itm = lstCmdPal.GroupBy(Function(x) x.id_facture_item).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        dicCmdPal_Req = lstCmdPal.GroupBy(Function(x) x.id_requisition_transport).ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        lstPlainte = ppdb.Fetch(Of plainte)("")
        dicPlainte = lstPlainte.ToDictionary(Function(k) k.id_plainte, Function(v) v)
        Dim lstPlainteEntete = ppdb.Fetch(Of plainte_entente)("")
        dicPlainteEntente = lstPlainteEntete.ToDictionary(Function(k) k.id_plainte_entente, Function(v) v)
        dicPlainteEntenteByPlt = lstPlainteEntete.GroupBy(Function(x) x.id_plainte).ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        dicRouleau_Pal = rouleaux.GroupBy(Function(x) x.id_palette).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        '* Ce qui est expédié et facturé.
        sql = "where ID_Requisition_Transport in (select ID_Requisition_Transport from REQUISITION_TRANSPORT where expedier = 1) and id_commande in (select distinct id_commande from commande_palette where id_facture_item is not null and ID_Requisition_Transport = commande_requisition_transport.ID_REQUISITION_TRANSPORT)"
        Dim lstCRT = GetData(Of commande_requisition_transport)(ppdb, sql, cachePath, bForceReadFromDB)
        dicCRT = lstCRT.ToDictionary(Function(k) k.id_commande_requisition_transport, Function(v) v)
        Log($"dicCRT={dicCRT.Count:0,0}")

        Dim lstRT = GetData(Of requisition_transport)(ppdb, "", cachePath, bForceReadFromDB)
        dicRT = lstRT.ToDictionary(Function(k) k.id_requisition_transport, Function(v) v)
        Log($"dicRT={dicRT.Count:0,0}")

        Dim lstSCTS = GetData(Of std_cout_transport_secteur)(ppdb, "", cachePath, bForceReadFromDB)
        dicStdCoutTransportSecteur = lstSCTS.GroupBy(Function(x) x.id_secteur).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        Log($"dicStdCoutTransportSecteur={dicStdCoutTransportSecteur.Count:0,0}")

        Dim lstSCTA = GetData(Of std_cout_transport_adresse)(ppdb, "", cachePath, bForceReadFromDB)
        dicStdCoutTransportAdresse = lstSCTA.GroupBy(Function(x) x.id_adresse).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        Log($"dicStdCoutTransportAdresse={dicStdCoutTransportAdresse.Count:0,0}")


        Dim lstACH = GetData(Of adresse_client_historique)(ppdb, "", cachePath, bForceReadFromDB)
        dicAdresseClientHistorique = lstACH.GroupBy(Function(x) GetDualIdKey(x.id_client, x.id_adresse)).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        Log($"dicAdresseClientHistorique={dicAdresseClientHistorique.Count:0,0}")
        sql = <a><![CDATA[
select
  ListeSecteurHistoriqueID
 ,id_secteur
 ,date_debut
 ,date_fin
 ,coalesce(cout_transport,        9999.99) cout_transport
 ,coalesce(cout_transport_camion, 9999.99) cout_transport_camion
 ,coalesce(cout_transport_train,  9999.99) cout_transport_train
 ,coalesce(cout_transport_bateau, 9999.99) cout_transport_bateau
 ,coalesce(cout_transport_avion,  9999.99) cout_transport_avion
 ,douane_assurance
from ListeSecteurHistorique 
]]></a>.Value.Trim
        Dim lstLSH = GetData(Of ListeSecteurHistorique)(ppdb, sql, cachePath, bForceReadFromDB)
        dicLSH = lstLSH.GroupBy(Function(x) x.id_secteur).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
        Log($"dicLSH={dicLSH.Count:0,0}")

        Dim lstExpedition = GetData(Of expedition)(ppdb, "", cachePath, bForceReadFromDB)
        dicExpedition = lstExpedition.ToDictionary(Function(k) k.id_expedition, Function(v) v)
        Log($"dicExpedition={dicExpedition.Count:0,0}")

        Dim lstTransport = GetData(Of transport)(ppdb, "", cachePath, bForceReadFromDB)
        dicTransport = lstTransport.ToDictionary(Function(k) k.id_transport, Function(v) v)
        Log($"dicTransport={dicTransport.Count:0,0}")


    End Sub

    Public Sub ClearCaches()
        dicTransport.Clear()
        dicTransport = Nothing
        dicExpedition.Clear()
        dicExpedition = Nothing
        dicLSH.Clear()
        dicLSH = Nothing
        dicAdresseClientHistorique.Clear()
        dicAdresseClientHistorique = Nothing

        dicStdCoutTransportAdresse.Clear()
        dicStdCoutTransportAdresse = Nothing
        dicStdCoutTransportSecteur.Clear()
        dicStdCoutTransportSecteur = Nothing
        dicRT.Clear()
        dicRT = Nothing
        dicCRT.Clear()
        dicCRT = Nothing

        dicRouleau_Pal.Clear()
        dicRouleau_Pal = Nothing

        dicPlainteEntenteByPlt.Clear()
        dicPlainteEntenteByPlt = Nothing

        dicPlainteEntente.Clear()
        dicPlainteEntente = Nothing

        dicPlainte.Clear()
        dicPlainte = Nothing

        lstPlainte.Clear()
        lstPlainte = Nothing

        dicCmdPal_Req.Clear()
        dicCmdPal_Req = Nothing

        dicCmdPal_Itm.Clear()
        dicCmdPal_Itm = Nothing

        dicCmdPal_Cmd.Clear()
        dicCmdPal_Cmd = Nothing

        lstCliHis.Clear()
        lstCliHis = Nothing

        lstVendeur_Histo.Clear()
        lstVendeur_Histo = Nothing

        lstStdCatScg.Clear()
        lstStdCatScg = Nothing

        lstStdTaux.Clear()
        lstStdTaux = Nothing

        dicStdPvCou.Clear()
        dicStdPvCou = Nothing

        lstStdPVCou.Clear()
        lstStdPVCou = Nothing

        dicFactureItem_Fac.Clear()
        dicFactureItem_Fac = Nothing

        dicFactureItem.Clear()
        dicFactureItem = Nothing

        lstFactureItem.Clear()
        lstFactureItem = Nothing

        dicFactureMensuel.Clear()
        dicFactureMensuel = Nothing

        lstFactureMensuel.Clear()
        lstFactureMensuel = Nothing

        dicFactureCmd.Clear()
        dicFactureCmd = Nothing

        dicFacture.Clear()
        dicFacture = Nothing

        lstFacture.Clear()
        lstFacture = Nothing

        dicCmdMac_Cmd.Clear()
        dicCmdMac_Cmd = Nothing

        lstSVG.Clear()
        lstSVG = Nothing

        lstCM.Clear()
        lstCM = Nothing

        scrap_rouleau.Clear()
        scrap_rouleau = Nothing

        scrap.Clear()
        scrap = Nothing

        rouleaux.Clear()
        rouleaux = Nothing

        dicTypeRejetDispo.Clear()
        dicTypeRejetDispo = Nothing

        dicStdProduit.Clear()
        dicStdProduit = Nothing

        dicStdProduitClient.Clear()
        dicStdProduitClient = Nothing

        dicStatutFact.Clear()
        dicStatutFact = Nothing

        dicStatutExped.Clear()
        dicStatutExped = Nothing

        dicStatut.Clear()
        dicStatut = Nothing

        dicSecteur.Clear()
        dicSecteur = Nothing

        dicDevise.Clear()
        dicDevise = Nothing

        dicForme.Clear()
        dicForme = Nothing

        dicTerritoire.Clear()
        dicTerritoire = Nothing

        dicProduitClient.Clear()
        dicProduitClient = Nothing

        dicProduitCateg.Clear()
        dicProduitCateg = Nothing

        dicMarche.Clear()
        dicMarche = Nothing

        dicClasse.Clear()
        dicClasse = Nothing

        dicClient.Clear()
        dicClient = Nothing

        dicVendeur.Clear()
        dicVendeur = Nothing

        dicMachine.Clear()
        dicMachine = Nothing

        dicCmdMacMult.Clear()
        dicCmdMacMult = Nothing

        dicTauxChange.Clear()
        dicTauxChange = Nothing

        dicStdRsnHis.Clear()
        dicStdRsnHis = Nothing

        dicRsnCouHis.Clear()
        dicRsnCouHis = Nothing

        dicCmdStr.Clear()
        dicCmdStr = Nothing

        dicProduit.Clear()
        dicProduit = Nothing

        dicPV.Clear()
        dicPV = Nothing

        dicCommande.Clear()
        dicCommande = Nothing

        lstCommande.Clear()
        lstCommande = Nothing

        GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce ' compact the LOH
        GC.Collect()

    End Sub

    Function GetTauxChangeResineScrap(dat As DateTime) As taux_change
        '* Aller chercher le taux de change pour la scrap.
        dat = dat.Date ' ignore time part
        Dim taux As taux_change
        If dat < DAT_CHANGEMENT_CALCUL_SCRAP Then
            taux = GetTauxChange(dat)
        Else
            taux = New taux_change With {.id_taux_change = -1, .datejour = dat, .taux = GetStdTaux(dat)}
        End If
        Return taux
    End Function

    Function GetTauxChange(dat As DateTime) As taux_change
        dat = dat.Date ' ignore time part
        Dim taux As taux_change = Nothing
        Do
            If dicTauxChange.TryGetValue(dat, taux) Then Exit Do
            If dat >= #1998-01-01# Then dat = dat.AddDays(-1) Else taux = New taux_change : Exit Do ' try yesterday...
        Loop
        Return taux
    End Function

    Private ReadOnly hsCoutStrucZero As New HashSet(Of Integer) '-todo- these are problems; check to fix...
    Function GetStructureForDate(structs As List(Of commande_structure), dat As DateTime) As commande_structure
        Dim str As commande_structure = structs.FirstOrDefault
        For Each cs In structs
            If cs.dateheure > dat Then Exit For
            str = cs
        Next
        If str Is Nothing Then str = New commande_structure
        If str.cout_us = 0 OrElse str.taux_us = 0 Then hsCoutStrucZero.Add(str.id_commande_structure)
        Return str
    End Function

    'Function GetStructs(id_commande_machine As Integer, id_commande As Integer, Optional id_machine As Integer = 0) As List(Of commande_structure)
    Function GetStructs(id_commande_machine As Integer) As List(Of commande_structure)
        Dim structs As List(Of commande_structure) = Nothing
        If Not dicCmdStr.TryGetValue(id_commande_machine, structs) Then structs = New List(Of commande_structure)
        'structs = structs.OrderBy(Function(x) x.dateheure).ToList ' see CM 225164 - structs with other CommandeID can be used
        'If structs.Count = 0 Then Debug.Write("")
        Return structs
    End Function

    Function GetResineScrap(id_commande_machine As Integer, id_commande As Integer, dat As DateTime) As Integer ' retourne id_resine_recyclage ou -1 pour rebut.

        Dim key = GetDualIdKey(id_commande_machine, id_commande)

        Dim rsnScrap = 15 ' default -todo- confirm this

        'Dim dat = Now ' si commande_machine sans date de début setup, on prend "maintenant"

        Dim obj = GlobalCache(CStr(key))
        If obj Is Nothing Then

            'Dim pcm As pexCommandeMachine = Nothing
            'If dicPexCM.TryGetValue(key, pcm) Then
            '    dat = pcm.DateDebutSetup.Value
            'End If

            Dim cmd As commande = Nothing
            If Not dicCommande.TryGetValue(id_commande, cmd) Then
                Console.WriteLine($"Pas de commande:   id_commande={id_commande}   id_commande_machine={id_commande_machine}")
                cmd = New commande
            End If

            Dim pv As PV = Nothing
            If dicPV.TryGetValue(cmd.id_pv, pv) Then 'pcm#223343 -> no id_pv in cmd...?

                If dat < #2022-09-01# Then
                    ' Ancien calcul avant table PV_HISTORIQUE_MENSUEL
                    Dim prd = dicProduit(pv.id_produit)
                    Dim couleur = PVCouleur(pv.chaine_pv, prd.sous_marche)
                    rsnScrap = If(couleur = "CLAIR", 15, 16)
                Else
                    '-todo- Modifier pour aller voir la vrai résine recyclée de chacun des cmd_mac_scrap.
                    ' Patch temporaire en attendant. 
                    '-todo- utiliser le id_pv_standard
                    rsnScrap = -1 ' rebut
                    Dim lst_pv_his As List(Of pv_historique_mensuel) = Nothing
                    If dicPVHisMens.TryGetValue(pv.id_pv, lst_pv_his) Then
                        Dim pv_his = lst_pv_his.Where(Function(x) dat >= x.date_debut And dat < x.date_fin).SingleOrDefault
                        If pv_his IsNot Nothing Then
                            rsnScrap = If(pv_his.id_resine_recyclage < 1, -1, pv_his.id_resine_recyclage) ' -1 = rebut
                        End If
                    End If
                End If
            Else
                'Log($"Pas de id_pv:   id_commande_machine={id_commande_machine}   id_pv={cmd.id_pv}   id_commande={id_commande}")
                'Log("G:\commande_machine_sans_id_pv.txt", $"{id_commande_machine}")
            End If

            GlobalCache.Add(CStr(key), rsnScrap, DateTime.Now.AddMinutes(10))

        Else
            rsnScrap = CInt(obj)
        End If

        Return rsnScrap

    End Function

    Function GetResineCoutHistorique(id_resine As Integer, dat As DateTime) As resine_cout_historique
        '* Aller chercher le coût de la résine pour la scrap.

        Dim found As resine_cout_historique = Nothing

        If dat >= DAT_CHANGEMENT_CALCUL_SCRAP Then
            '* Aller chercher le data dans std_resine_historique après une certaine date.  Le calcul du prix de la scrap a changé à ce moment.
            If id_resine > 0 Then
                Dim lst = dicStdRsnHis(id_resine)
                'Debug.Assert(Not (id_resine = 15 And lst.Count = 62 And dat > #2023-10-01#))
                Dim skip = lst.Count \ 2 + 1
                Dim ndx = skip
                Dim STDTauxChange As Decimal

                Do
                    '* Adapter et convertir en resine_cout_historique, pour rester standard dans le reste des calculs.
                    If dat >= lst(ndx).date_debut AndAlso dat <= lst(ndx).date_fin Then
                        STDTauxChange = GetStdTaux(dat)
                        found = New resine_cout_historique With
                            {.id_resine_cout_historique = -1,
                             .id_resine = lst(ndx).id_resine,
                             .cout_can = lst(ndx).cout_liste_us * STDTauxChange, ' bon taux de change? il y en a un dans la table...
                             .cout_us = lst(ndx).cout_liste_us,
                             .taux_change = STDTauxChange,
                             .date_debut = lst(ndx).date_debut,
                             .date_fin = lst(ndx).date_fin}
                        Exit Do
                    End If

                    skip \= 2
                    If skip = 0 Then skip = 1
                    If skip > 1 AndAlso ndx + skip >= lst.Count Then skip = 1

                    Debug.Assert(skip >= 1)

                    If dat < lst(ndx).date_debut Then
                        ndx -= skip ' on avance
                    ElseIf dat > lst(ndx).date_fin Then
                        ndx += skip ' on recule
                    Else
                        Debug.Assert(False)
                    End If

                    Debug.Assert(ndx >= 0)
                    Debug.Assert(ndx < lst.Count)

                Loop

                If found Is Nothing Then
                    Console.WriteLine($"Pas de std resine historique pour #{id_resine} le {dat:yyyy-MM-dd HH:mm:ss}")
                End If
            Else
                '* Pas de résine correspondant à la disposition du rejet
                Return New resine_cout_historique With {.id_resine_cout_historique = -1, .id_resine = id_resine, .cout_can = 0, .cout_us = 0, .taux_change = 1}
            End If
        Else
            If id_resine > 0 Then

                If dat < #2004-01-05# Then Return New resine_cout_historique With {.id_resine_cout_historique = -1, .id_resine = id_resine, .cout_can = 0.63D, .cout_us = 0.63D, .taux_change = 1D} ' couts proviennent de spCoutResine (original)

                If dat < DAT_CHANGEMENT_CALCUL_SCRAP And dat >= #2004-01-05# Then
                    '* Calcul du coût de la scrap reste comme il est de 2004 à 2021-09-01.                    
                    Dim lst = dicRsnCouHis(id_resine)
                    Dim skip = lst.Count \ 2 + 1
                    Dim ndx = skip

                    ' Binary search
                    Do
                        If dat >= lst(ndx).date_debut AndAlso dat <= lst(ndx).date_fin Then found = lst(ndx) : Exit Do

                        skip \= 2
                        If skip = 0 Then skip = 1
                        If skip > 1 AndAlso ndx + skip >= lst.Count Then skip = 1

                        Debug.Assert(skip >= 1)

                        If dat < lst(ndx).date_debut Then
                            ndx -= skip ' on avance
                        ElseIf dat > lst(ndx).date_fin Then
                            ndx += skip ' on recule
                        Else
                            Debug.Assert(False)
                        End If

                        Debug.Assert(ndx >= 0)
                        Debug.Assert(ndx < lst.Count)

                    Loop

                    If found Is Nothing Then
                        If dat >= #2004-01-01# Then Console.WriteLine($"Pas de cout historique pour #{id_resine} le {dat:yyyy-MM-dd HH:mm:ss}")
                    End If
                End If
            Else
                Return New resine_cout_historique With {.id_resine_cout_historique = -1, .id_resine = id_resine, .cout_can = 0.63D, .cout_us = 0D, .taux_change = 1D}
            End If
        End If


        Return found

    End Function
    Function GetStdRecette(idPV As Integer, dat As Date?) As List(Of std_pv_resine_historique)
        lstStdPvRsnHis = GetData(Of std_pv_resine_historique)(ppdb, "", cachePath, bForceReadFromDB)

        Dim stdPvRsnHis = lstStdPvRsnHis.Where(Function(x) idPV = x.id_std_pv_resine _
                                              AndAlso CBool(dat >= x.date_debut AndAlso dat <= x.date_fin)
                                              ).ToList '
        Return stdPvRsnHis
    End Function
    Function GetStdCoutResine(idResine As Integer, pourc As Double, dat As Date?) As Double
        Dim lstStdRsnHis = GetData(Of std_resine_historique)(ppdb, "", cachePath, bForceReadFromDB)
        Dim stdCoutRsnHis = lstStdRsnHis.Where(Function(x) idResine = x.id_resine _
                                             AndAlso CBool(dat >= x.date_debut AndAlso dat <= x.date_fin)
                                             ).FirstOrDefault '
        Return stdCoutRsnHis.cout_us * pourc
    End Function
    Function GetStdCoutTypResine(idResine As Integer, pourc As Double, dat As Date?) As Double
        Dim lstStdTypRsnHis = GetData(Of resine_type_historique)(ppdb, "", cachePath, bForceReadFromDB)
        Dim stdCoutTypRsnHis = lstStdTypRsnHis.Where(Function(x) idResine = x.id_resine_type _
                                             AndAlso CBool(dat >= x.date_debut AndAlso dat <= x.date_fin)
                                             ).FirstOrDefault '
        Return stdCoutTypRsnHis.cout_us_std * pourc
    End Function
    Function GetStdCoutRecette(idPV As Integer, dat As Date?) As Double
        Dim lstStdRsnRecette As List(Of std_pv_resine_historique) = GetStdRecette(idPV, dat)
        Dim stdCoutResine As Double = 0

        For Each stdResine As std_pv_resine_historique In lstStdRsnRecette
            If (stdResine.id_resine >= 0) Then
                stdCoutResine += GetStdCoutResine(stdResine.id_resine, stdResine.pourc_resine, dat)
            Else
                stdCoutResine += GetStdCoutTypResine(stdResine.id_resine, stdResine.pourc_resine, dat)
            End If
        Next

        Return stdCoutResine
    End Function
    Function PVCouleur(ChainePV As String, SousMarche As String) As String

        ChainePV = ChainePV.ToUpperInvariant
        SousMarche = ("" & SousMarche).ToUpperInvariant

        Dim res = ""

        If Not SousMarche.Contains("MULCH") Then
            For Each clr In {"BLUE", "YELLOW", "GREY", "GRAY", "BEIGE", "GOLD", "IVORY", "RED", "GREEN", "ORANGE", "VIOLET",
                             "BROWN", "ROSE", "PINK", "SILVER", "CHARCOAL", "MAGENTA", "AMBER", "OLIVE", "MAUVE", "TEAL",
                             "BURGANDY", "BURGUNDY", "BUFFDARK", "COULEUR", "LAVENDER"}
                If ChainePV.Contains(clr) Then res = "COULEUR" : Exit For
            Next
        End If
        If res = "" Then
            If SousMarche.Contains("MULCH") AndAlso Not ChainePV.Contains("PEX") AndAlso Not ChainePV.Contains("DPX") Then
                res = "CLAIR"
            ElseIf ChainePV.Contains("BLACK") OrElse ChainePV.Contains("BLK") Then
                res = "NOIR"
            ElseIf ChainePV.Contains("WHITE") OrElse ChainePV.Contains("BLANC") Then
                res = "BLANC"
            ElseIf ChainePV.Contains("CLEAR") OrElse ChainePV.Contains("NATUREL") Then
                res = "CLAIR"
            Else
                res = "CLAIR"
            End If
        End If

        Return res

    End Function

    Function GetPoidsLb(unite As String, quantite As Decimal, longueur As Decimal, poids As Decimal, poidsRoulCalcule As Decimal, codeProduit As String) As Decimal

        'If ("" & codeProduit).ToUpperInvariant = "SURCHARGE" Then Return 0D
        If PRD_EXCLUS_POUR_CALCUL_POIDS.Contains(("" & codeProduit).ToUpperInvariant) Then Return 0D

        Select Case ("" & unite).ToUpperInvariant
            Case "LBS"
                Return quantite
            Case "KG"
                Return Math.Round(quantite * 2.2046D, 4, MidpointRounding.AwayFromZero)
            Case "PIEDS"
                Return Math.Round(If(longueur = 0, 0D, poids / longueur) * quantite, 4, MidpointRounding.AwayFromZero)
            Case "METRES"
                Return Math.Round(If(longueur = 0, 0D, poids / longueur) * quantite * 0.3049D, 4, MidpointRounding.AwayFromZero)
            Case "ROL"
                Return Math.Round(quantite * poidsRoulCalcule, 4, MidpointRounding.AwayFromZero)
                'Case "AUTRE"
                '    Return quantite
        End Select

        Return 0D

    End Function

    Function GetPrixLb(unite As String, prix As Decimal, longueur As Decimal, poids As Decimal, poidsRoulCalcule As Decimal, codeProduit As String) As Decimal

        If PRD_EXCLUS_POUR_CALCUL_COUT.Contains(("" & codeProduit).ToUpperInvariant) Then Return 0D

        Dim res = 0D
        Select Case ("" & unite).ToUpperInvariant
            Case "LBS"
                res = prix
            Case "KG"
                res = prix / 2.2046D
            Case "PIEDS"
                res = If(longueur = 0, 0, prix / (poids / longueur))
            Case "METRES"
                res = If(longueur = 0, 0, prix / (poids / longueur) * 0.3049D)
            Case "ROL"
                res = If(poidsRoulCalcule = 0, 0, prix / poidsRoulCalcule)
                'Case "AUTRE"
                '    res = 0 'prix
        End Select

        Return Trunc(res, 4) ' moins de débalancements que sans le Trunc(), mais pas 100% de concordance.

    End Function

    Function GetVarGlob(dat As DateTime?) As std_variable_globale
        Static def As New std_variable_globale
        Dim svg = lstSVG.Where(Function(x) CBool(dat >= x.date_debut AndAlso dat <= x.date_fin)).SingleOrDefault
        Return If(svg, def)
    End Function

    Function GetClientHistorique(idClient As Integer, dat As DateTime?, Optional rabais As Boolean = False) As client_historique

        ' note: this funtion eats about 20% of the runtime... it is very slow.

#If False Then
        ' possible optimization; need to test to make sure it returns the same thing as the original        
        Dim start = FindItemPosition(lstCliHis, idClient)
        If start = -1 Then Return New client_historique ' not found

        For i = start To lstCliHis.Count - 1
            Dim cli = lstCliHis(i)
            If cli.id_client = idClient Then
                If rabais Then
                    If cli.rabais_actif = rabais AndAlso dat >= cli.date_debut AndAlso dat < cli.date_fin Then
                        Return cli
                    End If
                Else
                    If dat >= cli.date_debut AndAlso dat < cli.date_fin Then
                        Return cli
                    End If
                End If
            Else
                Exit For ' no more entry for this client
            End If
        Next
        Return New client_historique ' default
#Else
        Static def As New client_historique
        If Not dat.HasValue Then Return def
        Dim dv = dat.Value
        Dim cliHis As client_historique
        If rabais Then
            cliHis = lstCliHis.Where(Function(x) x.id_client = idClient AndAlso x.rabais_actif = True AndAlso dv >= x.date_debut AndAlso dv <= x.date_fin).SingleOrDefault
        Else
            cliHis = lstCliHis.Where(Function(x) x.id_client = idClient AndAlso dv >= x.date_debut AndAlso dv <= x.date_fin).SingleOrDefault
        End If
        Return If(cliHis, def)
#End If

    End Function

    Function GetStdTaux(dat As Date?) As Decimal
        If Not dat.HasValue Then
            Return 0
        End If
        Dim stdTaux = lstStdTaux.Where(Function(x) CBool(dat >= x.date_debut AndAlso dat <= x.date_fin)).SingleOrDefault
        Return If(stdTaux Is Nothing, 1D, stdTaux.taux) '-todo- default taux of 1... ok?
    End Function

    Public Function Calculer_Vendeur_Taux_Commission(VendeurID As Integer, dat As Date?, PrixVente_BrutMontantLbs As Decimal, calculDevise As Func(Of Decimal, Decimal, Decimal), tauxDevise As Decimal, id_devise_demandee As Integer) As Decimal
        If dat Is Nothing OrElse VendeurID = 0 OrElse PrixVente_BrutMontantLbs = 0 Then
            Return 0
        End If

        Dim vendeur = lstVendeur_Histo.Where(Function(x) CBool(x.ID_VENDEUR = VendeurID AndAlso dat >= x.date_debut AndAlso dat <= x.date_fin)).SingleOrDefault

        If vendeur Is Nothing Then
            Return 0
        End If
        'si le taux de commision cents  est a rien, alors on prends celui des taux_commission_cents
        If If(vendeur.TAUX_COMMISSION_LBS, 0) <> 0 Then
            If vendeur.ID_DEVISE_TAUX_COMMISSION_LBS = id_devise_demandee Then
                Return vendeur.TAUX_COMMISSION_LBS.Value
            End If
            Return calculDevise(If(vendeur.TAUX_COMMISSION_LBS, 0), tauxDevise)
        End If

        Return If(vendeur.TAUX_COMMISSION, 0) * PrixVente_BrutMontantLbs

    End Function

    Public Function GetVendeurFromCommande(CommandeID As Integer) As Integer
        Dim uneCmd As commande = Nothing
        If dicCommande.TryGetValue(CommandeID, uneCmd) Then
            Return uneCmd.id_vendeur
        End If
        Return 0
    End Function

    Public Function Calculer_Client_Redevance(ClientID As Integer, dat As Date?, PrixVente_BrutMontantLbs As Decimal, calculDevise As Func(Of Decimal, Decimal, Decimal), tauxDevise As Decimal, id_devise_demandee As Integer) As Decimal
        If dat Is Nothing OrElse ClientID = 0 OrElse PrixVente_BrutMontantLbs = 0 Then
            Return 0
        End If

        Dim unClient = lstCliHis.Where(Function(x) CBool(x.id_client = ClientID AndAlso dat >= x.date_debut AndAlso dat <= x.date_fin)).SingleOrDefault
        If unClient Is Nothing Then
            Return 0
        End If

        'si le taux de commision est a rien, alors on prends celui des taux_commission_cents
        If If(unClient.taux_commission_lbs, 0) <> 0 Then
            If unClient.ID_DEVISE_TAUX_COMMISSION_LBS = id_devise_demandee Then
                Return unClient.taux_commission_lbs.Value
            End If
            Return calculDevise(If(unClient.taux_commission_lbs, 0), tauxDevise)
        End If

        Return If(unClient.taux_commission, 0) * PrixVente_BrutMontantLbs
    End Function

    Public Function GetEscompteTarifFromCommande(CommandeID As Integer) As Decimal
        Dim uneCmd As commande = Nothing
        If dicCommande.TryGetValue(CommandeID, uneCmd) Then
            Return uneCmd.escompte_liste_prix
        End If
        Return 0
    End Function


    Function GetStdCatSurcharge(idCat As Integer, qte As Integer, dat As Date?) As std_categorie_surcharge
        Static def As New std_categorie_surcharge
        Dim catSurch = lstStdCatScg.Where(Function(x) idCat = x.id_std_categorie _
                                              AndAlso CBool(dat >= x.date_debut AndAlso dat <= x.date_fin) _
                                              AndAlso qte >= x.quantite_min AndAlso qte <= x.quantite_max
                                              ).FirstOrDefault ' -todo- fix table, qte_max = 1899 should be 1799
        Return If(catSurch, def)
    End Function

    Function GetStdPvCout(pv As String, dat As Date?) As std_pv_cout
        Static def As New std_pv_cout
        Dim lst As List(Of std_pv_cout) = Nothing
        If dicStdPvCou.TryGetValue(("" & pv).TrimEnd.ToUpperInvariant, lst) Then ' "" & ... to get rid of nothing
            Dim stdPvCout = lst.Where(Function(x) CBool(dat >= x.date_debut AndAlso dat <= x.date_fin)).SingleOrDefault
            Return If(stdPvCout, def)
        End If
        Return def
    End Function

    Function GetStdCoutTransportSecteur(id_secteur As Integer, dat As DateTime?) As std_cout_transport_secteur
        Static def As New std_cout_transport_secteur
        If dat Is Nothing Then Return def
        Dim lst As List(Of std_cout_transport_secteur) = Nothing
        If dicStdCoutTransportSecteur.TryGetValue(id_secteur, lst) Then
            Dim stdCTS = lst.Where(Function(x) x.id_transport_moyen = 0 AndAlso CBool(dat >= x.date_cout AndAlso dat < x.date_cout.AddMonths(1))).SingleOrDefault ' -todo- optimize date_fin...
            Return If(stdCTS, def)
        End If
        Return def
    End Function

    Function GetStdCoutTransportAdresse(id_adresse As Integer, dat As DateTime?) As std_cout_transport_adresse
        Static def As New std_cout_transport_adresse
        If dat Is Nothing Then Return def
        Dim lst As List(Of std_cout_transport_adresse) = Nothing
        If dicStdCoutTransportAdresse.TryGetValue(id_adresse, lst) Then
            Dim stdCTA = lst.Where(Function(x) x.id_transport_moyen = 0 AndAlso CBool(dat >= x.date_cout AndAlso dat < x.date_cout.AddMonths(1))).SingleOrDefault ' -todo- optimize date_fin...
            Return If(stdCTA, def)
        End If
        Return def
    End Function

    Function GetAdresseClientHistorique(id_cli_adr As DualID, dat As DateTime?) As adresse_client_historique
        Static def As New adresse_client_historique
        If dat Is Nothing Then Return def
        Dim lst As List(Of adresse_client_historique) = Nothing
        If dicAdresseClientHistorique.TryGetValue(id_cli_adr, lst) Then
            Dim stdACH = lst.Where(Function(x) CBool(dat >= x.date_debut AndAlso dat <= x.date_fin)).SingleOrDefault
            Return If(stdACH, def)
        End If
        Return def
    End Function

    Function GetListeSecteurHistorique(id_secteur As Integer, dat As DateTime) As ListeSecteurHistorique
        Static def As New ListeSecteurHistorique
        Dim lst As List(Of ListeSecteurHistorique) = Nothing
        If dicLSH.TryGetValue(id_secteur, lst) Then
            Dim lsh = lst.Where(Function(x) dat >= x.date_debut AndAlso dat <= x.date_fin).SingleOrDefault
            Return If(lsh, def)
        End If
        Return def
    End Function

    Function DataOK() As Boolean
        Dim bOK As Boolean
        bOK = True

        If dicTauxChange.Count = 0 Then
            bOK = False
            Log($"Les taux de change n'ont pas pu être récupérés.", color:=ConsoleColor.Red)
            SendEmail("", "PolyExpertReport - Aucun taux de change", "Les taux de change n'ont pas pu être récupérés.  Le processus est interrompu.",, EmailToError)
        End If

        Dim scrap_prob = scrap.Where(Function(x) x.id_type_rejet_dispo = 0 AndAlso x.date_scrap > Convert.ToDateTime("2012-01-01")).Count
        Dim scrap_roul_prob = scrap_rouleau.Where(Function(x) x.id_type_rejet_dispo = 0 AndAlso x.date_scrap > Convert.ToDateTime("2012-01-01")).Count
        If scrap_prob > 0 Or scrap_roul_prob > 0 Then
            bOK = False
            Log($"Certains rejets ont une disposition invalide.", color:=ConsoleColor.Red)
            SendEmail("", "PolyExpertReport - Rejets avec disposition invalide", "Certains rejets ont une disposition invalide.  Le processus est interrompu.",, EmailToError)
        End If

        Return bOK
    End Function

End Module


