﻿<PetaPoco.PrimaryKey("id_commande_recette")>
Partial Public Class commande_recette
    Public Property id_commande_recette As Integer
    Public Property id_commande_structure As Integer?
    Public Property id_recette As Integer?
    Public Property nom As String
    '	Public Property com_interne As String
    '	Public Property com_production As String
    Public Property ratio As Decimal?
    Public Property ordre As Integer?
    '	Public Property feed As Boolean
    '	Public Property densite As Decimal?
    Public Property cout As Decimal
    Public Property prix_lbs_us_normalise As Decimal
    Public Property note As Decimal?
    '	Public Property gravitrol As Decimal?
End Class