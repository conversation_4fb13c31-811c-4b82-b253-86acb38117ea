﻿Imports System.IO
Imports System.Threading
Imports NetMQ
Imports NetMQ.Sockets
Imports ProtoBuf

Public Module utilsNetMQ

    Private Subscribed As Boolean = False

    Public LAST_DB_CHANGE_TRACKING_MESSAGE As DateTime

    Public Event DBChangeTracking(databaseID As Short, objectID As Integer) '-todo- mecanisme pour seulement faire des "raise events" pour les tables qui nous intéressent

    Public Sub SubscribeToTracker(url As String, port As Integer)

        If Subscribed Then Exit Sub ' don't subscribe more than once...

        Dim client = New Thread(Sub() Subscriber(url, port))
        client.IsBackground = True ' don't prevent app from closing
        client.Start()

        Subscribed = True

    End Sub

    Public Sub UnsubscribeFromTracker()
        Subscribed = False
    End Sub

    Private Sub Subscriber(url As String, port As Integer)

        Using subSocket = New SubscriberSocket()

            subSocket.Options.ReceiveHighWatermark = 100 '-todo- app parameters            
            subSocket.Connect($"tcp://{url}:{port}") '-todo- app parameters
            subSocket.Subscribe("") ' all topics

            Debug.Print($"Subscriber (thread id {Thread.CurrentThread.ManagedThreadId}) socket connecting...")

            Dim lastModified As DateTime = #1753-01-01 00:00:00#

            Do
                Dim bytes = subSocket.ReceiveFrameBytes
                If Not Subscribed Then Exit Do
                'If Not InitCompleted Then Continue Do ' wait until initial read completed before starting... -todo- queue changes for later?
                LAST_DB_CHANGE_TRACKING_MESSAGE = Now
                Using ms = New MemoryStream(bytes)
                    Dim chg = ProtoBuf.Serializer.Deserialize(Of DBChangeInfo)(ms)
                    If chg.last_user_update >= lastModified Then
                        'Debug.Print($"Subscriber: {chg.database_id}.{chg.object_id}:{chg.last_user_update} ({bytes.Length} bytes)")
                        RaiseEvent DBChangeTracking(chg.database_id, chg.object_id)
                        lastModified = chg.last_user_update
                    End If
                End Using
            Loop While Subscribed

        End Using

    End Sub

End Module

<ProtoContract>
Class DBChangeInfo
    <ProtoMember(1)> Property database_id As Short
    <ProtoMember(2)> Property object_id As Integer
    <ProtoMember(3)> Property last_user_update As DateTime
End Class