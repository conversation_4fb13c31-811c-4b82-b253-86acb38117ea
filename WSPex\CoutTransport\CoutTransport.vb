﻿Imports System.ComponentModel
Imports System.Data.SqlClient
Imports System.Threading
Imports ProtoBuf
Imports Utilities

' NOTE: optimisation possible: ne pas mettre dans les tables les entrées client / adresse avant qu'il n'y ai d'activité transport sur ceux-ci (selon std_cout_Transport_palette)
'       - evite de rebatir tout le passé a chaque ajout client

Module CoutTransport

    ' Q1: Les couts transport dans ListeSecteurHistorique sont en CDN$? ou devise du secteur? (R1: En $Canadiens)
    ' Q2: Le champ "rabais_montant" dans CLIENT_HISTORIQUE est-il dans la devise du client?

    ' nouvelles règles

    ' [ ] nombre adresse configurable (actuel = 10)
    ' source configurable - actuel adresse client, si pas 10, alors secteur pondéré, sinon listeSecteurHistorique
    '           - trouver ou ce calcul est fait.
    ' [X] cout pondéré "a utiliser" on round up à la cenne près. (champs à part) (round up est configurable (cenne, demi-cenne, etc.)
    ' [X] tables: ajout colonnes "stats" - total lb

    '================================================================================
    ' [X] TODO: nombre_palette -> changer MONEY pour INTEGER	

    ' Règles
    ' 1) le cout au 1er du  mois est basé sur les 12 mois précédents. (ex: cout du 2022-12-01 basé sur les réquisitions du 2021-11-01 à 2021-11-30 23:59:59)
    ' 2) les adresses ou moins de 10 livraisons ont eu lieu ont le cout de transport du secteur
    ' 3) si on a 10+ livraisons, on exclu du calcul celles qui sont hors de 2 écarts types plus élevées. (si en dessous, on les garde...)
    ' 4) si pas assez d'infos pour une adresse, on va au secteur; si pas assez d'infos pour le secteur, on utilise le cout manuel de listeSecteurHistorique (maintenu par ... ?)
    ' 4a) si pas assez d'infos pour un client, on va au secteur de l'adresse principale.
    ' 5) cout_transport_pondere = cout transport après "filtres" appliqués (min 10, std dev * 2) ... cout_transport = cout_transport_pondere + <autres couts selon formule>

    'Taux de change (tauxChg, tau_chg, taux_change)
    'cout_can = cout_us * taux_change(math.round(x, 4))
    'cout_us = cout_can / taux_change(math.round(x, 4))
    'taux_change = cout_can / cout_us(math.round(x, 4))
    '- On garde 4 décimales dans les calculs avec le taux_change. <== important!!

    ' TODO
    ' 3) formule "dynamique" qui peut êter modifié dans le temps - historique
    ' 4) upcharge historique

    ' Note meeting - 2023-01-16:

    ' NOTE: prix_vente_inclus_transport = true -> (Ancienne facon); = false -> Nouvelle facon
    ' nouvelle facon: pas de rabais (pas de transport = pas de rabais transport)

    ' [_] Calculer rabais_client (pour variable client) au pro-rata du rabais appliqué... sur rabais du mois actuel (en calcul)

    ' std:pexRpt - variable client - transport calculé; revenu  meme montant pour que les 2 balancent
    ' var_cli - cout_trs 'facture' (+ arrondi)
    ' var_cli - ajout entrepot externe... a voir comment calculer... (ajout variable_client_std)

    ' [_] todo: document transport - documentation des règles.

    '-------------------------------------------------------------------------------------------------
    '
    ' modif suite au meeting 2023-01-25: ajout de la notion "moyen de transport" afin de distinguer les envois intermodaux du camiomage régulier...

    Private lstTaux As List(Of std_taux)
    Private lstSVG As List(Of std_variable_globale)
    Private lstLSH As List(Of listeSecteurHistorique)
    Private lstCli As List(Of client)

    Private lstCliHis As List(Of client_historique) '-todo- make dic
    Private dicCliHis As Dictionary(Of Integer, List(Of client_historique))

    Private lstAdrHis As List(Of adresse_client_historique) '-todo- make dic
    Private dicAdrHisA As Dictionary(Of Integer, List(Of adresse_client_historique)) ' id_adresse
    Private dicAdrHisC As Dictionary(Of Integer, List(Of adresse_client_historique)) ' id_client

    Private lstTrs As List(Of transport)

    Private allSecID As List(Of Integer)
    Private allAdrID As List(Of Integer)
    Private allCliID As List(Of Integer)

    Private lstParams As List(Of std_cout_transport_parametre)
    Private lstParamOps As List(Of std_cout_transport_parametre_operation)

    Private _ppdb As PetaPoco.Database

#If DEBUG Then
    Private Const UPDATE_DB = True
#Else
    Private Const UPDATE_DB = true
#End If

    Function CalculCoutTransport() As String ' retourne OK ou ERROR

        'Dim log = New Utilities.Logger(UseFile:=True, UseDB:=False, UseEmail:=False, IsProduction:=True, "")

        Dim logName = IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, $"CoutTransport_{Now.ToString("dd")}.log")
        Dim lf = New LogFile(logName)
        lf.Clear()

        _ppdb = GetDB()

        Try

            lf.Log($"CalculCoutTransport - START ---------------------------------------------------------")

            If UPDATE_DB Then
                Dim threadDBUpdater = New Thread(Sub() MonitorQueues(lf))
                threadDBUpdater.IsBackground = True
                threadDBUpdater.Start()
            End If

            lf.Log($"Reading from DB: {_ppdb.GetConnectionString.RemoveToken("password", ";"c).RemoveToken("User ID", ";"c).RemoveToken("app", ";"c)}")

#If DEBUG Then
        Dim cutOff = #2004-01-01# ' mettre un an de moins que la date de début désirée (on prend les 12 derniers mois...)
        If {3, 18}.Contains(Now.Date.Day) Then
            cutOff = #2004-01-01#
        Else
            cutOff = New Date(Now.Year - 3, 1, 1)
        End If
#Else
            Dim cutOff = #2004-01-01#
            If {3, 18}.Contains(Now.Date.Day) Then
                cutOff = #2004-01-01#
            Else
                cutOff = New Date(Now.Year - 3, 1, 1)
            End If
#End If

            lstTaux = _ppdb.Fetch(Of std_taux)("")
            lstSVG = _ppdb.Fetch(Of std_variable_globale)("")

            Dim sql = <a><![CDATA[
select
  ListeSecteurHistoriqueID
 ,id_secteur
 ,date_debut
 ,date_fin
 ,coalesce(cout_transport,        9999.99) cout_transport
 ,coalesce(cout_transport_camion, 9999.99) cout_transport_camion
 ,coalesce(cout_transport_train,  9999.99) cout_transport_train
 ,coalesce(cout_transport_bateau, 9999.99) cout_transport_bateau
 ,coalesce(cout_transport_avion,  9999.99) cout_transport_avion
 ,douane_assurance
from LISTESECTEURHISTORIQUE 
]]></a>.Value.Trim
            lstLSH = _ppdb.Fetch(Of listeSecteurHistorique)(sql)
            lstCli = _ppdb.Fetch(Of client)("")

            lstCliHis = _ppdb.Fetch(Of client_historique)("")
            dicCliHis = lstCliHis.GroupBy(Function(x) x.id_client).ToDictionary(Function(k) k.Key, Function(v) v.ToList)

            lstAdrHis = _ppdb.Fetch(Of adresse_client_historique)("")
            dicAdrHisA = lstAdrHis.GroupBy(Function(x) x.id_adresse).ToDictionary(Function(k) k.Key, Function(v) v.ToList)
            dicAdrHisC = lstAdrHis.GroupBy(Function(x) x.id_client).ToDictionary(Function(k) k.Key, Function(v) v.ToList)

            lstTrs = _ppdb.Fetch(Of transport)("")

            allSecID = _ppdb.Fetch(Of Integer)("select distinct id_secteur from listeSecteurHistorique order by id_secteur")
            allAdrID = _ppdb.Fetch(Of Integer)("select id_adresse from adresse_client order by id_adresse")
            allCliID = _ppdb.Fetch(Of Integer)("select id_client from client order by id_client")

            sql = <a><![CDATA[

select 
 req.id_requisition_transport
,req.id_transport_moyen 
,min(req.date_exped) date_exped
,min(req.id_devise) id_devise
,max(stx.taux) std_taux
,max(req.montant) montant
,sum(case when cmd.UNITE_FACTURATION = 'Rouleaux' then pal.NB_ROULEAU * cmd.POIDS_ROULEAU_CAL else pal.poids_net end) poids_net_cmd_adr  -- '-alex-'
,adr.id_adresse
,min(cli.id_client) id_client
,min(sec.id_secteur) id_secteur
,min(adr.nom) nom_adresse
,min(cli.nom_tri) nom_client
,min(sec.nom) nom_secteur
,count(*) nombre_palette

from requisition_transport req
left join commande_palette      pal on pal.id_requisition_transport = req.id_requisition_transport
left join commande              cmd on cmd.id_commande     = pal.id_commande
left join client                cli on cli.id_client       = cmd.id_client
left join adresse_client        adr on adr.id_adresse      = cmd.id_adresse
left join listesecteur          sec on sec.id_secteur      = adr.id_secteur
left join std_taux              stx on req.date_exped between stx.date_debut and stx.date_fin
    
where req.expedier = 1 
and req.date_exped >= @0
and req.montant > 0

group by req.id_requisition_transport, req.id_transport_moyen, adr.id_adresse

]]></a>.Value.Trim

            lstParams = _ppdb.Fetch(Of std_cout_transport_parametre)("")
            lstParamOps = _ppdb.Fetch(Of std_cout_transport_parametre_operation)("")

            ' cout_std - Partie Cout Standard
            Dim lstPal = GetCoutTransportStandard() ' always use default value of #2004-01-01#... 
            lstPal.RemoveAll(Function(x) x._exp_date_exped < cutOff) ' les palettes non-expédiées ont une date par défaut en 19xx...
            SubmitPalettes(lstPal) ' passer les palettes au dbUpdater pour mise à jour.

            ' Batir dictionnaires pour les 1ère date d'activité id_client et id_adresse (pour éventuelle optimisation)
            ' NOTE: Trouver une autre facon; celle-ci n'est pas bonne pour avoir les chiffres avant la 1ere expédition
#If 0 Then
        Dim dicCliMinDat = lstPal.GroupBy(Function(x) x._exp_id_client).ToDictionary(Function(k) k.Key, Function(v) v.Min(Function(x) x._exp_date_exped))
        Dim dicAdrMinDat = lstPal.GroupBy(Function(x) x._exp_id_adresse).ToDictionary(Function(k) k.Key, Function(v) v.Min(Function(x) x._exp_date_exped))
#End If

            ' 1) Lire données réquisitions, regroupées par requisition/adresse
            Dim lstReqAll = _ppdb.Fetch(Of SommaireRequisition)(sql, cutOff)
            lf.Log("Requisitions lues (drops)")

            ' 2) Calculer le cout/lb pour chaque réquisition
            Dim dicReq = New Dictionary(Of Integer, PoidsMontant) ' key = id_requisition
            For Each row In lstReqAll
                If dicReq.ContainsKey(row.id_requisition_transport) Then
                    dicReq(row.id_requisition_transport).PoidsNetLb += row.poids_net_cmd_adr
                    dicReq(row.id_requisition_transport).Requisitions.Add(row)
                Else
                    Dim itm = New PoidsMontant With {.Type = "req", .ID = row.id_requisition_transport, .Cout = row.montant, .PoidsNetLb = row.poids_net_cmd_adr}
                    itm.Requisitions.Add(row)
                    dicReq(row.id_requisition_transport) = itm
                End If
            Next
            lf.Log("Requisitions cout réparti")

            Dim datDeb = cutOff
            Dim datCout = cutOff.AddYears(1)
            Dim datFin = datDeb
            Dim palDeb = datDeb ' pour insertion des palettes

            Dim curYear = 0

            Do
                ' Processus de calcul mois par mois

                Dim params = lstParams.Where(Function(x) datCout >= x.date_debut AndAlso datCout < x.date_fin).ToList
                Debug.Assert(params.Count = 3) ' sec/adr/cli

                If Year(datCout) <> curYear Then
                    lf.Log($"Calcul année {datCout:yyyy}")
                    curYear = Year(datCout)
                End If
                datDeb = datCout.AddMonths(-12)
                datFin = datCout

                ' pour une periode...
                Dim lstReq = lstReqAll.Where(Function(x) x.date_exped >= datDeb AndAlso x.date_exped < datFin).ToList

                ' 3) Passer au travers des réquisitions et calculer les totaux par adresse, client et secteur.
                Dim dicAdr = New Dictionary(Of Integer, PoidsMontant)
                Dim dicCli = New Dictionary(Of Integer, PoidsMontant)
                Dim dicSec = New Dictionary(Of Integer, PoidsMontant)

                For Each row In lstReq

                    Dim req = dicReq(row.id_requisition_transport) ' avoir le cout/lb pour cette réquisition

                    If Not (row.cout_lb = -1 Or row.cout_lb = req.CoutLb) Then Debug.Assert(False) ' check			
                    row.cout_lb = req.CoutLb ' en profiter pour mettre a jour le cout/lb de cette requisition

                    UpdateDictionary(dicAdr, "adr", row, req, row.id_adresse, row.nom_adresse)
                    UpdateDictionary(dicCli, "cli", row, req, row.id_client, row.nom_client)
                    UpdateDictionary(dicSec, "sec", row, req, row.id_secteur, row.nom_secteur)

                Next

                Dim taux = lstTaux.Where(Function(x) datCout >= x.date_debut AndAlso datCout < x.date_fin).Single.taux

                For i = 0 To 4 ' moyen de transport

                    Dim id_moyen_transport = i

                    ' palettes des 12 derniers mois
                    Dim lstPalPer = lstPal.Where(Function(x) x._exp_date_exped >= datDeb _
                                                 AndAlso x._exp_date_exped < datFin _
                                                 AndAlso (id_moyen_transport = 0 OrElse
                                                          id_moyen_transport = x._id_req_moy_trs)).ToList ' si 0, on veut toutes les palettes
                    ' Do Secteurs
                    Dim lstSec = ProcessSecteur(datCout, id_moyen_transport, dicSec, taux, params)
                    If UPDATE_DB Then DBUpdater.q_Secteur.Enqueue((datCout, id_moyen_transport, lstSec.ToList))

                    ' Do Adresses
                    Dim lstAdr As List(Of std_cout_transport_adresse) = Nothing

                    ' nouvelle méthode avec adresses client regroupées par secteur
                    sql = <a><![CDATA[
select 
  ach.id_client
, ach.id_secteur
, ach.id_adresse
--, cast(coalesce(ch.prix_vente_inclut_transport, 1) as bit) as prix_vente_inclut_transport
--, trs.client_paie_transport
, case when cast(coalesce(ch.prix_vente_inclut_transport, 1) as bit) = 0 then 'C'
       else case when trs.client_paie_transport = 1 then 'C' else 'P' 
                 end
       end as qui_paie_transport
from adresse_client_historique ach
left join transport            trs on trs.id_transport = ach.id_transport
left join client_historique     ch on ch.id_client     = ach.id_client 
                               and @0 between ch.date_debut and ch.date_fin
where @0 between ach.date_debut and ach.date_fin
--and ach.id_client > 0
--and ach.id_adresse > 0
--and ach.id_secteur is not null
--and ach.id_transport > 0
]]></a>.Value.Trim

                    Dim lstCliSecAdr = _ppdb.Fetch(Of CliSecAdrTrs)(sql, datCout)
                    Dim grpCliSecTrs = lstCliSecAdr.GroupBy(Function(x) (x.id_client, x.id_secteur, x.qui_paie_transport)) ' grouper sur tuple (client, secteur, qui_paie_transport)
                    lstAdr = ProcessAdresse(datCout, id_moyen_transport, dicAdr, taux, params, lstSec, lstPalPer.ToList, grpCliSecTrs)
#If 0 Then
                ' filtrer adresses selon dicAdrMinDat; garder tout pour les 2 plus récents mois
                If DateDiff(DateInterval.Day, datCout, Now) > 60 Then
                    lstAdr.RemoveAll(Function(x) Not dicAdrMinDat.ContainsKey(x.id_adresse) OrElse x.date_cout < dicAdrMinDat(x.id_adresse))
                Else
                    Debug.Write("")
                End If
#End If
                    If UPDATE_DB Then DBUpdater.q_Adresse.Enqueue((datCout, id_moyen_transport, lstAdr.ToList))

                    ' Do Clients
                    Dim lstCli = ProcessClient(datCout, id_moyen_transport, dicCli, taux, params, lstSec, lstPalPer.ToList)
#If 0 Then
                ' filtrer clients selon dicCliMinDat
                If DateDiff(DateInterval.Day, datCout, Now) > 60 Then
                    lstCli.RemoveAll(Function(x) Not dicCliMinDat.ContainsKey(x.id_client) OrElse x.date_cout < dicCliMinDat(x.id_client))
                Else
                    Debug.Write("")
                End If

#End If
                    If UPDATE_DB Then DBUpdater.q_Client.Enqueue((datCout, id_moyen_transport, lstCli.ToList))

                Next

                ' Proceed to next month
                datCout = datCout.AddMonths(1)

            Loop Until datCout > DateTime.Now

            DBUpdater.StopRequested = True ' stop the monitoring thread when it has completed all its work.

            lstTaux = Nothing
            lstSVG = Nothing
            lstLSH = Nothing
            lstCli = Nothing
            lstCliHis = Nothing
            dicCliHis = Nothing
            lstAdrHis = Nothing
            dicAdrHisA = Nothing
            dicAdrHisC = Nothing
            lstTrs = Nothing

            allSecID = Nothing
            allAdrID = Nothing
            allCliID = Nothing

            lstParams = Nothing
            lstParamOps = Nothing

            lf.Log($"CalculCoutTransport - END -----------------------------------------------------------")

        Catch ex As Exception
            lf.Log($"{ex}")

            Return "ERROR"

        End Try

        Return "OK"

    End Function

    Private Sub SubmitPalettes(lstPal As List(Of commande_palette_work))

        Dim lstDbPal = New List(Of std_cout_transport_palette)
        For Each pal In lstPal
            Dim sp = New std_cout_transport_palette
            sp.id_commande_palette = pal.id_commande_palette
            sp.id_commande = pal.id_commande
            sp.id_requisition_transport = pal._id_req_trs
            sp.id_client = pal._exp_id_client
            sp.id_adresse = pal._exp_id_adresse
            sp.id_secteur = pal._exp_id_secteur
            sp.id_facture = pal._fac_id_facture
            sp.date_expedition = pal._exp_date_exped
            If sp.date_expedition < #1900-01-01# Then sp.date_expedition = #1900-01-01#
            sp.id_transport_moyen = pal._id_req_moy_trs
            sp.requisition_montant_can = pal._fac_req_mnt_can
            sp.prix_vente_inclut_transport = pal._cli_prix_vente_inclut_transport
            sp.client_paie_transport = pal._adr_client_paie_transport
            sp.facture_transport_montant_can = pal._fac_trs_mnt_can
            sp.facture_transport_rabais_can = pal._fac_trs_rbs_can
            sp.facture_unite_produit = pal._fac_unite_prd
            sp.facture_transport_qte_lb = pal._fac_trs_qte_lb
            sp.periode = sp.date_expedition.Year * 100 + sp.date_expedition.Month
            lstDbPal.Add(sp)
        Next

        Dim grp = lstDbPal.GroupBy(Function(x) x.periode)

        For Each g In grp.OrderBy(Function(x) x.Key)
            If UPDATE_DB Then DBUpdater.q_Palette.Enqueue((Nothing, g.Key, g.ToList))
        Next

    End Sub

    Public Function GetCoutTransportStandard(Optional cutOff As DateTime = #2004-01-01#) As List(Of commande_palette_work)

        Dim lstPal = GetData(Of commande_palette_work)(ppdb, "select pal.* from commande_palette pal left join expedition exp on exp.ID_EXPEDITION = pal.ID_EXPEDITION where exp.DATE_EXPED >= @0", cutOff)

        Dim lstCmd = GetData(Of commande)(ppdb, "where id_commande in (select pal.id_commande from commande_palette pal left join expedition exp on exp.ID_EXPEDITION = pal.ID_EXPEDITION where exp.DATE_EXPED >= @0)", cutOff)
        Dim dicCmd = lstCmd.ToDictionary(Function(k) k.id_commande, Function(v) v)

        Dim lstExp = GetData(Of expedition)(ppdb, "where date_exped > @0", cutOff)
        Dim dicExp = lstExp.ToDictionary(Function(k) k.id_expedition, Function(v) v)

        Dim lstReq = GetData(Of requisition_transport)(ppdb, "")
        Dim dicReq = lstReq.ToDictionary(Function(k) k.id_requisition_transport, Function(v) v)

        Dim lstFac = GetData(Of facture)(ppdb, "where date_facturation > @0", cutOff)
        Dim dicFac = lstFac.ToDictionary(Function(k) k.id_facture, Function(v) v)

        Dim lstItm = GetData(Of facture_item)(ppdb, "where id_facture in (select id_facture from facture where date_facturation > @0)", cutOff)
        Dim dicItm = lstItm.ToDictionary(Function(k) k.id_facture_item, Function(v) v)

        Dim lstAdrSec = GetData(Of AdrSec)(ppdb, "select id_adresse, id_secteur from adresse_client")
        Dim dicAdrSec = lstAdrSec.ToDictionary(Function(k) k.id_adresse, Function(v) v.id_secteur)

        Dim lstACH = GetData(Of adresse_client_historique)(ppdb, "where date_fin > @0", cutOff) '-TODO-lundi - on veut mettre les flags dans les palettes; puis modifier le groupement des adresses pour utiliser le data avec les flags qui matchent le mois courant (modif groupement adresse)
        Dim dicACH = lstACH.GroupBy(Function(x) x.id_adresse).ToDictionary(Function(k) k.Key, Function(v) v)

        Dim lstCH = GetData(Of client_historique)(ppdb, "where date_fin > @0", cutOff)
        Dim dicCH = lstCH.GroupBy(Function(x) x.id_client).ToDictionary(Function(k) k.Key, Function(v) v)

        Dim lstTrs = GetData(Of transport)(ppdb, "").ToList

        Dim lstTauxChange = GetData(Of taux_change)(ppdb, "SELECT * FROM taux_change WITH (NOLOCK)", cachePath, bForceReadFromDB)
        Dim dicTC = lstTauxChange.ToDictionary(Function(k) k.datejour, Function(v) v)

        ' Création des dictionnaires de lookup facture/items transport.
        Dim dicItmFac = New Dictionary(Of Integer, Integer) ' maps id_itm to _id_fac
        Dim dicFacTrs = New Dictionary(Of Integer, List(Of Integer)) ' maps fac To 1..N items de transport
        For Each itm In lstItm
            dicItmFac.Add(itm.id_facture_item, itm.id_facture)
            If itm.code_produit = "TRANSPORT" Then
                If dicFacTrs.ContainsKey(itm.id_facture) Then
                    dicFacTrs(itm.id_facture).Add(itm.id_facture_item)
                Else
                    dicFacTrs.Add(itm.id_facture, New List(Of Integer) From {itm.id_facture_item})
                End If
            End If
        Next

        Dim dicFacProd = New Dictionary(Of Integer, FactureProduit)

        ' Regrouper les palettes avec leurs documents associés.
        For Each pal In lstPal
            Dim facID = -1
            If pal.id_facture_item > 0 Then
                If dicItmFac.TryGetValue(pal.id_facture_item, facID) Then

                    pal._fac_id_facture = facID

                    Dim itmProd = dicItm(pal.id_facture_item)

                    Dim facProd As FactureProduit = Nothing
                    If dicFacProd.TryGetValue(facID, facProd) Then
                        facProd.Palettes.Add(pal)
                        facProd.Produits.Add(itmProd)
                    Else
                        facProd = New FactureProduit
                        facProd.Palettes.Add(pal)
                        facProd.Produits.Add(itmProd)
                        facProd.Facture = dicFac(facID)
                        Dim exp = dicExp(pal.id_expedition)
                        facProd.Expeditions.Add(exp)

                        Dim reqID = exp.id_requisition_transport
                        If reqID <= 0 Then reqID = facProd.Palettes.First.id_requisition_transport
                        If dicReq.ContainsKey(reqID) Then facProd.Requisitions.Add(dicReq(reqID))
                        facProd.Commandes.Add(dicCmd(pal.id_commande))
                        ' Vérifier pour frais de transport
                        Dim lstItmTrs As List(Of Integer) = Nothing
                        If dicFacTrs.TryGetValue(facID, lstItmTrs) Then
                            For Each id In lstItmTrs
                                facProd.Transports.Add(dicItm(id))
                            Next
                        End If
                        dicFacProd.Add(facID, facProd)
                    End If

                Else
                    ' Facture item inexistant dans DB... perte de données?
#If DEBUG Then
                    Console.WriteLine($"{pal.id_commande_palette} has non-existant {pal.id_facture_item} item id.")
#End If
                End If
            Else
                ' palette non facturée pour l'instant.
#If DEBUG Then
                Console.WriteLine($"{pal.id_commande_palette} has not been invoiced. Emballage: {pal.date_emballage}. Expedition {pal.id_expedition}")
#End If
            End If
        Next

        For Each kvp In dicFacProd.ToList

            Dim fp = kvp.Value

            If fp.Commandes.Count <> 1 Then Debug.Assert(False)
            If fp.Produits.Count <> 1 Then
                InformDev($"ERREUR: Facture avec plus d'un produit. {fp.Facture.no_facture} (id #{fp.Facture.id_facture}) contient {fp.Produits.Count} produits.")
            End If

            Dim cmd = fp.Commandes.Single
            Dim exp = fp.Expeditions.Single
            Dim fac = fp.Facture
            'Dim prod = fp.Produits.Single
            Dim unite As String
            If fp.Produits.Count = 1 Then
                unite = fp.Produits.Single.unite
            Else
                unite = fp.Produits.First.unite
                ' check that all UNITE are the same, prod is only used for this...
                For Each prd In fp.Produits.Skip(1)
                    If prd.unite <> unite Then Throw New Exception($"Multiple produits sur facture avec unitées différentes. ({fac.no_facture})")
                Next
            End If
            Dim trans = fp.Transports

            Dim ach = dicACH(exp.id_adresse_client).Where(Function(x) exp.date_exped >= x.date_debut AndAlso exp.date_exped < x.date_fin).SingleOrDefault
            If ach Is Nothing Then
                ach = lstACH.Where(Function(x) x.id_adresse = exp.id_adresse_client).OrderBy(Function(x) x.date_debut).First ' on prend le plus ancien record
                'If exp.date_exped >= ach.date_debut Then Debug.Write("") ' si on break ici, notre présomption ne tient pas pour le plus vieux record...
            End If
            'If ach Is Nothing Then Debug.Write("")
            Dim trs = lstTrs.Where(Function(x) x.id_transport = ach.id_transport).SingleOrDefault
            'If trs Is Nothing Then Debug.Write("")
            Dim ch = dicCH(exp.id_client).Where(Function(x) exp.date_exped >= x.date_debut AndAlso exp.date_exped < x.date_fin).SingleOrDefault
            If ch Is Nothing Then
                ' meme logique que ACH ci-haut, on prend le plus ancien record
                ch = lstCH.Where(Function(x) x.id_client = exp.id_client).OrderBy(Function(x) x.date_debut).First ' on prend le plus ancien record
                'If exp.date_exped >= ch.date_debut Then Debug.Write("") ' si on break ici, notre présomption ne tient pas pour le plus vieux record...
            End If
            'If ch Is Nothing Then Debug.Write("") ' ch peut être nul

            ' Calculer le total des frais ou rabais de transport
            Dim montant = 0D
            Dim rabais = 0D

            Dim taux_fac = If(fac.id_devise = 4, fac.taux_echange, 1D)
            If taux_fac = 0 Then
                Dim tc As taux_change = Nothing
                If dicTC.TryGetValue(fac.date_facturation, tc) Then taux_fac = tc.taux
            End If
            If taux_fac = 0 Then taux_fac = 99999D : Debug.Assert(False)

            For Each item In trans

                Select Case ("" & item.unite).ToUpperInvariant

                    Case "", "AUTRE"
                        Dim mnt = R4(item.quantite_expedie * item.prix * taux_fac) 'taux est 1 si la device est $CDN
                        If mnt < 0 Then rabais += mnt Else montant += mnt

                    Case "LBS"
                        Dim mnt = R4(item.quantite_expedie * item.prix * taux_fac) 'taux est 1 si la device est $CDN
                        If mnt < 0 Then rabais += mnt Else montant += mnt

                    Case Else
                        Debug.Assert(False)

                End Select
            Next

            fp.transport_total = montant
            fp.rabais_total = rabais
            Dim idReq = -1
            If fp.Requisitions.Count > 0 Then idReq = fp.Requisitions.Single.id_requisition_transport

            ' Calcul du poids a utiliser - si "ROL" on prend le poids calculé du rouleau dans la commande
            For Each pal In fp.Palettes

                If pal.id_requisition_transport > 0 AndAlso idReq = -1 Then
                    Debug.Assert(False)
                End If

                pal._exp_id_client = exp.id_client
                pal._exp_id_adresse = exp.id_adresse_client
                pal._exp_id_secteur = dicAdrSec(exp.id_adresse_client)
                pal._exp_date_exped = exp.date_exped

                pal._id_req_trs = idReq
                If idReq > 0 Then pal._id_req_moy_trs = fp.Requisitions.Single.id_transport_moyen
                If pal._id_req_moy_trs = 0 Then pal._id_req_moy_trs = 1 ' camion par défaut si on ne sait pas
                pal._fac_unite_prd = unite
                If unite.ToUpperInvariant = "ROL" Then
                    pal._fac_trs_qte_lb += CDec(cmd.poids_rouleau_cal) * pal.nb_rouleau
                Else
                    pal._fac_trs_qte_lb += pal.poids_net '  le poids de palettes est toujours en LB dans le systeme; donc "LB" ou "KG" ne change rien
                End If

                pal._adr_client_paie_transport = trs.client_paie_transport
                pal._cli_prix_vente_inclut_transport = ch.prix_vente_inclut_transport

                ' résumé de la logique "qui paie transport"
                ' prx_vte_inc_trs | cli_pai_trs = [P]oly ou [C]lient
                '        0               0         C
                '        0               1         C
                '        1               0         P
                '        1               1         C
                pal._qui_paie_transport = If(ch.prix_vente_inclut_transport = True AndAlso trs.client_paie_transport = False, "P", "C")
            Next

            ' Répartir sur les palettes au prorata du poids
            Dim total = fp.Palettes.Sum(Function(x) x._fac_trs_qte_lb)
            For Each cp In fp.Palettes
                Dim ratio = (cp._fac_trs_qte_lb / total)
                cp._fac_trs_mnt_can = R4(ratio * montant)
                cp._fac_trs_rbs_can = R4(ratio * rabais)
            Next

        Next

        ' regrouper les palettes par requisition	
        Dim grpReq = lstPal.Where(Function(x) x._id_req_trs > 0).GroupBy(Function(x) x._id_req_trs)
        ' pour chaque groupe 
        For Each gr In grpReq
            ' a) trouver la req
            Dim req = dicReq(gr.Key)
            ' b) repartir le montant de la requisition sur les palettes au prorata
            If req.montant <> 0 Then
                Dim total = gr.Sum(Function(x) x._fac_trs_qte_lb)
                For Each pal In gr
                    Dim ratio = pal._fac_trs_qte_lb / total
                    pal._fac_req_mnt_can = R4(ratio * req.montant)
                Next
            End If
        Next

        '-futur?- clear dic & lst ?

        Return lstPal

    End Function

    Private Function GetData(Of T)(ppdb As PetaPoco.Database, sql As String, ParamArray args As Object()) As List(Of T)
        Dim lst = ppdb.Fetch(Of T)(sql, args)
        Return lst
    End Function

    Private Function ProcessSecteur(datCout As DateTime,
                         id_moyen_transport As Integer,
                                     dicSec As Dictionary(Of Integer, PoidsMontant),
                                       taux As Decimal,
                                     params As List(Of std_cout_transport_parametre)
                                          ) As List(Of std_cout_transport_secteur)

        Dim secteurs = New List(Of std_cout_transport_secteur)
        Dim lstDrops = New List(Of SommaireRequisition)

#If DEBUG Then
        Dim param = params.Where(Function(x) x.type = "sec").Single '-fix!- str comp
#Else
        Dim param = params.Where(Function(x) x.type = "sec").First
#End If

        For Each idSec In allSecID
            Dim lsh = lstLSH.Where(Function(x) x.id_secteur = idSec AndAlso datCout >= x.date_debut AndAlso datCout < x.date_fin).SingleOrDefault

            Dim secteur = New std_cout_transport_secteur

            If dicSec.ContainsKey(idSec) Then lstDrops = dicSec(idSec).Requisitions.ToList Else lstDrops.Clear()

            If id_moyen_transport > 0 Then lstDrops.RemoveAll(Function(x) x.id_transport_moyen <> id_moyen_transport) ' purger les moyens de transport qui ne sont pas du type qu'on veut analyser; 0 = tous

            Dim assezDeDrops = lstDrops.Count >= param.nb_drop_min
            If param.qte_lb_min_par_drop > 0 Then
                assezDeDrops = lstDrops.Where(Function(x) x.poids_net_cmd_adr >= param.qte_lb_min_par_drop).Count > param.nb_drop_min
                lstDrops = lstDrops.Where(Function(x) x.poids_net_cmd_adr >= param.qte_lb_min_par_drop).ToList '-alex-'
            End If
            If param.use_last_x_drop > 0 Then lstDrops = lstDrops.OrderByDescending(Function(x) x.date_exped).Take(param.use_last_x_drop).ToList

            If assezDeDrops Then

                ' calcul de la moyenne
                Dim avg = lstDrops.Select(Function(x) x.cout_lb_can).ToList.Average

                ' Filtre écart type supérieur (pas de support pour inférieur actuellement)
                If param.filtre_ecart_type_superieur > 0 Then
                    Dim std = lstDrops.Select(Function(x) x.cout_lb_can).ToList.StdDev
                    Dim cut = avg + (std * param.filtre_ecart_type_superieur)
                    lstDrops.RemoveAll(Function(x) x.cout_lb_can > cut)
                End If
                Debug.Assert(lstDrops.Count > 0)

                Dim sumCoutCan = lstDrops.Sum(Function(x) x.cout_lb_can * x.poids_net_cmd_adr)
                Dim sumPoids = lstDrops.Sum(Function(x) x.poids_net_cmd_adr)
                If avg > 0 Then avg = sumCoutCan / sumPoids

                secteur.id_cout_transport_source_calcul = 2 ' 12 mois secteur

                secteur.cout_transport_pondere_can = R4(avg)
                secteur.cout_transport_pondere_us = R4(avg / taux)

                ' Note - les stats n'inclus pas les drops éliminer par les filtres
                secteur.stats_nombre_palette = lstDrops.Sum(Function(x) x.nombre_palette)
                secteur.stats_total_cout_can = sumCoutCan
                secteur.stats_total_poids_lb = sumPoids

            Else

                If lsh IsNot Nothing Then

                    secteur.id_cout_transport_source_calcul = 1

                    Dim couTrs = {lsh.cout_transport_camion,
                                  lsh.cout_transport_camion,
                                  lsh.cout_transport_train,
                                  lsh.cout_transport_bateau,
                                  lsh.cout_transport_avion}(id_moyen_transport) ' utiliser cout CAMION pour TOUS

                    secteur.cout_transport_pondere_can = If(couTrs, 0)
                    secteur.cout_transport_pondere_us = R4(If(couTrs, 0) / taux)

                    secteur.stats_nombre_palette = 0
                    secteur.stats_total_cout_can = 0
                    secteur.stats_total_poids_lb = 0

                Else
                    secteur = Nothing ' si on n'a pas de LSH, alors le secteur n'existait pas à cette date, on ignore.
                End If

            End If

            ' Autres champs
            If secteur IsNot Nothing Then

                'secteur.id_std_cout_transport_secteur = idSeq.Next
                secteur.id_secteur = idSec
                secteur.date_cout = datCout
                secteur.id_transport_moyen = id_moyen_transport

                secteur.cout_transport_can = secteur.cout_transport_pondere_can
                secteur.cout_transport_standard_can = secteur.cout_transport_pondere_can ' pour les secteur, le cout STD = cout pondéré

                Dim svg = lstSVG.Where(Function(x) datCout >= x.date_debut AndAlso datCout < x.date_fin).Single
                secteur.entreposage_can = svg.entreposage
                secteur.douane_assurance_can = If(lsh?.douane_assurance, 0)

                secteur.cout_transport_us = secteur.cout_transport_pondere_us
                secteur.cout_transport_standard_us = secteur.cout_transport_pondere_us ' pour les secteur, le cout STD = cout pondéré

                secteur.entreposage_us = R4(svg.entreposage / taux)
                secteur.douane_assurance_us = R4(If(lsh?.douane_assurance, 0) / taux)

                'If lsh Is Nothing Then Debug.Write("")

                secteur.taux_us = taux

                AppliquerOperations(param.operations, secteur)

                secteurs.Add(secteur)

            End If

        Next

        Return secteurs

    End Function

    Private Function ProcessAdresse(datCout As DateTime,
                         id_moyen_transport As Integer,
                                     dicAdr As Dictionary(Of Integer, PoidsMontant),
                                       taux As Decimal,
                                     params As List(Of std_cout_transport_parametre),
                                     lstSec As List(Of std_cout_transport_secteur),
                                     lstPal As List(Of commande_palette_work),
                               grpCliSecTrs As IEnumerable(Of IGrouping(Of (id_client As Integer, id_secteur As Integer, qui_paie_transport As String), CliSecAdrTrs))
                                          ) As List(Of std_cout_transport_adresse)

        Dim adresses = New List(Of std_cout_transport_adresse)
        Dim lstDrops = New List(Of SommaireRequisition)
#If DEBUG Then
        Dim param = params.Where(Function(x) x.type = "adr").Single
#Else
        Dim param = params.Where(Function(x) x.type = "adr").First
#End If
        For Each grp In grpCliSecTrs ' grp d'adresse du client pour le secteur par payeur de transport...

            'If grp.Count > 1 Then Debug.Write("")

            Dim id_client = grp.Key.id_client
            Dim id_secteur = grp.Key.id_secteur
            Dim qui_paie_trs = grp.Key.qui_paie_transport : Debug.Assert({"P", "C"}.Contains(qui_paie_trs))
            Dim adressesIDs = grp.Select(Function(x) x.id_adresse).ToList

            lstDrops.Clear()
            For Each csat In grp
                If dicAdr.ContainsKey(csat.id_adresse) Then lstDrops.AddRange(dicAdr(csat.id_adresse).Requisitions.ToList)
            Next

            If id_moyen_transport > 0 Then
                lstDrops.RemoveAll(Function(x) x.id_transport_moyen <> id_moyen_transport) ' purger les moyens de transport qui ne sont pas du type qu'on veut analyser; 0 = tous
                'lstPal.RemoveAll(Function(x) x._id_req_moy_trs <> id_moyen_transport)
                For i = lstPal.Count - 1 To 0 Step -1
                    If lstPal(i)._id_req_moy_trs <> id_moyen_transport Then lstPal.RemoveAt(i)
                Next
            End If

            Dim sec = lstSec.Where(Function(x) x.id_secteur = id_secteur).SingleOrDefault
            If sec Is Nothing Then
                'Debug.Assert(lstDrops.Count = 0)
                Continue For
            End If
            Debug.Assert(sec IsNot Nothing)

            Dim cli = lstCli.Where(Function(x) x.id_client = id_client).SingleOrDefault
            If cli Is Nothing Then
                ' Client supprimé de la BD... on ignore.
                Continue For
            End If
            Debug.Assert(cli IsNot Nothing)

            Dim ch = dicCliHis(id_client).Where(Function(x) datCout >= x.date_debut AndAlso datCout < x.date_fin).SingleOrDefault
            ' ch peut etre nothing...

            Dim adrModele = New std_cout_transport_adresse ' cet objet sera "multiplié" pour chaque adresse du groupe

            Dim assezDeDrops = lstDrops.Count >= param.nb_drop_min
            If param.qte_lb_min_par_drop > 0 Then
                lstDrops = lstDrops.Where(Function(x) x.poids_net_cmd_adr >= param.qte_lb_min_par_drop).ToList '-alex-
                assezDeDrops = lstDrops.Count > param.nb_drop_min
            End If
            If param.use_last_x_drop > 0 Then lstDrops = lstDrops.OrderByDescending(Function(x) x.date_exped).Take(param.use_last_x_drop).ToList

            If assezDeDrops Then

                ' calcul de la moyenne
                Dim avg = lstDrops.Select(Function(x) x.cout_lb_can).ToList.Average

                ' Filtre écart type supérieur (pas de support pour inférieur actuellement)
                If param.filtre_ecart_type_superieur > 0 Then
                    Dim std = lstDrops.Select(Function(x) x.cout_lb_can).ToList.StdDev
                    Dim cut = avg + (std * param.filtre_ecart_type_superieur)
                    lstDrops.RemoveAll(Function(x) x.cout_lb_can > cut)
                End If
                Debug.Assert(lstDrops.Count > 0)

                Dim sumCoutCan = lstDrops.Sum(Function(x) x.cout_lb_can * x.poids_net_cmd_adr)
                Dim sumPoids = lstDrops.Sum(Function(x) x.poids_net_cmd_adr)
                If avg > 0 Then avg = sumCoutCan / sumPoids

                adrModele.id_cout_transport_source_calcul = 3 ' 12 mois adresse

                adrModele.cout_transport_pondere_can = R4(avg)
                adrModele.cout_transport_pondere_us = R4(avg / taux)

                ' Note - les stats n'inclus pas les drops éliminées par les filtres
                adrModele.stats_nombre_palette = lstDrops.Sum(Function(x) x.nombre_palette)
                adrModele.stats_total_cout_can = sumCoutCan
                adrModele.stats_total_poids_lb = sumPoids

            Else

                If sec IsNot Nothing Then

                    adrModele.id_cout_transport_source_calcul = sec.id_cout_transport_source_calcul ' provient du secteur ou de LSH

                    adrModele.cout_transport_pondere_can = If(sec.cout_transport_pondere_can, 0)
                    adrModele.cout_transport_pondere_us = If(sec.cout_transport_pondere_us, 0)

                    adrModele.stats_nombre_palette = 0
                    adrModele.stats_total_cout_can = 0
                    adrModele.stats_total_poids_lb = 0

                Else
                    adrModele = Nothing ' si on n'a pas de secteur, alors l'adresse n'existait pas à cette date, on ignore.
                End If

            End If

            ' Calcul cout standard - AssezDeDrops pour le standard doit inclure les réquisitions a zéro.
            ' Ajout 2023-03-20: on veut les palettes qui match le payeur du transport seulement
            Dim lstPalAdr = New List(Of commande_palette_work)
            For i = 0 To lstPal.Count - 1
                If lstPal(i)._qui_paie_transport = qui_paie_trs Then
                    For j = 0 To adressesIDs.Count - 1
                        If adressesIDs(j) = lstPal(i)._exp_id_adresse Then lstPalAdr.Add(lstPal(i)) : Exit For
                    Next
                End If
            Next

            Dim drops = lstPalAdr.GroupBy(Function(x) (x.id_requisition_transport, x._exp_id_adresse)) ' regrouper par "drops" (livraison à une adresse)
            Dim nbDeDrops = drops.Count

            Dim assezDeDropsSTD = nbDeDrops >= param.nb_drop_min
            If param.qte_lb_min_par_drop > 0 Then
                drops = drops.Where(Function(g) g.Sum(Function(x) x._fac_trs_qte_lb) >= param.qte_lb_min_par_drop)
                assezDeDropsSTD = drops.Count > param.nb_drop_min
            End If
            If param.use_last_x_drop > 0 Then drops = drops.OrderByDescending(Function(g) g.First._exp_date_exped).Take(param.use_last_x_drop)

            Dim lstPalPourCalcStd = drops.SelectMany(Function(g) g).ToList ' Ungroup les palettes de chaque groupes dans une seule liste

            If assezDeDropsSTD Then ' Calcul du cout standard

                Dim sumMntReq = lstPalPourCalcStd.Sum(Function(x) x._fac_req_mnt_can)
                Dim sumMntFac = lstPalPourCalcStd.Sum(Function(x) x._fac_trs_mnt_can)
                Dim sumRabFac = lstPalPourCalcStd.Sum(Function(x) x._fac_trs_rbs_can)

                Dim couStdTot = sumMntReq - sumRabFac - sumMntFac ' montant réquisition + rabais donné au client - frais chargés au client
                'If couStdTot < 0 Then Debug.Write("")
                If couStdTot < 0 Then couStdTot = 0

                Dim sumLbTot = lstPalPourCalcStd.Sum(Function(x) x._fac_trs_qte_lb) ' peut etre le montant actuel de la palette (net) ou le poids rouleau calculé dans la commande (type ROL)
                Debug.Assert(sumLbTot > 0)

                Dim couStdLb = couStdTot / sumLbTot

                adrModele.cout_transport_standard_can = R4(couStdLb)
                adrModele.cout_transport_standard_us = R4(couStdLb / taux)

            Else
                ' pas assez de réquisitions, on prend le secteur
                If sec IsNot Nothing Then
                    adrModele.cout_transport_standard_can = sec.cout_transport_standard_can
                    adrModele.cout_transport_standard_us = sec.cout_transport_standard_us
                End If
            End If

            ' Autres champs
            If adrModele IsNot Nothing Then

                adrModele.date_cout = datCout
                adrModele.id_transport_moyen = id_moyen_transport
                adrModele.id_secteur_transport = sec.id_secteur

                adrModele.cout_transport_can = adrModele.cout_transport_pondere_can
                adrModele.cout_transport_us = adrModele.cout_transport_pondere_us

                adrModele.entreposage_can = sec.entreposage_can
                adrModele.douane_assurance_can = sec.douane_assurance_can

                adrModele.entreposage_us = sec.entreposage_us
                adrModele.douane_assurance_us = sec.douane_assurance_us

                adrModele.taux_us = taux

                AppliquerOperations(param.operations, adrModele)

                If ch IsNot Nothing Then
                    If cli.id_devise = 3 Then
                        adrModele.rabais_client_can = If(ch.rabais_actif AndAlso ch.rabais_type = 2, ch.rabais_montant, 0)
                        adrModele.rabais_client_us = R4(If(adrModele.rabais_client_can, 0) / taux)
                    Else
                        adrModele.rabais_client_us = If(ch.rabais_actif AndAlso ch.rabais_type = 2, ch.rabais_montant, 0)
                        adrModele.rabais_client_can = R4(If(adrModele.rabais_client_us, 0) * taux)
                    End If
                    adrModele.prix_vente_inclut_transport = ch.prix_vente_inclut_transport
                Else
                    adrModele.prix_vente_inclut_transport = False
                End If

                '######################################################
                ' Rendu ici, le processing est par adresse individuelle
                '######################################################
                For Each csa In grp

                    Dim adresse = adrModele.ShallowCopy ' on clone l'objet modèle avec ses valeurs
                    adresse.id_adresse = csa.id_adresse

                    ' on va chercher le adr_cli_histo et record de transport associé pour l'adresse; il ne fait pas de sens de tenter de regrouper ces valeurs...
                    Dim ach = dicAdrHisA(csa.id_adresse).Where(Function(x) datCout >= x.date_debut AndAlso datCout < x.date_fin).SingleOrDefault
                    If ach Is Nothing OrElse ach.id_secteur = 0 Then
                        'Debug.Write("")
                        Continue For
                    End If
                    Debug.Assert(ach IsNot Nothing)

                    Dim trs = lstTrs.Where(Function(x) x.id_transport = ach.id_transport).SingleOrDefault
                    'trs peut etre nothing

                    adresse.client_paie_transport = If(trs Is Nothing, False, trs.client_paie_transport)

                    'Si pas assez de drop client - email 2023-02-15 18h23 dlaurin 
                    If Not assezDeDropsSTD Then
                        'Si client paie transport ou prix inclus pas transport
                        If adresse.prix_vente_inclut_transport = False Then
                            'transport a 0 - on assume que le client paie sont transport.
                            adresse.cout_transport_standard_can = 0
                            adresse.cout_transport_standard_us = 0
                        Else
                            If adresse.client_paie_transport Then
                                'transport a 0
                                adresse.cout_transport_standard_can = If(adresse.rabais_client_can, 0)
                                adresse.cout_transport_standard_us = If(adresse.rabais_client_us, 0)
                            Else
                                'Si pas assez de drop secteur
                                If sec.id_cout_transport_source_calcul = 1 Then ' 1 = cout secteur provient de LSH
                                    'Liste secteur
                                    adresse.cout_transport_standard_can = sec.cout_transport_standard_can
                                    adresse.cout_transport_standard_us = sec.cout_transport_standard_us
                                Else
                                    'Secteur pond
                                    adresse.cout_transport_standard_can = sec.cout_transport_standard_can
                                    adresse.cout_transport_standard_us = sec.cout_transport_standard_us
                                End If
                            End If
                        End If
                    Else
                        'Cout du transport du client pond
                        'adresse.cout_transport_standard_can = (bon selon calcul ci-haut)
                        'adresse.cout_transport_standard_us = (bon selon calcul ci-haut)
                    End If

                    adresse.entrepot_externe_can = R4(ach.cout_entrepo_ext_us * taux)
                    adresse.entrepot_externe_us = ach.cout_entrepo_ext_us

                    adresse.variable_client_can = If(adresse.cout_transport_standard_can, 0) _
                                                + If(adresse.douane_assurance_can, 0) _
                                                + If(adresse.entreposage_can, 0) _
                                                + If(adresse.entrepot_externe_can, 0)

                    adresse.variable_client_us = If(adresse.cout_transport_standard_us, 0) _
                                               + If(adresse.douane_assurance_us, 0) _
                                               + If(adresse.entreposage_us, 0) _
                                               + If(adresse.entrepot_externe_us, 0)

                    adresse.nom_client = cli.nom_tri
                    adresse.type_adresse = ach.type
                    adresse.adresse = ach.nom

                    adresses.Add(adresse)

                Next

            End If

        Next

        Return adresses

    End Function

    Private Function ProcessClient(datCout As DateTime,
                        id_moyen_transport As Integer,
                                    dicCli As Dictionary(Of Integer, PoidsMontant),
                                      taux As Decimal,
                                    params As List(Of std_cout_transport_parametre),
                                    lstSec As List(Of std_cout_transport_secteur),
                                    lstPal As List(Of commande_palette_work)
                                         ) As List(Of std_cout_transport_client)

        'Static idSeq As New Sequence ' pour ID auto-générés

        Dim path = ""

        Dim clients = New List(Of std_cout_transport_client)
        Dim lstDrops = New List(Of SommaireRequisition)

#If DEBUG Then
        Dim param = params.Where(Function(x) x.type = "cli").Single '-fix!- str comp
#Else
        Dim param = params.Where(Function(x) x.type = "cli").First
#End If

        For Each idCli In allCliID

            'If idCli = 200626 And datCout >= #2022-12-01# And datCout < #2023-02-01# Then Debug.Write("")

            If dicCli.ContainsKey(idCli) Then lstDrops = dicCli(idCli).Requisitions.ToList Else lstDrops.Clear()

            If id_moyen_transport > 0 Then
                lstDrops.RemoveAll(Function(x) x.id_transport_moyen <> id_moyen_transport) ' purger les moyens de transport qui ne sont pas du type qu'on veut analyser; 0 = tous
                For i = lstPal.Count - 1 To 0 Step -1
                    If lstPal(i)._id_req_moy_trs <> id_moyen_transport Then lstPal.RemoveAt(i)
                Next
            End If

            Dim sec As std_cout_transport_secteur = Nothing
            Dim utiliser = param.util_facture.Trim.ToLowerInvariant
            Dim selection = param.util_selection.Trim.ToLowerInvariant
            Dim lstTmp As List(Of adresse_client_historique) = Nothing
            Dim tmp As adresse_client_historique = Nothing
            Select Case utiliser
                Case "adresse"
                    ' on prend le secteur de l'adresse indiquée dans le champ "util_selection" des parametres
                    lstTmp = Nothing : tmp = Nothing
                    dicAdrHisC.TryGetValue(idCli, lstTmp)
                    If lstTmp IsNot Nothing Then tmp = lstTmp.Where(Function(x) x.type = selection AndAlso x.actif AndAlso datCout >= x.date_debut AndAlso datCout < x.date_fin).SingleOrDefault
                    If tmp IsNot Nothing Then sec = lstSec.Where(Function(x) x.id_secteur = tmp.id_secteur).SingleOrDefault
                Case "secteur"
                    If lstDrops.Count > 0 Then '-futur?- support pour max_drop vs autre...?
                        'Debug.Write("")
                        Dim grp = lstDrops.GroupBy(Function(x) x.id_secteur).OrderByDescending(Function(g) g.Count).ToList
                        sec = lstSec.Where(Function(x) x.id_secteur = grp.First.First.id_secteur).SingleOrDefault ' id_secteur from the 1st item of the 1st group...
                        Debug.Assert(sec IsNot Nothing)
                    Else
                        ' sec is nothing réglé plus bas...
                    End If
                Case Else : Debug.Assert(False) ' nouveau parametre inconnu? -fix- envoyer courriel ou qqe chose pour avertir.
            End Select
            If sec Is Nothing Then
                ' on prend le secteur de l'adresse principale
                lstTmp = Nothing : tmp = Nothing
                dicAdrHisC.TryGetValue(idCli, lstTmp)
                If lstTmp IsNot Nothing Then tmp = lstTmp.Where(Function(x) x.type = "Principale" AndAlso x.actif AndAlso datCout >= x.date_debut AndAlso datCout < x.date_fin).OrderBy(Function(x) x.id_adresse_client_historique).FirstOrDefault
                If tmp Is Nothing AndAlso lstTmp IsNot Nothing Then
                    tmp = lstTmp.Where(Function(x) x.actif AndAlso datCout >= x.date_debut AndAlso datCout < x.date_fin).OrderBy(Function(x) x.id_adresse_client_historique).FirstOrDefault
                End If
                If tmp IsNot Nothing Then sec = lstSec.Where(Function(x) x.id_secteur = tmp.id_secteur).SingleOrDefault
            End If
            If sec Is Nothing Then
                'Debug.Assert(lstDrops.Count = 0)
                Continue For ' secteur (et client) n'existait pas à l'époque
            End If

            Dim ach As adresse_client_historique = Nothing
            lstTmp = Nothing
            dicAdrHisC.TryGetValue(idCli, lstTmp)
            If lstTmp IsNot Nothing Then ach = lstTmp.Where(Function(x) x.type = "Principale" AndAlso x.actif AndAlso datCout >= x.date_debut AndAlso datCout < x.date_fin).OrderBy(Function(x) x.id_adresse_client_historique).FirstOrDefault
            If ach Is Nothing AndAlso lstTmp IsNot Nothing Then
                ' pas d'adresse principale... on prend la 1ere qui fitte la date
                ach = lstTmp.Where(Function(x) x.actif AndAlso datCout >= x.date_debut AndAlso datCout < x.date_fin).OrderBy(Function(x) x.id_adresse_client_historique).FirstOrDefault
            End If
            Debug.Assert(ach IsNot Nothing)

            ' Aller chercher le max cout_entrepo_ext_us dans les adresses actives du client. ' TODO: ajout flag actif dans adresse_client_historique
            Dim coutEntrepotExterneUS = lstTmp.Where(Function(x) x.actif AndAlso datCout >= x.date_debut AndAlso datCout < x.date_fin).Max(Function(x) x.cout_entrepo_ext_us)

            Dim cli = lstCli.Where(Function(x) x.id_client = idCli).SingleOrDefault
            If cli Is Nothing Then
                ' Client supprimé de la BD... on ignore.
                Continue For
            End If
            Debug.Assert(cli IsNot Nothing)

            Dim trs = lstTrs.Where(Function(x) x.id_transport = ach.id_transport).SingleOrDefault
            ' trs peut etre nothing

            Dim ch = dicCliHis(idCli).Where(Function(x) datCout >= x.date_debut AndAlso datCout < x.date_fin).SingleOrDefault
            ' ch peut etre nothing...

            Dim client = New std_cout_transport_client

            Dim assezDeDrops = lstDrops.Count >= param.nb_drop_min
            If param.qte_lb_min_par_drop > 0 Then
                lstDrops = lstDrops.Where(Function(x) x.poids_net_cmd_adr >= param.qte_lb_min_par_drop).ToList '-alex-
                assezDeDrops = lstDrops.Count > param.nb_drop_min
            End If
            If param.use_last_x_drop > 0 Then lstDrops = lstDrops.OrderByDescending(Function(x) x.date_exped).Take(param.use_last_x_drop).ToList

            If assezDeDrops Then

                ' calcul de la moyenne
                Dim avg = lstDrops.Select(Function(x) x.cout_lb_can).ToList.Average

                ' Filtre écart type supérieur (pas de support pour inférieur actuellement)
                If param.filtre_ecart_type_superieur > 0 Then
                    Dim std = lstDrops.Select(Function(x) x.cout_lb_can).ToList.StdDev
                    Dim cut = avg + (std * param.filtre_ecart_type_superieur)
                    lstDrops.RemoveAll(Function(x) x.cout_lb_can > cut)
                End If
                Debug.Assert(lstDrops.Count > 0)

                Dim sumCoutCan = lstDrops.Sum(Function(x) x.cout_lb_can * x.poids_net_cmd_adr)
                Dim sumPoids = lstDrops.Sum(Function(x) x.poids_net_cmd_adr)
                If avg > 0 Then avg = sumCoutCan / sumPoids

                client.id_cout_transport_source_calcul = 4 ' 12 mois client

                client.cout_transport_pondere_can = R4(avg)
                client.cout_transport_pondere_us = R4(avg / taux)

                ' Note - les stats n'inclus pas les drops éliminer par les filtres
                client.stats_nombre_palette = lstDrops.Sum(Function(x) x.nombre_palette)
                client.stats_total_cout_can = sumCoutCan
                client.stats_total_poids_lb = sumPoids

            Else

                If sec IsNot Nothing Then

                    client.id_cout_transport_source_calcul = sec.id_cout_transport_source_calcul ' provient du secteur ou de LSH

                    client.cout_transport_pondere_can = If(sec.cout_transport_pondere_can, 0)
                    client.cout_transport_pondere_us = If(sec.cout_transport_pondere_us, 0)

                    client.stats_nombre_palette = 0
                    client.stats_total_cout_can = 0
                    client.stats_total_poids_lb = 0

                Else
                    client = Nothing ' si on n'a pas de secteur, alors l'adresse n'existait pas à cette date, on ignore.
                End If

            End If

            ' Calcul cout standard - AssezDeDrops pour le standard doit inclure les requistions a zéro.
            Dim lstPalCli = New List(Of commande_palette_work) 'lstPal.Where(Function(x) x._exp_id_client = idCli).ToList
            For i = 0 To lstPal.Count - 1
                If lstPal(i)._exp_id_client = idCli Then lstPalCli.Add(lstPal(i))
            Next

            Dim drops = lstPalCli.GroupBy(Function(x) (x.id_requisition_transport, x._exp_id_adresse)) ' regrouper par "drops" (livraison à une adresse)
            Dim nbDeDrops = drops.Count

            Dim assezDeDropsSTD = nbDeDrops >= param.nb_drop_min
            If param.qte_lb_min_par_drop > 0 Then
                drops = drops.Where(Function(g) g.Sum(Function(x) x._fac_trs_qte_lb) >= param.qte_lb_min_par_drop)
                assezDeDropsSTD = drops.Count > param.nb_drop_min
            End If
            If param.use_last_x_drop > 0 Then drops = drops.OrderByDescending(Function(g) g.First._exp_date_exped).Take(param.use_last_x_drop)

            Dim lstPalPourCalcStd = drops.SelectMany(Function(g) g).ToList ' Ungroup les palettes de chaque groupes dans une seule liste

            If assezDeDropsSTD Then

                Dim sumMntReq = lstPalPourCalcStd.Sum(Function(x) x._fac_req_mnt_can)
                Dim sumMntFac = lstPalPourCalcStd.Sum(Function(x) x._fac_trs_mnt_can)
                Dim sumRabFac = lstPalPourCalcStd.Sum(Function(x) x._fac_trs_rbs_can)

                Dim couStdTot = sumMntReq - sumRabFac - sumMntFac ' montant réquisition + rabais donné au client - frais chargés au client
                'If couStdTot < 0 Then Debug.Write("") ' se produit pour les clients à qui on charge le trs; on charge plus que notre coût.
                If couStdTot < 0 Then couStdTot = 0

                Dim sumLbTot = lstPalPourCalcStd.Sum(Function(x) x._fac_trs_qte_lb) ' peut etre le montant actuel de la palette (net) ou le poids rouleau calculé dans la commande (type ROL)
                Debug.Assert(sumLbTot > 0)

                Dim couStdLb = couStdTot / sumLbTot

                client.cout_transport_standard_can = R4(couStdLb)
                client.cout_transport_standard_us = R4(couStdLb / taux)
            Else
                client.cout_transport_standard_can = sec.cout_transport_standard_can
                client.cout_transport_standard_us = sec.cout_transport_standard_us
            End If

            ' Autres champs
            If client IsNot Nothing Then

                client.id_client = idCli
                client.date_cout = datCout
                client.id_transport_moyen = id_moyen_transport
                client.id_secteur_transport = sec.id_secteur

                'Selon parametre "util_facture"
                Select Case utiliser
                    Case "adresse" '-futur?- lookup adresse principale?
                        client.cout_transport_can = client.cout_transport_pondere_can
                        client.cout_transport_us = client.cout_transport_pondere_us
                    Case "secteur"
                        client.cout_transport_can = sec.cout_transport_pondere_can
                        client.cout_transport_us = sec.cout_transport_pondere_us
                    Case Else : Debug.Assert(False) ' nouveau parametre inconnu? (-fix- envoyer courriel ou qqe chose pour avertir.
                End Select

                client.entreposage_can = sec.entreposage_can
                client.douane_assurance_can = sec.douane_assurance_can

                client.entreposage_us = sec.entreposage_us
                client.douane_assurance_us = sec.douane_assurance_us

                client.taux_us = taux

                AppliquerOperations(param.operations, client)

                If ch IsNot Nothing Then
                    If cli.id_devise = 3 Then
                        client.rabais_client_can = If(ch.rabais_actif, ch.rabais_montant, 0)
                        client.rabais_client_us = R4(If(client.rabais_client_can, 0) / taux)
                    Else
                        client.rabais_client_us = If(ch.rabais_actif, ch.rabais_montant, 0)
                        client.rabais_client_can = R4(If(client.rabais_client_can, 0) * taux)
                    End If
                    client.prix_vente_inclut_transport = ch.prix_vente_inclut_transport
                End If

                client.nom_client = cli.nom_tri
                client.type_adresse = ach.type
                client.adresse = ach.nom
                client.client_paie_transport = If(trs Is Nothing, False, trs.client_paie_transport)

                'Si pas assez de drop client - email 2023-02-15 18h23 dlaurin 
                If Not assezDeDropsSTD Then
                    'Si client paie transport ou prix inclus pas transport
                    If (client.prix_vente_inclut_transport = False) Then
                        'transport a 0
                        client.cout_transport_standard_can = 0
                        client.cout_transport_standard_us = 0
                    ElseIf client.client_paie_transport Then
                        'transport a 0
                        client.cout_transport_standard_can = If(client.rabais_client_can, 0)
                        client.cout_transport_standard_us = If(client.rabais_client_us, 0)
                    Else
                        'Si pas assez de drop secteur
                        If sec.id_cout_transport_source_calcul = 1 Then ' 1 = cout secteur provient de LSH
                            'Liste secteur
                            client.cout_transport_standard_can = sec.cout_transport_standard_can
                            client.cout_transport_standard_us = sec.cout_transport_standard_us
                        Else
                            'Secteur pond
                            client.cout_transport_standard_can = sec.cout_transport_standard_can
                            client.cout_transport_standard_us = sec.cout_transport_standard_us
                        End If
                    End If
                Else
                    'Cout du transport du client pond
                    'client.cout_transport_standard_can = (bon selon calcul ci-haut)
                    'client.cout_transport_standard_us = (bon selon calcul ci-haut)
                End If

                'Si pas assez de drop client - email 2023-02-15 18h23 dlaurin 
                If Not assezDeDrops Then
                    'Si client paie transport ou prix inclus pas transport
                    If (client.prix_vente_inclut_transport = False) Then
                        'transport a 0
                        client.cout_transport_standard_can = 0
                        client.cout_transport_standard_us = 0
                    Else
                        If client.client_paie_transport Then
                            'transport a 0
                            client.cout_transport_standard_can = If(client.rabais_client_can, 0)
                            client.cout_transport_standard_us = If(client.rabais_client_us, 0)
                        Else
                            'Si pas assez de drop secteur
                            If sec.id_cout_transport_source_calcul = 1 Then ' 1 = cout secteur provient de LSH
                                'Liste secteur
                                client.cout_transport_standard_can = sec.cout_transport_standard_can
                                client.cout_transport_standard_us = sec.cout_transport_standard_us
                            Else
                                'Secteur pond
                                client.cout_transport_standard_can = sec.cout_transport_standard_can
                                client.cout_transport_standard_us = sec.cout_transport_standard_us
                            End If
                        End If
                    End If
                Else
                    'Cout du transport du client pond
                    'client.cout_transport_standard_can = (bon selon calcul ci-haut)
                    'client.cout_transport_standard_us = (bon selon calcul ci-haut)
                End If

                'client.entrepot_externe_can = R4(ach.cout_entrepo_ext_us * taux) ' DONE: aller chercher le max cout_entrepo_ext_us dans les adresses actives du client.
                'client.entrepot_externe_us = ach.cout_entrepo_ext_us
                client.entrepot_externe_can = R4(coutEntrepotExterneUS * taux)
                client.entrepot_externe_us = coutEntrepotExterneUS

                client.variable_client_can = If(client.cout_transport_standard_can, 0) _
                                           + If(client.douane_assurance_can, 0) _
                                           + If(client.entreposage_can, 0) _
                                           + If(client.entrepot_externe_can, 0)

                client.variable_client_us = If(client.cout_transport_standard_us, 0) _
                                          + If(client.douane_assurance_us, 0) _
                                          + If(client.entreposage_us, 0) _
                                          + If(client.entrepot_externe_us, 0)

                client.nom_client = cli.nom_tri
                client.type_adresse = ach.type
                client.adresse = ach.nom

                clients.Add(client)

            End If

        Next

        Return clients

    End Function

    ' Les champs cout_transport_facture_<devise> sont mis a jour ici
    Private Sub AppliquerOperations(operations As String, coutTrs As std_cout_transport)

        ' Note: les opérations sont appliquées dans l'ordre ou il apparaissent dans le string... ex: OP1;OP2;OP3 

        Dim tmp_can = If(coutTrs.cout_transport_can, 0)
        Dim tmp_us = If(coutTrs.cout_transport_us, 0)

        If operations.Trim <> "" Then

            Dim tmp = operations.ToUpperInvariant
            tmp = tmp.Replace(" ", ";")
            tmp = tmp.Replace(",", ";")
            Dim lstOps = tmp.Split({";"}, StringSplitOptions.RemoveEmptyEntries)

            For Each code In lstOps

                Dim op = lstParamOps.Where(Function(x) String.Compare(x.code, code, ignoreCase:=True) = 0).Single

                Select Case op.operation.Trim.ToLowerInvariant
                    Case "" ' defaut, aucune opération
                    Case "ceiling", "plafond"
                        Dim roundTo = CDec(op.parametre_1)
                        tmp_can = ArrondirCenneSuperieure(tmp_can, roundTo)
                        tmp_us = ArrondirCenneSuperieure(tmp_us, roundTo)
                    Case Else
                        Debug.Assert(False) ' operation non supportée
                End Select
            Next

        End If

        coutTrs.cout_transport_facture_can = tmp_can
        coutTrs.cout_transport_facture_us = tmp_us

    End Sub

    ' Pour arrondir a la prochaine cenne, passer 0.01... pour la prochaine demi-cenne, passer 0.005
    Function ArrondirCenneSuperieure(montant As Decimal, roundTo As Decimal) As Decimal
        Return Math.Ceiling(montant / roundTo) * roundTo
    End Function

    Public Function R4(value As Decimal) As Decimal
        Return Math.Round(value, 4, MidpointRounding.AwayFromZero)
    End Function

    Private Sub UpdateDictionary(dic As Dictionary(Of Integer, PoidsMontant), typ As String, row As SommaireRequisition, req As PoidsMontant, id As Integer, nom As String)

        If dic.ContainsKey(id) Then
            dic(id).Cout += row.poids_net_cmd_adr * req.CoutLb
            dic(id).PoidsNetLb += row.poids_net_cmd_adr
            dic(id).Requisitions.Add(row)
        Else
            Dim itm = New PoidsMontant With {.Type = typ, .ID = id, .Nom = nom, .Cout = row.poids_net_cmd_adr * req.CoutLb, .PoidsNetLb = row.poids_net_cmd_adr}
            itm.Requisitions.Add(row)
            dic(id) = itm
        End If

    End Sub

    Public Sub SaveToDB(Of T)(lstObj As List(Of T), DBCONNECT As String, tableName As String, Optional truncateTable As Boolean = False, Optional batchSize As Integer = 20000)

        Dim lFrom = 0 ' for list sub range, to prevent dataTable out-of-memory -PARAM-
        Using cn = New SqlConnection(DBCONNECT)
            cn.Open()

            If truncateTable Then
                Console.WriteLine($"TRUNCATING TABLE {tableName}") ' on {DBCONNECT.RemoveToken("password", ";"c)}")
                Using cmd = New SqlCommand("truncate table " & tableName, cn)
                    cmd.ExecuteNonQuery()
                End Using
            End If

            Using sqlTx = cn.BeginTransaction
                Using bulkCopy = New SqlBulkCopy(cn, SqlBulkCopyOptions.KeepIdentity, sqlTx)
                    bulkCopy.BatchSize = batchSize
                    bulkCopy.DestinationTableName = tableName
                    bulkCopy.BulkCopyTimeout = 900
                    Try
                        Do
                            If lFrom + batchSize > lstObj.Count Then batchSize = lstObj.Count - lFrom
                            Dim tmp1 = lstObj.GetRange(lFrom, batchSize)
                            Dim tmp2 = AsDataTable(tmp1)
                            bulkCopy.WriteToServer(tmp2)
                            lFrom += batchSize
                        Loop Until lFrom >= lstObj.Count
                        sqlTx.Commit()
                    Catch ex As Exception
                        Debug.Assert(False)
                        sqlTx.Rollback()
                        cn.Close()
                    End Try
                End Using
            End Using
        End Using

    End Sub

    Public Function AsDataTable(Of T)(data As IEnumerable(Of T)) As DataTable
        ' from http://blog.developers.ba/bulk-insert-generic-list-sql-server-minimum-lines-code/
        ' modified for PetaPoco decorated objects -gg

        Dim typ = GetType(T)
        Dim properties = TypeDescriptor.GetProperties(typ)
        Dim props = typ.GetProperties

        Dim table = New DataTable()
        Dim dic = New Dictionary(Of String, String) ' object property name, petapoco field name
        Dim fldName As String = Nothing

        Dim colAttr As PetaPoco.ColumnAttribute
        Dim ignoreAttr As PetaPoco.IgnoreAttribute = Nothing
        For Each prop As PropertyDescriptor In properties
            ' Get field name from Petapoco attribute if it exists
            Dim p = props.Where(Function(x) x.Name = prop.Name).SingleOrDefault

            ' Use Petapoco attributes for column names and .ignores
            colAttr = CType(Attribute.GetCustomAttribute(p, GetType(PetaPoco.ColumnAttribute)), PetaPoco.ColumnAttribute)
            ignoreAttr = If(colAttr Is Nothing, CType(Attribute.GetCustomAttribute(p, GetType(PetaPoco.IgnoreAttribute)), PetaPoco.IgnoreAttribute), Nothing)

            If ignoreAttr Is Nothing Then
                fldName = If(colAttr IsNot Nothing, colAttr.Name, prop.Name)
                dic.Add(prop.Name, fldName) ' cache
                table.Columns.Add(fldName, If(Nullable.GetUnderlyingType(prop.PropertyType), prop.PropertyType)) ' Add field to table
            End If
        Next

        For Each item As T In data
            Dim row As DataRow = table.NewRow()
            For Each prop As PropertyDescriptor In properties
                If dic.TryGetValue(prop.Name, fldName) Then row(fldName) = If(prop.GetValue(item), DBNull.Value)
            Next
            table.Rows.Add(row)
        Next

        Return table

    End Function

    ' Staging tables must have the same name as the "normal" table with the "staging_" prefix.
    ' Sub accepts either staging table name list or normal table name list (Staging tables should always start with "staging_")
    Public Sub SwapStagingTables(DBCONNECT As String, tablesNames As IEnumerable(Of String))

        Dim lstTables = New List(Of (table As String, stagingTable As String)) ' nice custom tuple

        For Each tn In tablesNames

            tn = tn.ToUpperInvariant

            Dim tbl = "", stg = "" ' table and staging names

            Select Case True
                Case tn.Contains("DBO.STAGING_")
                    tbl = tn.Replace("DBO.STAGING_", "DBO.")
                    stg = tn
                Case tn.Contains("STAGING_")
                    tbl = tn.Replace("STAGING_", "")
                    stg = tn
                Case tn.Contains("DBO.")
                    tbl = tn
                    stg = tn.Replace("DBO.", "DBO.STAGING_")
                Case tn.Contains(".")
                    Debug.Assert(False)
                    Throw New Exception($"Table name format Not supported In SwapStagingTables(): {tn}")
                Case Else
                    tbl = tn
                    stg = $"STAGING_{tn}"
            End Select

            lstTables.Add((tbl, stg))

        Next

        ' Swap staging to live
        Using cn = New SqlConnection(DBCONNECT)
            cn.Open()
            Using sqlTx = cn.BeginTransaction
                For Each tblPair In lstTables
                    Using cmd = New SqlCommand($"truncate table {tblPair.table}", cn, sqlTx)
                        cmd.ExecuteNonQuery()
                    End Using
                    Using cmd = New SqlCommand($"alter table {tblPair.stagingTable} switch to {tblPair.table}", cn, sqlTx)
                        cmd.ExecuteNonQuery()
                    End Using
                Next
                sqlTx.Commit()
            End Using
        End Using

    End Sub

    Private Class PoidsMontant

        Public Property [Type] As String
        Public Property ID As Integer
        Public Property Nom As String

        Public Property Cout As Decimal '-futur?- devise ... si aucune indication, on assume CAN
        Public Property PoidsNetLb As Decimal

        Public Property Requisitions As List(Of SommaireRequisition) = New List(Of SommaireRequisition)

        Public ReadOnly Property CoutLb As Decimal
            Get
                If PoidsNetLb = 0 Then Return 0
                Return R4(Cout / PoidsNetLb) ', 4, MidpointRounding.AwayFromZero)
            End Get
        End Property
    End Class

    <PetaPoco.PrimaryKey("ID_STD_TAUX")>
    Partial Public Class std_taux
        Public Property id_std_taux As Integer
        Public Property date_debut As DateTime
        Public Property date_fin As DateTime
        Public Property taux As Decimal
    End Class

    <PetaPoco.PrimaryKey("id_requisition_transport")>
    Partial Public Class SommaireRequisition
        Public Property id_requisition_transport As Integer
        Public Property id_transport_moyen As Integer ' 1..4 camion, train, bateau, avion, 99 = tous?
        Public Property date_exped As DateTime
        Public Property id_devise As Integer
        Public Property std_taux As Decimal
        Public Property montant As Decimal
        Public Property poids_net_cmd_adr As Integer
        Public Property id_adresse As Integer
        Public Property id_client As Integer
        Public Property id_secteur As Integer
        Public Property nom_adresse As String
        Public Property nom_client As String
        Public Property nom_secteur As String
        Public Property nombre_palette As Integer

        <PetaPoco.Ignore> Public Property cout_lb As Decimal = -1

        <PetaPoco.Ignore>
        Public ReadOnly Property cout_lb_can As Decimal
            Get
                If id_devise = 3 Then Return cout_lb
                If id_devise = 4 Then Return R4(cout_lb * std_taux)
                Return -1D
            End Get
        End Property

    End Class

    <PetaPoco.PrimaryKey("ID_STD_VARIABLE_GLOBALE")>
    Partial Public Class std_variable_globale
        Public Property id_std_variable_globale As Integer
        Public Property date_debut As DateTime
        Public Property date_fin As DateTime
        'Public Property credit As Decimal
        'Public Property rabais_volume_autre As Decimal
        'Public Property entretien_reparation As Decimal
        'Public Property electricite As Decimal
        'Public Property frais_vente As Decimal
        'Public Property interet As Decimal
        'Public Property mauvaise_creance As Decimal
        'Public Property expedition As Decimal
        'Public Property [mod] As Decimal
        'Public Property total As Decimal
        Public Property entreposage As Decimal
        'Public Property reprocess As Decimal
        'Public Property extrusion As Decimal
        'Public Property commentaire As String
    End Class

    <PetaPoco.PrimaryKey("ListeSecteurHistoriqueID")>
    Partial Public Class listeSecteurHistorique
        Public Property listesecteurhistoriqueid As Integer
        Public Property id_secteur As Integer
        Public Property date_debut As DateTime
        Public Property date_fin As DateTime
        'Public Property cout_transport As Decimal?
        Public Property cout_transport_camion As Decimal?
        Public Property cout_transport_train As Decimal?
        Public Property cout_transport_bateau As Decimal?
        Public Property cout_transport_avion As Decimal?
        Public Property douane_assurance As Decimal?
    End Class

    <PetaPoco.PrimaryKey("ID_CLIENT_HISTORIQUE")>
    Partial Public Class client_historique
        Public Property id_client_historique As Integer
        Public Property id_client As Integer
        Public Property date_debut As DateTime
        Public Property date_fin As DateTime '-futur?- coalesce null with 9999-12-31?
        'Public Property rabais_volume_lbs As Decimal
        Public Property rabais_actif As Boolean
        Public Property rabais_type As Integer
        Public Property rabais_montant As Decimal
        'Public Property cout_emballage As Decimal
        'Public Property com_emballage As String
        Public Property prix_vente_inclut_transport As Boolean = True ' historiquement, les prix de ventes incluaient le transport; premiers changements en 2022-11-01.
    End Class

    <PetaPoco.PrimaryKey("ID_ADRESSE_CLIENT_HISTORIQUE")>
    Partial Public Class adresse_client_historique
        Public Property id_adresse_client_historique As Integer
        Public Property id_adresse As Integer
        Public Property id_client As Integer
        Public Property type As String
        Public Property nom As String
        'Public Property delais_transport As Short?
        'Public Property no_douane As String
        'Public Property adresse As String
        'Public Property adresse_2 As String
        'Public Property ville As String
        'Public Property province As String
        'Public Property pays As String
        'Public Property code_postal As String
        'Public Property telephone_1 As String
        'Public Property telephone_2 As String
        'Public Property fax As String
        'Public Property www As String
        'Public Property email As String
        Public Property id_transport As Integer
        'Public Property id_taxe As Integer?
        'Public Property faxer_commande As Boolean
        'Public Property interurbain As Boolean
        'Public Property commentaire As String
        'Public Property com_transport As String
        'Public Property destinataire As String
        'Public Property faxexped As String
        'Public Property destexped As String
        'Public Property faxer_exped As Boolean
        'Public Property formatintlfax As Boolean
        'Public Property formatintltel1 As Boolean
        'Public Property formatintltel2 As Boolean
        'Public Property formatintlfaxconfexped As Boolean
        Public Property id_secteur As Integer
        'Public Property transport_route_std As Boolean
        'Public Property envoi_courriel_exped As Boolean?
        'Public Property courriel_exped As String
        'Public Property id_secteur_old As Integer?
        'Public Property id_adresse_entrepot_externe As Integer?
        Public Property cout_entrepo_ext_us As Decimal
        Public Property date_debut As DateTime
        Public Property date_fin As DateTime
        Public Property actif As Boolean
    End Class

    <PetaPoco.PrimaryKey("ID_TRANSPORT")>
    Partial Public Class transport
        Public Property id_transport As Integer
        'Public Property nom As String
        'Public Property description As String
        'Public Property exportation As Boolean
        'Public Property courtier As String
        'Public Property dans_notre_camion As Boolean
        'Public Property dans_livre As Boolean
        'Public Property dans_pickup As Boolean
        'Public Property desc_dyn As String
        'Public Property fact_client As Boolean
        'Public Property recyclage As Boolean
        Public Property client_paie_transport As Boolean
        'Public Property rowversion As Byte()
    End Class

    <PetaPoco.PrimaryKey("ID_CLIENT")>
    Partial Public Class client
        Public Property id_client As Integer
        'Public Property no_client As String
        'Public Property nom As String
        'Public Property type_entreprise As String
        'Public Property type_client As String
        'Public Property reference As String
        'Public Property langue As String
        'Public Property sae As String
        'Public Property id_palette As Integer?
        'Public Property rabais_volume As Boolean
        'Public Property rabais_volume_lbs As Decimal?
        'Public Property date_volume As DateTime?
        'Public Property logo As String
        'Public Property logo_client As Byte()
        'Public Property statement_name As String
        'Public Property id_vendeur As Integer?
        'Public Property id_terme As Integer?
        'Public Property id_classe As Integer?
        'Public Property id_territoire As Integer?
        'Public Property credit_see As Single?
        'Public Property duree_see As String
        'Public Property date_saisie As DateTime?
        'Public Property limite_entrepot As Integer?
        'Public Property limite_temps As Short?
        'Public Property nb_employe As Short?
        'Public Property nb_extruders As Short?
        'Public Property nb_bag As Short?
        'Public Property nb_print As Short?
        'Public Property potentiel_livre As Integer?
        'Public Property potentiel_$ As Single?
        'Public Property superficie As Integer?
        'Public Property evaluation As String
        'Public Property prod_converted As Short?
        'Public Property prod_general As Short?
        'Public Property distribution As Short?
        Public Property nom_tri As String
        'Public Property dia_rouleau_max As Short?
        'Public Property poids_rouleau_max As Short?
        'Public Property date_modification As DateTime?
        'Public Property imprime As Boolean
        'Public Property couleur As String
        'Public Property couleur_lettrage As String
        Public Property id_devise As Integer
        'Public Property unite As String
        'Public Property conf_commande_vendeur As Boolean
        'Public Property nb_etiquette_rol As Short?
        'Public Property liste_prix_change As Boolean
        'Public Property client_echantillon As Boolean
        'Public Property id_type_client As Integer?
        'Public Property sae_facturation As String
        'Public Property unite_facturation As String
        'Public Property nb_feuille_skid As Integer?
        'Public Property no_overun As Boolean
        'Public Property com_production As String
        'Public Property com_interne As String
        'Public Property com_emballage As String
        'Public Property bloc_note As String
        'Public Property rabais_actif As Boolean
        'Public Property rabais_type As Integer?
        'Public Property rabais_montant As Single?
        'Public Property conf_exped_vendeur As Boolean
        'Public Property impression_facture As Boolean
        'Public Property impression_ps As Boolean
        'Public Property impression_bol As Boolean
        'Public Property credit_car As Single?
        'Public Property credit_cmd As Single?
        'Public Property credit_pourc_acc As Single?
        'Public Property credit_note As String
        'Public Property credit_overide As Integer?
        'Public Property credit_overide_note As String
        'Public Property credit_bloque_prod As Boolean
        'Public Property credit_bloque_exped As Boolean
        'Public Property contact_csr As String
        'Public Property telephone_csr As String
        'Public Property intltelephone_csr As Boolean
        'Public Property fax_csr As String
        'Public Property intlfax_csr As Boolean
        'Public Property poste_csr As String
        'Public Property email_csr As String
        'Public Property interurbain_csr As Boolean
        'Public Property id_modele_logo As Integer?
        'Public Property id_employe As Integer?
        'Public Property imp_detail_rouleau As Boolean
        'Public Property appliquersurcharge As Boolean
        'Public Property montantsurcharge As Single?
        'Public Property nbcopiedetailrouleau As Integer?
        'Public Property id_core As Integer?
        'Public Property nb_etiquette_preid_rol As Integer?
        'Public Property nb_etiquette_preid_sample As Integer?
        'Public Property id_rapport_client_detskidroll As Integer?
        'Public Property id_rapport_client_etiqsupp As Integer?
        'Public Property taux_commission As Decimal?
        'Public Property echantillon As Boolean
        'Public Property rptskidslipid As Integer
        'Public Property recycleur As Boolean
        'Public Property cout_emballage As Decimal
        'Public Property recycleur_default As Boolean?
        'Public Property client_ignore_polyexpert_report As Boolean
        'Public Property id_categorie_client As Integer?
        'Public Property emb_nb_etage_max As Integer?
        'Public Property emb_nb_rouleau_max_largeur As Integer?
        'Public Property emb_nb_rouleau_max_longueur As Integer?
        'Public Property emb_feuille_double_stacking As Boolean
        'Public Property emb_docket_echantillon As Boolean
        'Public Property id_entrepot As Integer?
        'Public Property dia_rouleau_min As Short?
        'Public Property emb_ligne_traitement As Boolean
        'Public Property emb_deux_planches_sous_palette_old As Boolean
        'Public Property emb_feuille_palette_dans_docket As Boolean
        'Public Property verif_diff_poids_pesee As Boolean
        'Public Property has_release_number As Boolean
        'Public Property rowversion As Byte()
        'Public Property bolt_seal_obligatoire As Boolean
        'Public Property id_emb_feuille_palette As Integer
        'Public Property use_email_facture As Boolean
        'Public Property email_from As String
        'Public Property email_tos As String
        'Public Property email_subject As String
        'Public Property email_body As String
        'Public Property is_show_cos_on_order_confirmation As Boolean
        'Public Property consigne_nb_jr_vert As Integer?
        'Public Property consigne_nb_jr_jaune As Integer?
        'Public Property consigne_nb_jr_rouge As Integer?
        'Public Property consigne_nb_jr_delai_fact As Integer?
        'Public Property consigne_nb_jr_delai_supp_fact As Integer?
        'Public Property consigne_force_fact As Boolean
        'Public Property consigne_nb_hr_fact_apres_choix As Integer?
        'Public Property consigne_permettre_facturation As Boolean
        'Public Property consigne_courriel_force_fact As String
        'Public Property commentaire_source_prix As String
        'Public Property prix_vente_inclut_transport As Boolean?
    End Class

    Function GetDBParam(Of T)(ppdb As PetaPoco.Database, application As String, section As String, cle As String, Optional defaultValue As T = Nothing) As T
        Dim appPrm = ppdb.Fetch(Of application_parametre)("where application = @0 and section = @1 and cle = @2", application, section, cle).SingleOrDefault
        Return If(appPrm Is Nothing, defaultValue, CType(CObj(appPrm.valeur), T))
    End Function

    <PetaPoco.PrimaryKey("ID")>
    Partial Public Class application_parametre
        Public Property id As Integer
        Public Property application As String
        Public Property section As String
        Public Property cle As String
        Public Property desc_fra As String
        Public Property desc_ang As String
        Public Property type As String
        Public Property valeur As String
        Public Property unite As String
        Public Property ordre_affichage As Integer
    End Class

    Partial Public Class std_cout_transport_parametre
        Public Property id_std_cout_transport_parametre As Integer
        Public Property date_debut As DateTime
        Public Property date_fin As DateTime
        Public Property type As String
        Public Property util_facture As String
        Public Property util_selection As String
        Public Property nb_drop_min As Integer
        Public Property qte_lb_min_par_drop As Integer
        Public Property use_last_x_drop As Integer
        Public Property filtre_ecart_type_superieur As Integer
        Public Property operations As String
        Public Property description As String
    End Class

    Partial Public Class std_cout_transport_parametre_operation
        Public Property id_std_cout_transport_parametre_operation As Integer
        Public Property code As String
        Public Property operation As String
        Public Property parametre_1 As String
        Public Property parametre_2 As String
        Public Property description As String
    End Class

    Private Class AdrSec ' simple lookup
        Property id_adresse As Integer
        Property id_secteur As Integer
    End Class

    Private Class FactureProduit ' Regroupe les palettes d'une facture et les frais de transport associés
        <PetaPoco.Ignore> Public Property transport_total As Decimal
        <PetaPoco.Ignore> Public Property rabais_total As Decimal

        Property Commandes As HashSet(Of commande) = New HashSet(Of commande)
        Property Palettes As HashSet(Of commande_palette_work) = New HashSet(Of commande_palette_work)
        Property Requisitions As HashSet(Of requisition_transport) = New HashSet(Of requisition_transport)
        Property Expeditions As HashSet(Of expedition) = New HashSet(Of expedition)
        Property Facture As facture
        Property Produits As HashSet(Of facture_item) = New HashSet(Of facture_item)
        Property Transports As HashSet(Of facture_item) = New HashSet(Of facture_item)
    End Class


    <PetaPoco.PrimaryKey("id_commande_palette")>
    <ProtoContract>
    Partial Public Class std_cout_transport_palette
        <ProtoMember(1)> Public Property id_commande_palette As Integer
        <ProtoMember(2)> Public Property id_commande As Integer
        <ProtoMember(3)> Public Property id_requisition_transport As Integer
        <ProtoMember(4)> Public Property id_client As Integer
        <ProtoMember(5)> Public Property id_adresse As Integer
        <ProtoMember(6)> Public Property id_secteur As Integer
        <ProtoMember(7)> Public Property id_facture As Integer
        <ProtoMember(8)> Public Property date_expedition As DateTime
        <ProtoMember(9)> Public Property id_transport_moyen As Integer
        <ProtoMember(10)> Public Property requisition_montant_can As Decimal
        <ProtoMember(11)> Public Property prix_vente_inclut_transport As Boolean
        <ProtoMember(12)> Public Property client_paie_transport As Boolean
        <ProtoMember(13)> Public Property facture_transport_montant_can As Decimal
        <ProtoMember(14)> Public Property facture_transport_rabais_can As Decimal
        <ProtoMember(15)> Public Property facture_unite_produit As String
        <ProtoMember(16)> Public Property facture_transport_qte_lb As Decimal
        <ProtoMember(17)> Public Property periode As Integer
    End Class

    <PetaPoco.PrimaryKey("ID_COMMANDE_PALETTE")>
    Partial Public Class commande_palette_work
        <PetaPoco.Ignore> Public Property _id_req_trs As Integer
        <PetaPoco.Ignore> Public Property _id_req_moy_trs As Integer
        <PetaPoco.Ignore> Public Property _fac_unite_prd As String
        <PetaPoco.Ignore> Public Property _fac_trs_qte_lb As Decimal

        <PetaPoco.Ignore> Public Property _fac_trs_mnt_can As Decimal
        <PetaPoco.Ignore> Public Property _fac_trs_rbs_can As Decimal
        <PetaPoco.Ignore> Public Property _fac_req_mnt_can As Decimal
        '<PetaPoco.Ignore> Public Property _fac_inc_prd_can As Decimal '-futur?-
        <PetaPoco.Ignore> Public Property _fac_id_facture As Integer

        <PetaPoco.Ignore> Public Property _exp_id_client As Integer
        <PetaPoco.Ignore> Public Property _exp_id_adresse As Integer
        <PetaPoco.Ignore> Public Property _exp_id_secteur As Integer
        <PetaPoco.Ignore> Public Property _exp_date_exped As Date

        <PetaPoco.Ignore> Public Property _cli_prix_vente_inclut_transport As Boolean ' de la table client_historique
        <PetaPoco.Ignore> Public Property _adr_client_paie_transport As Boolean ' de la table adresse_client_historique
        <PetaPoco.Ignore> Public Property _qui_paie_transport As String ' P = PolyExpert, C = Client

        Public Property id_commande_palette As Integer
        Public Property no_palette As Integer
        Public Property id_commande As Integer
        'Public Property id_operateur As Integer?
        'Public Property id_emballeur As Integer?
        'Public Property id_equipe As Integer?
        Public Property nb_rouleau As Integer
        Public Property poids_net As Integer
        Public Property poids_brut As Integer
        'Public Property to_print As Integer?
        'Public Property date_palette As DateTime?
        'Public Property date_modification As DateTime?
        'Public Property ouverte_flag As Boolean
        'Public Property emballee_flag As Boolean
        Public Property id_expedition As Integer
        'Public Property prep_expedition_x As Boolean
        'Public Property charge_expedition_x As Boolean
        'Public Property id_statut_palette As Integer?
        Public Property id_facture_item As Integer
        Public Property date_emballage As DateTime
        Public Property date_annulation As DateTime
        Public Property id_facture_item_credit As Integer
        'Public Property largeur As Single?
        'Public Property profondeur As Single?
        'Public Property hauteur As Single?
        'Public Property emplacements As String
        'Public Property id_emplacement_tampon As Integer?
        'Public Property date_envoyer As DateTime?
        Public Property id_requisition_transport As Integer
        'Public Property date_demande_transport As DateTime?
        'Public Property flag_x As Integer?
        'Public Property nbrouleauetage As Integer?
        'Public Property nbetage As Integer?
        'Public Property paletteid As Integer?
        'Public Property emballageid As Integer?
        'Public Property id_entrepot As Integer?
        'Public Property id_entrepot_dest As Integer?
        ''Public Property rowversion As Byte()
        'Public Property id_statut_exped_palette As Integer
        'Public Property old_no_palette As Integer?
        'Public Property date_facture_consigne As DateTime?
        'Public Property consigne_force_facture As Boolean
    End Class

    Class CliSecAdrTrs
        Property id_client As Integer
        Property id_secteur As Integer
        Property id_adresse As Integer
        Property qui_paie_transport As String ' P = PolyExpert, C = Client
    End Class

End Module

