﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.PrimaryKey("ID_VENDEUR")>
Partial Public Class vendeur_historique
    <ProtoMember(1)> Public Property ID_VENDEUR As Integer?
    <ProtoMember(2)> Public Property NOM As String
    <ProtoMember(3)> Public Property TELEPHONE As String
    <ProtoMember(4)> Public Property FAX As String
    <ProtoMember(5)> Public Property EMAIL As String
    <ProtoMember(6)> Public Property INTERURBAIN As Boolean?
    <ProtoMember(7)> Public Property TAUX_COMMISSION As Decimal?
    <ProtoMember(8)> Public Property FormatIntlTel As Boolean?
    <ProtoMember(9)> Public Property FormatIntlFax As Boolean?
    <ProtoMember(10)> Public Property METS_VPMM As Boolean?
    <ProtoMember(11)> Public Property NOM_COMPLET As String
    <ProtoMember(12)> Public Property Actif As Boolean?
    <ProtoMember(13)> Public Property BYEMAIL As Boolean?
    <ProtoMember(14)> Public Property EMAIL_PDF As String
    <ProtoMember(15)> Public Property TAUX_COMMISSION_LBS As Decimal?
    <ProtoMember(16)> Public Property ID_DEVISE_TAUX_COMMISSION_LBS As Integer
    <ProtoMember(17)> Public Property date_debut As DateTime
    <ProtoMember(18)> Public Property date_fin As DateTime?
    <ProtoMember(19)> Public Property id_historique As Integer
End Class