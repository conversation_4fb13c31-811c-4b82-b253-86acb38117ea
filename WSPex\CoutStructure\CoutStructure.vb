﻿Imports ProtoBuf
Imports Utilities

Module CoutStructure

    Private ReadOnly lock As New Object

    Public Function CalculCoutStructure() As String

        SyncLock lock ' une seule exécution à la fois.

            Try

                Using peta = GetDB()

                    Dim lstStr = peta.Fetch(Of [structure])("where id_pv is not null and id_pv > 0")
                    Dim lstRctStr = peta.Fetch(Of recette_structure)("")
                    Dim dicRctStr = lstRctStr.GroupBy(Function(x) x.id_structure).ToDictionary(Function(k) k.Key, Function(v) v.ToList)

                    Dim lstRct = peta.Fetch(Of recette)("")
                    Dim dicRct = lstRct.GroupBy(Function(x) x.id_recette).ToDictionary(Function(k) k.Key, Function(v) v.Single)

                    Dim lstRctRsn = peta.Fetch(Of recette_resine)("")
                    Dim dicRctRsn = lstRctRsn.GroupBy(Function(x) x.id_recette).ToDictionary(Function(k) k.Key, Function(v) v)
                    Dim lstPV = peta.Fetch(Of pv)("")

                    Dim virtuelles = peta.Fetch(Of resine_virtuelle)("where resine_principale = 1 and id_resine <> 1401").ToDictionary(Function(k) k.id_resine, Function(v) v.id_resine_relier)
                    Dim resineCouts = peta.Fetch(Of vwStructures_Resines_Couts)("").ToDictionary(Function(k) k.id_resine, Function(v) v)

                    ' Taux de change pour le mois.
                    Dim taux_change = peta.Fetch(Of Decimal)("select taux from std_taux where @0 between date_debut and date_fin", Now).Single

                    For Each struc In lstStr

                        Dim pv = lstPV.Where(Function(x) x.id_pv = struc.id_pv).SingleOrDefault
                        If pv Is Nothing Then Continue For

                        Dim rct_rsn As New Dictionary(Of Integer, List(Of recette_resine))

                        Dim rct_str As List(Of recette_structure) = Nothing
                        If dicRctStr.TryGetValue(struc.id_structure, rct_str) Then
                            If rct_str.Count = 0 Then Continue For
                            For Each rs In rct_str
                                rct_rsn(rs.id_recette) = dicRctRsn(rs.id_recette).ToList
                            Next
                        Else
                            Continue For
                        End If

                        Dim is_volumique = If(pv Is Nothing, True, pv.id_calc_pct_couche = 2)
                        Dim struct_actif_calc = True

                        CalcDensiteDesCouches(rct_str, rct_rsn, resineCouts, virtuelles, dicRct)

                        ' MAJ densités des recettes + actif_calc structure
                        For Each rs In rct_str
                            Dim rct As recette = Nothing
                            If dicRct.TryGetValue(rs.id_recette, rct) Then
                                If rct.actif = False OrElse rct.actif_calc = False Then struct_actif_calc = False
                                If rct.densite <> R4(rs.densite) Then
                                    rct.densite = R4(rs.densite)
                                    rct.changed = True
                                End If

                            Else
                                Continue For
                            End If
                        Next

                        ' Densité structure
                        Dim densMlg = CalcRatioMassVolDesCouches(is_volumique, rct_str)
                        If densMlg <> struc.densite Then
                            struc.densite = densMlg
                            struc.changed = True
                        End If

                        ' calcul des coût
                        Dim std = 0D, reel = 0D, liste = 0D
                        Dim tot_s = rct_str.Sum(Function(x) x.ratio_couche_massique)
                        For Each rs In rct_str

                            Dim rcts = dicRctRsn(rs.id_recette)
                            Dim tot_r = rcts.Sum(Function(x) x.ratio_resine)
                            Dim rct_reel = 0D

                            For Each rr In rcts
                                Dim rat_t = (rs.ratio_couche_massique / tot_s) * (rr.ratio_resine / tot_r)
                                liste += rat_t * rr.cout_liste_us
                                reel += rat_t * rr.cout_reel_us
                                std += rat_t * rr.cout_std_us

                                rct_reel += (rr.ratio_resine / tot_r) * rr.cout_reel_us
                            Next

                            ' update record recette
                            Dim rct As recette = Nothing
                            If dicRct.TryGetValue(rs.id_recette, rct) Then
                                If rct.cout_us <> R4(rct_reel) Then
                                    rct.cout_us = R4(rct_reel)
                                    rct.changed = True
                                End If
                                If rct.cout <> R4(rct_reel * taux_change) Then
                                    rct.cout = R4(rct_reel * taux_change)
                                    rct.changed = True
                                End If
                            End If

                        Next

                        liste = R4(liste)
                        reel = R4(reel)
                        std = R4(std)

                        ' Couts

                        Dim cutOff = 10D
                        Dim BAD = 99999.9999D

                        If liste = 0 OrElse liste > cutOff Then liste = BAD
                        If reel = 0 OrElse reel > cutOff Then reel = BAD
                        If std = 0 OrElse std > cutOff Then std = BAD

                        If struc.cout_liste_us <> liste Then
                            struc.cout_liste_us = liste
                            struc.changed = True
                        End If
                        If struc.cout_reel_us <> reel Then
                            struc.cout_reel_us = reel
                            struc.cout_us = reel
                            struc.cout = If(reel = BAD, BAD, R4(reel * taux_change))
                            struc.changed = True
                        End If
                        If struc.cout_std_us <> std Then
                            struc.cout_std_us = std
                            struc.changed = True
                        End If

                        If (struc.cout_liste_us > cutOff OrElse struc.cout_liste_us = 0) AndAlso struc.cout_liste_us < BAD Then
                            struc.cout_liste_us = BAD
                            struc.changed = True
                        End If
                        If (struc.cout_reel_us > cutOff OrElse struc.cout_reel_us = 0) AndAlso struc.cout_reel_us < BAD Then
                            struc.cout_reel_us = BAD
                            struc.changed = True
                        End If
                        If (struc.cout_std_us > cutOff OrElse struc.cout_std_us = 0) AndAlso struc.cout_std_us < BAD Then
                            struc.cout_std_us = BAD
                            struc.changed = True
                        End If

                        ' ACTIF_CALC
                        If struc.actif_calc <> struct_actif_calc Then
                            struc.actif_calc = struct_actif_calc
                            struc.changed = True
                        End If

                    Next

                    Dim cs = 0, cr = 0

                    ' update DB structures
                    Using tx = peta.GetTransaction
                        For Each s In lstStr
                            If s.changed Then
                                cs += 1
                                peta.Update(s)
                            End If
                        Next
                        tx.Complete()
                    End Using

                    ' update DB recettes
                    Using tx = peta.GetTransaction
                        For Each r In lstRct
                            If r.changed Then
                                cr += 1
                                peta.Update(r)
                            End If
                        Next
                        tx.Complete()
                    End Using

                    Return $"OK. MAJ: {cs} Structure{If(cs > 1, "s", "")}; {cr} Recette{If(cr > 1, "s", "")}."
                End Using

            Catch ex As Exception
                SendEmail("WSPex.CalculCoutStructure", "Une erreur est survenue dans WSPex.CalculCoutStructure", ex.ToString)
                Return $"ERROR"
            End Try

            Return $"OK"

        End SyncLock

    End Function

    'todo: uniformiser avec routines dans pexlib et déplacer vers librairie commune.

    Private Sub CalcDensiteDesCouches(recette_structures As List(Of recette_structure) _
                                , recette_resines As Dictionary(Of Integer, List(Of recette_resine)) _
                                , resines As Dictionary(Of Integer, vwStructures_Resines_Couts) _
                                , virtuelles As Dictionary(Of Integer, Integer) _
                                , recettes As Dictionary(Of Integer, recette))

        For Each rct_str In recette_structures

            Dim densite = 0D

            ' calculer la densite de cette recette
            Dim rct_rsn = recette_resines(rct_str.id_recette)

            Dim rct As recette = Nothing
            If Not recettes.TryGetValue(rct_str.id_recette, rct) Then
                Dim key = $"id_recette:{rct_str.id_recette}"
                Dim txt = $"Record manquant dans la table RECETTE: id_recette = {rct_str.id_recette}"
                SendEmail(key, txt, txt,, "<EMAIL>")
            End If

            Dim sumRatioResine = 0D
            Dim sumRatioDivDensite = 0D ' La densité pour une couche est toujours calculée avec la formule de Lee

            If rct IsNot Nothing Then rct.actif_internal = True

            For Each rr In rct_rsn
                sumRatioResine += rr.ratio_resine
                Dim rsn As vwStructures_Resines_Couts
                If resines(rr.id_resine).resine_virtuelle Then
                    ' on ne prend pas id_resine_relier, car on est normalement en LIVE et les virtuelles peuvent avoir changées
                    rsn = resines(virtuelles(rr.id_resine))
                    If rsn.id_resine <> rr.id_resine_relier Then
                        ' mettre a jour resine relier au besoin...
                        rr.id_resine_relier = rsn.id_resine
                        rr.changed = True
                    End If
                Else
                    rsn = resines(rr.id_resine)
                End If
                If rsn.densite <> rr.densite_resine Then rr.densite_resine = rsn.densite : rr.changed = True
                If rsn.cout_liste <> rr.cout_liste_us Then rr.cout_liste_us = rsn.cout_liste : rr.changed = True
                If rsn.cout_reel <> rr.cout_reel_us Then rr.cout_reel_us = rsn.cout_reel : rr.changed = True
                If rsn.cout_standard <> rr.cout_std_us Then rr.cout_std_us = rsn.cout_standard : rr.changed = True
                If rsn.actif = False AndAlso rct.actif_internal = True AndAlso rct IsNot Nothing Then rct.actif_internal = False
                sumRatioDivDensite += If(rr.densite_resine = 0, 0, rr.ratio_resine / rr.densite_resine)
            Next

            If sumRatioResine > 0 Then
                rct_str.densite = R4(If(sumRatioDivDensite = 0, 0, sumRatioResine / sumRatioDivDensite)) ' formule de Lee
            Else
                rct_str.densite = 0
            End If

            ' on réactive recette inactive seulement, on ne les désactives pas...
            If rct.actif_internal _
              AndAlso rct IsNot Nothing _
              AndAlso (rct.actif <> rct.actif_internal OrElse rct.actif_calc <> rct.actif_internal) Then
                rct.changed = True
                rct.actif = True
                rct.actif_calc = True
            End If

        Next

    End Sub

    Private Function CalcRatioMassVolDesCouches(is_volumique As Boolean, recettes As List(Of recette_structure)) As Decimal

        Dim dicRatio = New Dictionary(Of Integer, Decimal)

        Dim MelangeDensite = 0D

        ' Calcul du champ RatioEquivalent.
        ' Si la recette est massique, l'équivalent est le ratio volumique
        ' si la recette est volumique, l'équivalent est le massique

        ' Si on est en mode volumique et que le total des ratios volumique est zéro, alors on transfère les ratios massique dans le volumique et on recalcule le massique
        If is_volumique AndAlso recettes.Sum(Function(x) x.ratio_couche_volumique) = 0 Then
            'If recettes.Sum(Function(x) x.ratio_couche_massique) <= 0 Then util.Break
            recettes.ForEach(Sub(x) x.ratio_couche_volumique = x.ratio_couche_massique)
        End If

        ' 1) Densite du mélange total
        Dim totRatioCouche = recettes.Sum(Function(x) If(is_volumique, x.ratio_couche_volumique, x.ratio_couche_massique))
        'If totRatioCouche = 0 Then util.break

        If is_volumique Then

            ' les ratios couches sont volumique, donc des pourcentages de l'épaisseur totale
            ' le massique équivalent se calcule comme suit:
            ' 1) densite du mélange - somme de [ ratio couche * densite couche ]

            MelangeDensite = 0D
            For Each rct In recettes
                MelangeDensite += If(totRatioCouche = 0, 0, (rct.ratio_couche_volumique / totRatioCouche) * rct.densite)
            Next

            ' Avec la densité du mélange, on peut calculer le ratio équivalent massique
            For Each rct In recettes
                'rct.ratio_couche_massique = r4(If(MelangeDensite = 0, 0, rct.ratio_couche_volumique * rct.Densite / MelangeDensite))
                Dim ratio = R4(If(MelangeDensite = 0, 0, rct.ratio_couche_volumique * rct.densite / MelangeDensite))
                If ratio <> rct.ratio_couche_massique Then
                    rct.ratio_couche_massique = ratio
                    rct.changed = True
                End If
            Next

        Else ' IS_MASSIQUE

            ' les ratios couches sont massique, donc des pourcentages du mélange totale
            ' 1) densité du mélange - formule de Lee avec les ratios et densités des couches
            MelangeDensite = 0D
            For Each rct In recettes
                'Debug.Assert(mdlRct.Densite > 0)
                MelangeDensite += If(rct.densite = 0, 0, (rct.ratio_couche_massique / totRatioCouche) / rct.densite)
            Next
            MelangeDensite = If(MelangeDensite = 0, 0, 1 / MelangeDensite)

            ' Avec la densité du mélange, on peut calculer le ratio équivalent volumique
            Dim sumRatDens = recettes.Sum(Function(x) If(x.densite = 0, 0, x.ratio_couche_massique / x.densite))
            Dim sumRatCou = recettes.Sum(Function(x) x.ratio_couche_massique)
            For Each rct In recettes
                'rct.ratio_couche_volumique = R4(If(rct.Densite = 0, 0, rct.ratio_couche_massique / rct.Densite / sumRatDens * sumRatCou))
                Dim ratio = R4(If(rct.densite = 0, 0, rct.ratio_couche_massique / rct.densite / sumRatDens * sumRatCou))
                If ratio <> rct.ratio_couche_volumique Then
                    rct.ratio_couche_volumique = ratio
                    rct.changed = True
                End If
            Next

        End If

        Return R4(MelangeDensite)

    End Function

    <PetaPoco.PrimaryKey("ID_RECETTE_RESINE")>
    Partial Public Class recette_resine
        Public Property id_recette_resine As Integer
        Public Property id_recette As Integer
        Public Property id_resine As Integer
        Public Property ratio_resine As Decimal
        Public Property element As String
        'Public Property date_creation As DateTime
        'Public Property rowversion As Byte()
        'Public Property sandbox As String
        <PetaPoco.Ignore> Public Property densite_resine As Decimal
        <PetaPoco.Ignore> Public Property id_resine_relier As Integer
        <PetaPoco.Ignore> Public Property changed As Boolean
        '
        <PetaPoco.Ignore> Public Property cout_std_us As Decimal
        <PetaPoco.Ignore> Public Property cout_liste_us As Decimal
        <PetaPoco.Ignore> Public Property cout_reel_us As Decimal
    End Class

    <PetaPoco.PrimaryKey("ID_RECETTE")>
    Partial Public Class recette
        Public Property id_recette As Integer
        Public Property nom As String
        'Public Property com_interne As String
        'Public Property com_production As String
        'Public Property id_famille As Integer?
        Public Property actif As Boolean
        Public Property densite As Decimal                                  ' [_]
        Public Property cout As Decimal                                     ' [_] cout reel $Can
        'Public Property prix_lbs_us_normalise As Decimal                    ' [_] ?
        'Public Property note As Decimal?
        Public Property actif_calc As Boolean
        'Public Property com_production_bkp As String
        '	Public Property date_creation As DateTime
        Public Property cout_us As Decimal                                  ' [_] cout reel $us
        'Public Property rowversion As Byte()
        'Public Property signature As String
        <PetaPoco.Ignore> Public Property changed As Boolean
        <PetaPoco.Ignore> Public Property actif_internal As Boolean
    End Class

    <PetaPoco.PrimaryKey("ID_RECETTE_STRUCTURE")>
    Partial Public Class recette_structure
        Public Property id_recette_structure As Integer
        Public Property id_structure As Integer
        Public Property id_recette As Integer
        Public Property ratio_couche_massique As Decimal                    ' [X]
        Public Property ratio_couche_volumique As Decimal                   ' [X]
        Public Property no_ordre As Integer
        'Public Property flag As Boolean
        'Public Property feed As Boolean
        'Public Property date_creation As DateTime
        'Public Property rowversion As Byte()
        <PetaPoco.Ignore> Public Property densite As Decimal
        <PetaPoco.Ignore> Public Property changed As Boolean
    End Class

    <PetaPoco.PrimaryKey("ID_STRUCTURE")>
    Partial Public Class [structure]
        Public Property id_structure As Integer
        Public Property id_pv As Integer
        ''Public Property nom As String
        Public Property densite As Decimal                                  ' [X]
        'Public Property sheet_only As Boolean
        Public Property cout As Decimal                                     ' [X] cout reel $Can? Taux?
        'Public Property epaisseur_min As Single?
        'Public Property epaisseur_max As Single?
        'Public Property commentaire As String
        'Public Property id_structure_origine As Integer?
        'Public Property prix_lbs_us_normalise As Decimal                    ' [_] n'est plus utilisé. était mis a jour initialement dans la calculatrice (PolyExpert)
        'Public Property note As Decimal
        Public Property actif_calc As Boolean
        ''Public Property in_id_resine As String							' [?]
        'Public Property id_structure_old As Integer?
        'Public Property date_creation As DateTime
        'Public Property import As Integer?
        'Public Property note_upd_manuel As Boolean
        'Public Property old_id_pv As Integer?
        Public Property cout_us As Decimal                                  ' [X] cout reel $US?
        'Public Property bf_norme_dmd As Integer
        'Public Property bf_norme_act As Integer
        'Public Property rowversion As Byte()
        Public Property cout_std_us As Decimal                              ' [X]
        Public Property cout_liste_us As Decimal                            ' [X]
        Public Property cout_reel_us As Decimal                             ' [X]
        'Public Property id_structure_master As Integer?
        <PetaPoco.Ignore> Public Property changed As Boolean
        '<PetaPoco.Ignore> Public Property processed As Boolean
    End Class


    '<PetaPoco.PrimaryKey("ID_RESINE")>
    'Partial Public Class resine
    '	Public Property id_resine As Integer
    '	Public Property nom As String
    '	Public Property densite As Decimal
    '	'Public Property melt_index As Decimal?
    '	'Public Property slip As String
    '	'Public Property a_b As String
    '	'Public Property id_fournisseur As Integer?
    '	'Public Property type As String
    '	'Public Property ppa As String
    '	'Public Property commentaire As String
    '	'Public Property densite_lbs_pied_cube As Decimal?
    '	'Public Property note As Integer?
    '	Public Property prix_lbs_can As Decimal
    '	'Public Property prix_lbs_can_last As Decimal
    '	Public Property prix_lbs_us As Decimal
    '	'Public Property prix_lbs_us_last As Decimal
    '	'Public Property prix_lbs_date As DateTime
    '	Public Property prix_lbs_us_normalise As Decimal
    '	'Public Property prix_lbs_us_normalise_last As Decimal
    '	'Public Property prix_lbs_us_normalise_override As Boolean
    '	'Public Property prix_lbs_norm_date As DateTime
    '	Public Property taux_change As Decimal
    '	'Public Property qte_inventaire As Decimal?
    '	'Public Property date_inventaire As DateTime?
    '	Public Property actif As Boolean
    '	Public Property resine_virtuelle As Boolean
    '	'Public Property additif As Boolean
    '	'Public Property actif_last As Boolean
    '	Public Property id_resine_relier As Integer
    '	'Public Property reactive As Boolean
    '	'Public Property densite_bkp As Decimal?
    '	'Public Property densite_cor As Decimal?
    '	'Public Property date_creation As DateTime
    '	'Public Property ancienne_ref As String
    '	'Public Property resine_std As Boolean
    '	'Public Property delai_livraison_jour As Integer?
    '	'Public Property id_std_resine As Integer?
    '	'Public Property tmp_resine_type As Integer?
    '	'Public Property resine_type_id As Integer?
    '	'Public Property densite_reelle As Decimal?
    '	'Public Property id_type_rejet_resine_densite As Integer?
    '	'Public Property id_type_rejet_resine_couleur As Integer?
    '	'Public Property id_type_rejet_dispo_restriction As Integer?
    '	'Public Property compte_gl As String
    '	'Public Property rowversion As Byte()
    '	'Public Property qte_inv_min_lbs As Integer?
    '	'Public Property id_resine_groupe As Integer?
    '	'Public Property is_usine_addable As Boolean
    '	'Public Property isusinewatched As Boolean
    '	'Public Property is_interne As Boolean
    '	'Public Property is_trim As Boolean
    '	'Public Property consignation As Boolean
    '	'Public Property creation_resine_pex As Boolean
    '	'Public Property disponible_au_recyclage As Boolean
    '	'Public Property pas_echantillon_reception As Boolean
    '	'Public Property pourc_max_melange As Decimal?
    '	'Public Property datasheet_conforme As Boolean
    '	'Public Property leadtime As Integer?
    'End Class

    <PetaPoco.PrimaryKey("ID_RESINE_VIRTUELLE")>
    Partial Public Class resine_virtuelle
        Public Property id_resine_virtuelle As Integer
        Public Property id_resine As Integer
        Public Property id_resine_relier As Integer
        'Public Property date_creation As DateTime
        Public Property resine_principale As Boolean
        Public Property actif As Boolean
    End Class

    <PetaPoco.PrimaryKey("ID_PV")>
    Partial Public Class pv
        Public Property id_pv As Integer
        'Public Property import As Integer?
        'Public Property id_client As Integer?
        'Public Property id_produit As Integer?
        'Public Property item_client As String
        'Public Property qte_minimum As Integer?
        'Public Property chaine_pv As String
        'Public Property date_creation As DateTime
        'Public Property inactif As Boolean
        'Public Property com_interne As String
        'Public Property com_production As String
        'Public Property id_etiquette As Integer?
        'Public Property texte_supp As String
        'Public Property etiq_poids As Boolean
        'Public Property veut_core As Boolean
        'Public Property cert_analyse As Boolean
        'Public Property id_nouveau_produit As Integer?
        'Public Property chaine_pv_nouveau_produit As String
        'Public Property archive_produit As Boolean
        'Public Property id_footnote As Integer?
        'Public Property id_nouveau_pv As Integer?
        'Public Property facteur_conversion As Single?
        'Public Property opacite As Single?
        'Public Property id_machine As Integer?
        'Public Property blocage_tech As Boolean
        'Public Property isid As Integer?
        'Public Property epaisseur_etiquette As Boolean
        'Public Property id_pv_origine As Integer?
        'Public Property id_client_origine As Integer?
        'Public Property facteur_densite_yield As Decimal?
        'Public Property id_pv_old As Integer?
        'Public Property id_pv_standard As Integer?
        'Public Property modele_standard As Integer
        'Public Property detail_desactivation As String
        'Public Property raison_blocage_tech As String
        'Public Property additif_valide As Boolean
        'Public Property commentaire_recette_std As String
        'Public Property remplacer_pied_par_msi As Boolean
        'Public Property id_cycle As Integer?
        'Public Property upcharge_commentaire As String
        'Public Property upcharge As Decimal?
        'Public Property liste_machine_prod As String
        'Public Property emb_nb_etage_max As Integer?
        'Public Property emb_nb_rouleau_max_largeur As Integer?
        'Public Property emb_nb_rouleau_max_longueur As Integer?
        'Public Property emb_feuille_double_stacking As Boolean
        'Public Property emb_docket_echantillon As Boolean
        'Public Property emb_nb_etiquette_preid_rol As Integer?
        'Public Property emb_nbcopiedetailrouleau As Integer?
        'Public Property emb_id_rapport_client_detskidroll As Integer?
        'Public Property emb_id_rapport_client_etiqsupp As Integer?
        'Public Property emb_nb_feuille_skid As Integer?
        'Public Property emb_nb_etiquette_rol As Integer?
        'Public Property veut_core_old As Boolean
        'Public Property emb_ligne_traitement As Boolean
        'Public Property emb_deux_planches_sous_palette_old As Boolean
        'Public Property emb_feuille_palette_dans_docket As Boolean
        'Public Property rowversion As Byte()
        'Public Property commentaire_indice_qualite As String
        'Public Property commentaire_indice_qualite_client As String
        'Public Property id_pv_n_layer_extrusion As Integer
        'Public Property une_pesee_par_palette As Boolean
        'Public Property bf_norme_dmd As Integer
        'Public Property bf_norme_act As Integer
        'Public Property constante_densite_calcul_superficie As Decimal?
        'Public Property limiter_cert_analyse_rd As Boolean
        'Public Property id_pv_structure As Integer?
        'Public Property id_resine_recyclage As Integer?
        Public Property id_calc_pct_couche As Integer
    End Class

    <PetaPoco.PrimaryKey("id_resine")>
    Partial Public Class vwStructures_Resines_Couts
        Public Property id_resine As Integer
        Public Property nom As String
        Public Property resine_virtuelle As Boolean
        Public Property resine_std As Boolean?
        Public Property densite As Decimal
        'Public Property additif As Boolean?
        'Public Property commentaire As String
        Public Property actif As Boolean
        'Public Property resine_type_id As Integer?
        Public Property cout_standard As Decimal
        Public Property cout_reel As Decimal
        Public Property cout_liste As Decimal
        'Public Property rv_id As Integer?
        'Public Property rv_nom As String
        'Public Property ppm_slip As Decimal?
        'Public Property ppm_antiblock As Decimal?
        'Public Property ppm_ppa As Decimal?
        'Public Property ppm_uv As Decimal?
        'Public Property ppm_as As Decimal?
        'Public Property ppm_white As Decimal?
        'Public Property ppm_black As Decimal?
    End Class

End Module
