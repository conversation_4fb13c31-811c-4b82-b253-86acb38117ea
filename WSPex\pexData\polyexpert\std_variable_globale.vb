﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_std_variable_globale")>
<ProtoContract>
Partial Public Class std_variable_globale
    <ProtoMember(1)> Public Property id_std_variable_globale As Integer
    <ProtoMember(2)> Public Property date_debut As DateTime
    <ProtoMember(3)> Public Property date_fin As DateTime
    'Public Property credit As Decimal
    'Public Property rabais_volume_autre As Decimal
    'Public Property entretien_reparation As Decimal
    'Public Property electricite As Decimal
    'Public Property frais_vente As Decimal
    'Public Property interet As Decimal
    'Public Property mauvaise_creance As Decimal
    'Public Property expedition As Decimal
    'Public Property [mod] As Decimal
    <ProtoMember(13)> Public Property total As Decimal
    <ProtoMember(14)> Public Property entreposage As Decimal
    <ProtoMember(15)> Public Property reprocess As Decimal
    <ProtoMember(16)> Public Property extrusion As Decimal
    Public Property commentaire As String
End Class