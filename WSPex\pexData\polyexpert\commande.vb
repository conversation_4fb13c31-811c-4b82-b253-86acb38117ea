﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_commande")>
<ProtoContract>
Partial Public Class commande
    <ProtoMember(1)> Public Property id_commande As Integer
    <ProtoMember(2)> Public Property no_commande As String
    <ProtoMember(3)> Public Property id_client As Integer
    <ProtoMember(4)> Public Property type As String
    <ProtoMember(5)> Public Property date_commande As DateTime
    <ProtoMember(6)> Public Property id_adresse As Integer
    <ProtoMember(7)> Public Property id_pv As Integer
    <ProtoMember(8)> Public Property poids As Integer
    <ProtoMember(9)> Public Property longueur As Integer
    <ProtoMember(10)> Public Property id_forme As Integer
    <ProtoMember(11)> Public Property largeur_1 As Double
    <ProtoMember(12)> Public Property largeur_2 As Double
    <ProtoMember(13)> Public Property largeur_3 As Double
    <ProtoMember(14)> Public Property epaisseur As Double
    <ProtoMember(15)> Public Property type_traitement As String
    <ProtoMember(16)> Public Property poids_rouleau_cal As Double
    <ProtoMember(17)> Public Property nb_rouleau_cal As Integer
    <ProtoMember(18)> Public Property no_affichage As String
    <ProtoMember(19)> Public Property id_machine As Integer
    <ProtoMember(20)> Public Property id_statut As Integer
    <ProtoMember(21)> Public Property id_vendeur As Integer
    <ProtoMember(22)> Public Property id_statut_fact As Integer
    <ProtoMember(23)> Public Property id_statut_exped As Integer
    <ProtoMember(24)> Public Property id_devise As Integer
    <ProtoMember(25)> Public Property prix_lbs As Double
    <ProtoMember(26)> Public Property surcharge As Double
    <ProtoMember(27)> Public Property id_secteur As Integer
    <ProtoMember(28)> Public Property commande_retd As Boolean
    <ProtoMember(29)> Public Property escompte_liste_prix As Decimal
    <ProtoMember(30)> Public Property liste_prix_qte As Integer
End Class