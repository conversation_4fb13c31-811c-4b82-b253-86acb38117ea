<?xml version="1.0"?>
<doc>
    <assembly>
        <name>protobuf-net</name>
    </assembly>
    <members>
        <member name="T:ProtoBuf.BclHelpers">
            <summary>
            Provides support for common .NET types that do not have a direct representation
            in protobuf, using the definitions from bcl.proto
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.GetUninitializedObject(System.Type)">
            <summary>
            Creates a new instance of the specified type, bypassing the constructor.
            </summary>
            <param name="type">The type to create</param>
            <returns>The new instance</returns>
            <exception cref="T:System.NotSupportedException">If the platform does not support constructor-skipping</exception>
        </member>
        <member name="F:ProtoBuf.BclHelpers.TimestampEpoch">
            <summary>
            The default value for dates that are following google.protobuf.Timestamp semantics
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteTimeSpan(System.TimeSpan,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a TimeSpan to a protobuf stream using protobuf-net's own representation, bcl.TimeSpan
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadTimeSpan(ProtoBuf.ProtoReader)">
            <summary>
            Parses a TimeSpan from a protobuf stream using protobuf-net's own representation, bcl.TimeSpan
            </summary>        
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDuration(ProtoBuf.ProtoReader)">
            <summary>
            Parses a TimeSpan from a protobuf stream using the standardized format, google.protobuf.Duration
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDuration(System.TimeSpan,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a TimeSpan to a protobuf stream using the standardized format, google.protobuf.Duration
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadTimestamp(ProtoBuf.ProtoReader)">
            <summary>
            Parses a DateTime from a protobuf stream using the standardized format, google.protobuf.Timestamp
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteTimestamp(System.DateTime,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a DateTime to a protobuf stream using the standardized format, google.protobuf.Timestamp
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDateTime(ProtoBuf.ProtoReader)">
            <summary>
            Parses a DateTime from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDateTime(System.DateTime,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a DateTime to a protobuf stream, excluding the <c>Kind</c>
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDateTimeWithKind(System.DateTime,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a DateTime to a protobuf stream, including the <c>Kind</c>
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadDecimal(ProtoBuf.ProtoReader)">
            <summary>
            Parses a decimal from a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteDecimal(System.Decimal,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a decimal to a protobuf stream
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteGuid(System.Guid,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a Guid to a protobuf stream
            </summary>        
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadGuid(ProtoBuf.ProtoReader)">
            <summary>
            Parses a Guid from a protobuf stream
            </summary>
        </member>
        <member name="T:ProtoBuf.BclHelpers.NetObjectOptions">
            <summary>
            Optional behaviours that introduce .NET-specific functionality
            </summary>
        </member>
        <member name="F:ProtoBuf.BclHelpers.NetObjectOptions.None">
            <summary>
            No special behaviour
            </summary>
        </member>
        <member name="F:ProtoBuf.BclHelpers.NetObjectOptions.AsReference">
            <summary>
            Enables full object-tracking/full-graph support.
            </summary>
        </member>
        <member name="F:ProtoBuf.BclHelpers.NetObjectOptions.DynamicType">
            <summary>
            Embeds the type information into the stream, allowing usage with types not known in advance.
            </summary>
        </member>
        <member name="F:ProtoBuf.BclHelpers.NetObjectOptions.UseConstructor">
            <summary>
            If false, the constructor for the type is bypassed during deserialization, meaning any field initializers
            or other initialization code is skipped.
            </summary>
        </member>
        <member name="F:ProtoBuf.BclHelpers.NetObjectOptions.LateSet">
            <summary>
            Should the object index be reserved, rather than creating an object promptly
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.ReadNetObject(System.Object,ProtoBuf.ProtoReader,System.Int32,System.Type,ProtoBuf.BclHelpers.NetObjectOptions)">
            <summary>
            Reads an *implementation specific* bundled .NET object, including (as options) type-metadata, identity/re-use, etc.
            </summary>
        </member>
        <member name="M:ProtoBuf.BclHelpers.WriteNetObject(System.Object,ProtoBuf.ProtoWriter,System.Int32,ProtoBuf.BclHelpers.NetObjectOptions)">
            <summary>
            Writes an *implementation specific* bundled .NET object, including (as options) type-metadata, identity/re-use, etc.
            </summary>
        </member>
        <member name="T:ProtoBuf.BufferExtension">
            <summary>
            Provides a simple buffer-based implementation of an <see cref="T:ProtoBuf.IExtension">extension</see> object.
            </summary>
        </member>
        <member name="F:ProtoBuf.BufferPool.MaxByteArraySize">
            <remarks>
            https://docs.microsoft.com/en-us/dotnet/framework/configure-apps/file-schema/runtime/gcallowverylargeobjects-element
            </remarks>
        </member>
        <member name="T:ProtoBuf.ProtoBeforeSerializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked before serialization.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoAfterSerializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked after serialization.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoBeforeDeserializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked before deserialization.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoAfterDeserializationAttribute">
            <summary>Specifies a method on the root-contract in an hierarchy to be invoked after deserialization.</summary>
        </member>
        <member name="M:ProtoBuf.Compiler.CompilerContext.LoadNullRef">
            <summary>
            Pushes a null reference onto the stack. Note that this should only
            be used to return a null (or set a variable to null); for null-tests
            use BranchIfTrue / BranchIfFalse.
            </summary>
        </member>
        <member name="M:ProtoBuf.Compiler.CompilerContext.UsingBlock.#ctor(ProtoBuf.Compiler.CompilerContext,ProtoBuf.Compiler.Local)">
            <summary>
            Creates a new "using" block (equivalent) around a variable;
            the variable must exist, and note that (unlike in C#) it is
            the variables *final* value that gets disposed. If you need
            *original* disposal, copy your variable first.
            
            It is the callers responsibility to ensure that the variable's
            scope fully-encapsulates the "using"; if not, the variable
            may be re-used (and thus re-assigned) unexpectedly.
            </summary>
        </member>
        <member name="T:ProtoBuf.DataFormat">
            <summary>
            Sub-format to use when serializing/deserializing data
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.Default">
            <summary>
            Uses the default encoding for the data-type.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.ZigZag">
            <summary>
            When applied to signed integer-based data (including Decimal), this
            indicates that zigzag variant encoding will be used. This means that values
            with small magnitude (regardless of sign) take a small amount
            of space to encode.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.TwosComplement">
            <summary>
            When applied to signed integer-based data (including Decimal), this
            indicates that two's-complement variant encoding will be used.
            This means that any -ve number will take 10 bytes (even for 32-bit),
            so should only be used for compatibility.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.FixedSize">
            <summary>
            When applied to signed integer-based data (including Decimal), this
            indicates that a fixed amount of space will be used.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.Group">
            <summary>
            When applied to a sub-message, indicates that the value should be treated
            as group-delimited.
            </summary>
        </member>
        <member name="F:ProtoBuf.DataFormat.WellKnown">
            <summary>
            When applied to members of types such as DateTime or TimeSpan, specifies
            that the "well known" standardized representation should be use; DateTime uses Timestamp,
            
            </summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnionObject">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnionObject.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnionObject.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnionObject.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnionObject.Reset(ProtoBuf.DiscriminatedUnionObject@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnionObject.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion64">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64.Reset(ProtoBuf.DiscriminatedUnion64@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion64.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion128Object">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Guid">
            <summary>The value typed as Guid</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128Object.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.#ctor(System.Int32,System.Nullable{System.Guid})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128Object.Reset(ProtoBuf.DiscriminatedUnion128Object@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion128Object.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion128">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion128.Guid">
            <summary>The value typed as Guid</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.#ctor(System.Int32,System.Nullable{System.Guid})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion128.Reset(ProtoBuf.DiscriminatedUnion128@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion128.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion64Object">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Int64">
            <summary>The value typed as Int64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.UInt64">
            <summary>The value typed as UInt64</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Double">
            <summary>The value typed as Double</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.DateTime">
            <summary>The value typed as DateTime</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.TimeSpan">
            <summary>The value typed as TimeSpan</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion64Object.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Int64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.UInt64)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Double)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Nullable{System.DateTime})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.#ctor(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion64Object.Reset(ProtoBuf.DiscriminatedUnion64Object@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion64Object.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion32">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32.Reset(ProtoBuf.DiscriminatedUnion32@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion32.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.DiscriminatedUnion32Object">
            <summary>Represent multiple types as a union; this is used as part of OneOf -
            note that it is the caller's responsbility to only read/write the value as the same type</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Int32">
            <summary>The value typed as Int32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.UInt32">
            <summary>The value typed as UInt32</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Boolean">
            <summary>The value typed as Boolean</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Single">
            <summary>The value typed as Single</summary>
        </member>
        <member name="F:ProtoBuf.DiscriminatedUnion32Object.Object">
            <summary>The value typed as Object</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.Is(System.Int32)">
            <summary>Indicates whether the specified discriminator is assigned</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Int32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.UInt32)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Single)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Boolean)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.#ctor(System.Int32,System.Object)">
            <summary>Create a new discriminated union value</summary>
        </member>
        <member name="M:ProtoBuf.DiscriminatedUnion32Object.Reset(ProtoBuf.DiscriminatedUnion32Object@,System.Int32)">
            <summary>Reset a value if the specified discriminator is assigned</summary>
        </member>
        <member name="P:ProtoBuf.DiscriminatedUnion32Object.Discriminator">
            <summary>The discriminator value</summary>
        </member>
        <member name="T:ProtoBuf.Extensible">
            <summary>
            Simple base class for supporting unexpected fields allowing
            for loss-less round-tips/merge, even if the data is not understod.
            The additional fields are (by default) stored in-memory in a buffer.
            </summary>
            <remarks>As an example of an alternative implementation, you might
            choose to use the file system (temporary files) as the back-end, tracking
            only the paths [such an object would ideally be IDisposable and use
            a finalizer to ensure that the files are removed].</remarks>
            <seealso cref="T:ProtoBuf.IExtensible"/>
        </member>
        <member name="M:ProtoBuf.Extensible.GetExtensionObject(System.Boolean)">
            <summary>
            Retrieves the <see cref="T:ProtoBuf.IExtension">extension</see> object for the current
            instance, optionally creating it if it does not already exist.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="M:ProtoBuf.Extensible.GetExtensionObject(ProtoBuf.IExtension@,System.Boolean)">
            <summary>
            Provides a simple, default implementation for <see cref="T:ProtoBuf.IExtension">extension</see> support,
            optionally creating it if it does not already exist. Designed to be called by
            classes implementing <see cref="T:ProtoBuf.IExtensible"/>.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <param name="extensionObject">The extension field to check (and possibly update).</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="M:ProtoBuf.Extensible.AppendValue``1(ProtoBuf.IExtensible,System.Int32,``0)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <typeparam name="TValue">The type of the value to append.</typeparam>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
        </member>
        <member name="M:ProtoBuf.Extensible.AppendValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,``0)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="format">The data-format to use when encoding the value.</param>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValue``1(ProtoBuf.IExtensible,System.Int32)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned is the composed value after merging any duplicated content; if the
            value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <returns>The effective value of the field, or the default value if not found.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned is the composed value after merging any duplicated content; if the
            value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>The effective value of the field, or the default value if not found.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue``1(ProtoBuf.IExtensible,System.Int32,``0@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,``0@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,``0@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="allowDefinedTag">Allow tags that are present as part of the definition; for example, to query unknown enum values.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValues``1(ProtoBuf.IExtensible,System.Int32)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValues``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <typeparam name="TValue">The data-type of the field.</typeparam>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.TryGetValue(ProtoBuf.Meta.TypeModel,System.Type,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,System.Object@)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            The value returned (in "value") is the composed value after merging any duplicated content;
            if the value is "repeated" (a list), then use GetValues instead.
            </summary>
            <param name="type">The data-type of the field.</param>
            <param name="model">The model to use for configuration.</param>
            <param name="value">The effective value of the field, or the default value if not found.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <param name="allowDefinedTag">Allow tags that are present as part of the definition; for example, to query unknown enum values.</param>
            <returns>True if data for the field was present, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.GetValues(ProtoBuf.Meta.TypeModel,System.Type,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat)">
            <summary>
            Queries an extensible object for an additional (unexpected) data-field for the instance.
            Each occurrence of the field is yielded separately, making this usage suitable for "repeated"
            (list) fields.
            </summary>
            <remarks>The extended data is processed lazily as the enumerator is iterated.</remarks>
            <param name="model">The model to use for configuration.</param>
            <param name="type">The data-type of the field.</param>
            <param name="instance">The extensible object to obtain the value from.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="format">The data-format to use when decoding the value.</param>
            <returns>An enumerator that yields each occurrence of the field.</returns>
        </member>
        <member name="M:ProtoBuf.Extensible.AppendValue(ProtoBuf.Meta.TypeModel,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Object)">
            <summary>
            Appends the value as an additional (unexpected) data-field for the instance.
            Note that for non-repeated sub-objects, this equates to a merge operation;
            for repeated sub-objects this adds a new instance to the set; for simple
            values the new value supercedes the old value.
            </summary>
            <remarks>Note that appending a value does not remove the old value from
            the stream; avoid repeatedly appending values for the same field.</remarks>
            <param name="model">The model to use for configuration.</param>
            <param name="format">The data-format to use when encoding the value.</param>
            <param name="instance">The extensible object to append the value to.</param>
            <param name="tag">The field identifier; the tag should not be defined as a known data-field for the instance.</param>
            <param name="value">The value to append.</param>
        </member>
        <member name="T:ProtoBuf.ExtensibleUtil">
            <summary>
            This class acts as an internal wrapper allowing us to do a dynamic
            methodinfo invoke; an't put into Serializer as don't want on public
            API; can't put into Serializer&lt;T&gt; since we need to invoke
            across classes
            </summary>
        </member>
        <member name="M:ProtoBuf.ExtensibleUtil.GetExtendedValues``1(ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,System.Boolean)">
            <summary>
            All this does is call GetExtendedValuesTyped with the correct type for "instance";
            this ensures that we don't get issues with subclasses declaring conflicting types -
            the caller must respect the fields defined for the type they pass in.
            </summary>
        </member>
        <member name="M:ProtoBuf.ExtensibleUtil.GetExtendedValues(ProtoBuf.Meta.TypeModel,System.Type,ProtoBuf.IExtensible,System.Int32,ProtoBuf.DataFormat,System.Boolean,System.Boolean)">
            <summary>
            All this does is call GetExtendedValuesTyped with the correct type for "instance";
            this ensures that we don't get issues with subclasses declaring conflicting types -
            the caller must respect the fields defined for the type they pass in.
            </summary>
        </member>
        <member name="T:ProtoBuf.Helpers">
            <summary>
            Not all frameworks are created equal (fx1.1 vs fx2.0,
            micro-framework, compact-framework,
            silverlight, etc). This class simply wraps up a few things that would
            otherwise make the real code unnecessarily messy, providing fallback
            implementations if necessary.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoTypeCode">
            <summary>
            Intended to be a direct map to regular TypeCode, but:
            - with missing types
            - existing on WinRT
            </summary>
        </member>
        <member name="T:ProtoBuf.IExtensible">
            <summary>
            Indicates that the implementing type has support for protocol-buffer
            <see cref="T:ProtoBuf.IExtension">extensions</see>.
            </summary>
            <remarks>Can be implemented by deriving from Extensible.</remarks>
        </member>
        <member name="M:ProtoBuf.IExtensible.GetExtensionObject(System.Boolean)">
            <summary>
            Retrieves the <see cref="T:ProtoBuf.IExtension">extension</see> object for the current
            instance, optionally creating it if it does not already exist.
            </summary>
            <param name="createIfMissing">Should a new extension object be
            created if it does not already exist?</param>
            <returns>The extension object if it exists (or was created), or null
            if the extension object does not exist or is not available.</returns>
            <remarks>The <c>createIfMissing</c> argument is false during serialization,
            and true during deserialization upon encountering unexpected fields.</remarks>
        </member>
        <member name="T:ProtoBuf.IExtension">
            <summary>
            Provides addition capability for supporting unexpected fields during
            protocol-buffer serialization/deserialization. This allows for loss-less
            round-trip/merge, even when the data is not fully understood.
            </summary>
        </member>
        <member name="M:ProtoBuf.IExtension.BeginAppend">
            <summary>
            Requests a stream into which any unexpected fields can be persisted.
            </summary>
            <returns>A new stream suitable for storing data.</returns>
        </member>
        <member name="M:ProtoBuf.IExtension.EndAppend(System.IO.Stream,System.Boolean)">
            <summary>
            Indicates that all unexpected fields have now been stored. The
            implementing class is responsible for closing the stream. If
            "commit" is not true the data may be discarded.
            </summary>
            <param name="stream">The stream originally obtained by BeginAppend.</param>
            <param name="commit">True if the append operation completed successfully.</param>
        </member>
        <member name="M:ProtoBuf.IExtension.BeginQuery">
            <summary>
            Requests a stream of the unexpected fields previously stored.
            </summary>
            <returns>A prepared stream of the unexpected fields.</returns>
        </member>
        <member name="M:ProtoBuf.IExtension.EndQuery(System.IO.Stream)">
            <summary>
            Indicates that all unexpected fields have now been read. The
            implementing class is responsible for closing the stream.
            </summary>
            <param name="stream">The stream originally obtained by BeginQuery.</param>
        </member>
        <member name="M:ProtoBuf.IExtension.GetLength">
            <summary>
            Requests the length of the raw binary stream; this is used
            when serializing sub-entities to indicate the expected size.
            </summary>
            <returns>The length of the binary stream representing unexpected data.</returns>
        </member>
        <member name="T:ProtoBuf.IExtensionResettable">
            <summary>
            Provides the ability to remove all existing extension data
            </summary>
        </member>
        <member name="M:ProtoBuf.IExtensionResettable.Reset">
            <summary>
            Remove all existing extension data
            </summary>
        </member>
        <member name="T:ProtoBuf.ImplicitFields">
            <summary>
            Specifies the method used to infer field tags for members of the type
            under consideration. Tags are deduced using the invariant alphabetic
            sequence of the members' names; this makes implicit field tags very brittle,
            and susceptible to changes such as field names (normally an isolated
            change).
            </summary>
        </member>
        <member name="F:ProtoBuf.ImplicitFields.None">
            <summary>
            No members are serialized implicitly; all members require a suitable
            attribute such as [ProtoMember]. This is the recmomended mode for
            most scenarios.
            </summary>
        </member>
        <member name="F:ProtoBuf.ImplicitFields.AllPublic">
            <summary>
            Public properties and fields are eligible for implicit serialization;
            this treats the public API as a contract. Ordering beings from ImplicitFirstTag.
            </summary>
        </member>
        <member name="F:ProtoBuf.ImplicitFields.AllFields">
            <summary>
            Public and non-public fields are eligible for implicit serialization;
            this acts as a state/implementation serializer. Ordering beings from ImplicitFirstTag.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.CallbackSet">
            <summary>
            Represents the set of serialization callbacks to be used when serializing/deserializing a type.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.CallbackSet.BeforeSerialize">
            <summary>Called before serializing an instance</summary>
        </member>
        <member name="P:ProtoBuf.Meta.CallbackSet.BeforeDeserialize">
            <summary>Called before deserializing an instance</summary>
        </member>
        <member name="P:ProtoBuf.Meta.CallbackSet.AfterSerialize">
            <summary>Called after serializing an instance</summary>
        </member>
        <member name="P:ProtoBuf.Meta.CallbackSet.AfterDeserialize">
            <summary>Called after deserializing an instance</summary>
        </member>
        <member name="P:ProtoBuf.Meta.CallbackSet.NonTrivial">
            <summary>
            True if any callback is set, else False
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.MetaType">
            <summary>
            Represents a type at runtime for use with protobuf, allowing the field mappings (etc) to be defined
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.ToString">
            <summary>
            Get the name of the type being represented
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.BaseType">
            <summary>
            Gets the base-type for this type
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.IncludeSerializerMethod">
            <summary>
            When used to compile a model, should public serialization/deserialzation methods
            be included for this type?
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.AsReferenceDefault">
            <summary>
            Should this type be treated as a reference by default?
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.AddSubType(System.Int32,System.Type)">
            <summary>
            Adds a known sub-type to the inheritance model
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.AddSubType(System.Int32,System.Type,ProtoBuf.DataFormat)">
            <summary>
            Adds a known sub-type to the inheritance model
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.HasCallbacks">
            <summary>
            Indicates whether the current type has defined callbacks 
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.HasSubtypes">
            <summary>
            Indicates whether the current type has defined subtypes
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.Callbacks">
            <summary>
            Returns the set of callbacks defined for this type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.SetCallbacks(System.Reflection.MethodInfo,System.Reflection.MethodInfo,System.Reflection.MethodInfo,System.Reflection.MethodInfo)">
            <summary>
            Assigns the callbacks to use during serialiation/deserialization.
            </summary>
            <param name="beforeSerialize">The method (or null) called before serialization begins.</param>
            <param name="afterSerialize">The method (or null) called when serialization is complete.</param>
            <param name="beforeDeserialize">The method (or null) called before deserialization begins (or when a new instance is created during deserialization).</param>
            <param name="afterDeserialize">The method (or null) called when deserialization is complete.</param>
            <returns>The set of callbacks.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.SetCallbacks(System.String,System.String,System.String,System.String)">
            <summary>
            Assigns the callbacks to use during serialiation/deserialization.
            </summary>
            <param name="beforeSerialize">The name of the method (or null) called before serialization begins.</param>
            <param name="afterSerialize">The name of the method (or null) called when serialization is complete.</param>
            <param name="beforeDeserialize">The name of the method (or null) called before deserialization begins (or when a new instance is created during deserialization).</param>
            <param name="afterDeserialize">The name of the method (or null) called when deserialization is complete.</param>
            <returns>The set of callbacks.</returns>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.Name">
            <summary>
            Gets or sets the name of this contract.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.SetFactory(System.Reflection.MethodInfo)">
            <summary>
            Designate a factory-method to use to create instances of this type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.SetFactory(System.String)">
            <summary>
            Designate a factory-method to use to create instances of this type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.ThrowIfFrozen">
            <summary>
            Throws an exception if the type has been made immutable
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.Type">
            <summary>
            The runtime type that the meta-type represents
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.Add(System.Int32,System.String)">
            <summary>
            Adds a member (by name) to the MetaType
            </summary>        
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.AddField(System.Int32,System.String)">
            <summary>
            Adds a member (by name) to the MetaType, returning the ValueMember rather than the fluent API.
            This is otherwise identical to Add.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.UseConstructor">
            <summary>
            Gets or sets whether the type should use a parameterless constructor (the default),
            or whether the type should skip the constructor completely. This option is not supported
            on compact-framework.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.ConstructType">
            <summary>
            The concrete type to create when a new instance of this type is needed; this may be useful when dealing
            with dynamic proxies, or with interface-based APIs
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.Add(System.String)">
            <summary>
            Adds a member (by name) to the MetaType
            </summary>     
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.SetSurrogate(System.Type)">
            <summary>
            Performs serialization of this type via a surrogate; all
            other serialization options are ignored and handled
            by the surrogate's configuration.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.Add(System.String[])">
            <summary>
            Adds a set of members (by name) to the MetaType
            </summary>     
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.Add(System.Int32,System.String,System.Object)">
            <summary>
            Adds a member (by name) to the MetaType
            </summary>        
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.Add(System.Int32,System.String,System.Type,System.Type)">
            <summary>
            Adds a member (by name) to the MetaType, including an itemType and defaultType for representing lists
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.AddField(System.Int32,System.String,System.Type,System.Type)">
            <summary>
            Adds a member (by name) to the MetaType, including an itemType and defaultType for representing lists, returning the ValueMember rather than the fluent API.
            This is otherwise identical to Add.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.Item(System.Int32)">
            <summary>
            Returns the ValueMember that matchs a given field number, or null if not found
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.Item(System.Reflection.MemberInfo)">
            <summary>
            Returns the ValueMember that matchs a given member (property/field), or null if not found
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.GetFields">
            <summary>
            Returns the ValueMember instances associated with this type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.GetSubtypes">
            <summary>
            Returns the SubType instances associated with this type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.MetaType.CompileInPlace">
            <summary>
            Compiles the serializer for this type; this is *not* a full
            standalone compile, but can significantly boost performance
            while allowing additional types to be added.
            </summary>
            <remarks>An in-place compile can access non-public types / members</remarks>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.EnumPassthru">
            <summary>
            Gets or sets a value indicating that an enum should be treated directly as an int/short/etc, rather
            than enforcing .proto enum rules. This is useful *in particul* for [Flags] enums.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.IgnoreListHandling">
            <summary>
            Gets or sets a value indicating that this type should NOT be treated as a list, even if it has
            familiar list-like characteristics (enumerable, add, etc)
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.MetaType.IsGroup">
            <summary>
            Indicates whether this type should always be treated as a "group" (rather than a string-prefixed sub-message)
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.ProtoSyntax">
            <summary>
            Indiate the variant of the protobuf .proto DSL syntax to use
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.ProtoSyntax.Proto2">
            <summary>
            https://developers.google.com/protocol-buffers/docs/proto
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.ProtoSyntax.Proto3">
            <summary>
            https://developers.google.com/protocol-buffers/docs/proto3
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.RuntimeTypeModel">
            <summary>
            Provides protobuf serialization support for a number of types that can be defined at runtime
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.InferTagFromNameDefault">
            <summary>
            Global default that
            enables/disables automatic tag generation based on the existing name / order
            of the defined members. See <seealso cref="P:ProtoBuf.ProtoContractAttribute.InferTagFromName"/>
            for usage and <b>important warning</b> / explanation.
            You must set the global default before attempting to serialize/deserialize any
            impacted type.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.AutoAddProtoContractTypesOnly">
            <summary>
            Global default that determines whether types are considered serializable
            if they have [DataContract] / [XmlType]. With this enabled, <b>ONLY</b>
            types marked as [ProtoContract] are added automatically.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.UseImplicitZeroDefaults">
            <summary>
            Global switch that enables or disables the implicit
            handling of "zero defaults"; meanning: if no other default is specified,
            it assumes bools always default to false, integers to zero, etc.
            
            If this is disabled, no such assumptions are made and only *explicit*
            default values are processed. This is enabled by default to 
            preserve similar logic to v1.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.AllowParseableTypes">
            <summary>
            Global switch that determines whether types with a <c>.ToString()</c> and a <c>Parse(string)</c>
            should be serialized as strings.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.IncludeDateTimeKind">
            <summary>
            Global switch that determines whether DateTime serialization should include the <c>Kind</c> of the date/time.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.InternStrings">
            <summary>
            Global switch that determines whether a single instance of the same string should be used during deserialization.
            </summary>
            <remarks>Note this does not use the global .NET string interner</remarks>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.SerializeDateTimeKind">
            <summary>
            Should the <c>Kind</c> be included on date/time values?
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.Default">
            <summary>
            The default model, used to support ProtoBuf.Serializer
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.GetTypes">
            <summary>
            Returns a sequence of the Type instances that can be
            processed by this model.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.GetSchema(System.Type,ProtoBuf.Meta.ProtoSyntax)">
            <summary>
            Suggest a .proto definition for the given type
            </summary>
            <param name="type">The type to generate a .proto definition for, or <c>null</c> to generate a .proto that represents the entire model</param>
            <returns>The .proto definition as a string</returns>
            <param name="syntax">The .proto syntax to use</param>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.Item(System.Type)">
            <summary>
            Obtains the MetaType associated with a given Type for the current model,
            allowing additional configuration.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.Add(System.Type,System.Boolean)">
            <summary>
            Adds support for an additional type in this model, optionally
            applying inbuilt patterns. If the type is already known to the
            model, the existing type is returned **without** applying
            any additional behaviour.
            </summary>
            <remarks>Inbuilt patterns include:
            [ProtoContract]/[ProtoMember(n)]
            [DataContract]/[DataMember(Order=n)]
            [XmlType]/[XmlElement(Order=n)]
            [On{Des|S}erializ{ing|ed}]
            ShouldSerialize*/*Specified
            </remarks>
            <param name="type">The type to be supported</param>
            <param name="applyDefaultBehaviour">Whether to apply the inbuilt configuration patterns (via attributes etc), or
            just add the type with no additional configuration (the type must then be manually configured).</param>
            <returns>The MetaType representing this type, allowing
            further configuration.</returns>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.AutoCompile">
            <summary>
            Should serializers be compiled on demand? It may be useful
            to disable this for debugging purposes.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.AutoAddMissingTypes">
            <summary>
            Should support for unexpected types be added automatically?
            If false, an exception is thrown when unexpected types
            are encountered.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.ThrowIfFrozen">
            <summary>
            Verifies that the model is still open to changes; if not, an exception is thrown
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.Freeze">
            <summary>
            Prevents further changes to this model
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.GetKeyImpl(System.Type)">
            <summary>
            Provides the key that represents a given type in the current model.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.Serialize(System.Int32,System.Object,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="key">Represents the type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.Deserialize(System.Int32,System.Object,ProtoBuf.ProtoReader)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="key">Represents the type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.CompileInPlace">
            <summary>
            Compiles the serializers individually; this is *not* a full
            standalone compile, but can significantly boost performance
            while allowing additional types to be added.
            </summary>
            <remarks>An in-place compile can access non-public types / members</remarks>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.Compile">
            <summary>
            Fully compiles the current model into a static-compiled model instance
            </summary>
            <remarks>A full compilation is restricted to accessing public types / members</remarks>
            <returns>An instance of the newly created compiled type-model</returns>
        </member>
        <member name="T:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions">
            <summary>
            Represents configuration options for compiling a model to 
            a standalone assembly.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.SetFrameworkOptions(ProtoBuf.Meta.MetaType)">
            <summary>
            Import framework options from an existing type
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.TargetFrameworkName">
            <summary>
            The TargetFrameworkAttribute FrameworkName value to burn into the generated assembly
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.TargetFrameworkDisplayName">
            <summary>
            The TargetFrameworkAttribute FrameworkDisplayName value to burn into the generated assembly
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.TypeName">
            <summary>
            The name of the TypeModel class to create
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.OutputPath">
            <summary>
            The path for the new dll
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.ImageRuntimeVersion">
            <summary>
            The runtime version for the generated assembly
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.MetaDataVersion">
            <summary>
            The runtime version for the generated assembly
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions.Accessibility">
            <summary>
            The acecssibility of the generated serializer
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.RuntimeTypeModel.Accessibility">
            <summary>
            Type accessibility
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.RuntimeTypeModel.Accessibility.Public">
            <summary>
            Available to all callers
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.RuntimeTypeModel.Accessibility.Internal">
            <summary>
            Available to all callers in the same assembly, or assemblies specified via [InternalsVisibleTo(...)]
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.Compile(System.String,System.String)">
            <summary>
            Fully compiles the current model into a static-compiled serialization dll
            (the serialization dll still requires protobuf-net for support services).
            </summary>
            <remarks>A full compilation is restricted to accessing public types / members</remarks>
            <param name="name">The name of the TypeModel class to create</param>
            <param name="path">The path for the new dll</param>
            <returns>An instance of the newly created compiled type-model</returns>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.Compile(ProtoBuf.Meta.RuntimeTypeModel.CompilerOptions)">
            <summary>
            Fully compiles the current model into a static-compiled serialization dll
            (the serialization dll still requires protobuf-net for support services).
            </summary>
            <remarks>A full compilation is restricted to accessing public types / members</remarks>
            <returns>An instance of the newly created compiled type-model</returns>
        </member>
        <member name="P:ProtoBuf.Meta.RuntimeTypeModel.MetadataTimeoutMilliseconds">
            <summary>
            The amount of time to wait if there are concurrent metadata access operations
            </summary>
        </member>
        <member name="E:ProtoBuf.Meta.RuntimeTypeModel.LockContended">
            <summary>
            If a lock-contention is detected, this event signals the *owner* of the lock responsible for the blockage, indicating
            what caused the problem; this is only raised if the lock-owning code successfully completes.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.RuntimeTypeModel.SetDefaultFactory(System.Reflection.MethodInfo)">
            <summary>
            Designate a factory-method to use to create instances of any type; note that this only affect types seen by the serializer *after* setting the factory.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.LockContentedEventArgs">
            <summary>
            Contains the stack-trace of the owning code when a lock-contention scenario is detected
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.LockContentedEventArgs.OwnerStackTrace">
            <summary>
            The stack-trace of the code that owned the lock when a lock-contention scenario occurred
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.LockContentedEventHandler">
            <summary>
            Event-type that is raised when a lock-contention scenario is detected
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.SubType">
            <summary>
            Represents an inherited type in a type hierarchy.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SubType.FieldNumber">
            <summary>
            The field-number that is used to encapsulate the data (as a nested
            message) for the derived dype.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.SubType.DerivedType">
            <summary>
            The sub-type to be considered.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.SubType.#ctor(System.Int32,ProtoBuf.Meta.MetaType,ProtoBuf.DataFormat)">
            <summary>
            Creates a new SubType instance.
            </summary>
            <param name="fieldNumber">The field-number that is used to encapsulate the data (as a nested
            message) for the derived dype.</param>
            <param name="derivedType">The sub-type to be considered.</param>
            <param name="format">Specific encoding style to use; in particular, Grouped can be used to avoid buffering, but is not the default.</param>
        </member>
        <member name="T:ProtoBuf.Meta.TypeFormatEventArgs">
            <summary>
            Event arguments needed to perform type-formatting functions; this could be resolving a Type to a string suitable for serialization, or could
            be requesting a Type from a string. If no changes are made, a default implementation will be used (from the assembly-qualified names).
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.TypeFormatEventArgs.Type">
            <summary>
            The type involved in this map; if this is initially null, a Type is expected to be provided for the string in FormattedName.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.TypeFormatEventArgs.FormattedName">
            <summary>
            The formatted-name involved in this map; if this is initially null, a formatted-name is expected from the type in Type.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.TypeFormatEventHandler">
            <summary>
            Delegate type used to perform type-formatting functions; the sender originates as the type-model.
            </summary>
        </member>
        <member name="T:ProtoBuf.Meta.TypeModel">
            <summary>
            Provides protobuf serialization support for a number of types
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.SerializeDateTimeKind">
            <summary>
            Should the <c>Kind</c> be included on date/time values?
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.MapType(System.Type)">
            <summary>
            Resolve a System.Type to the compiler-specific type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.MapType(System.Type,System.Boolean)">
            <summary>
            Resolve a System.Type to the compiler-specific type
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.TrySerializeAuxiliaryType(ProtoBuf.ProtoWriter,System.Type,ProtoBuf.DataFormat,System.Int32,System.Object,System.Boolean,System.Object)">
            <summary>
            This is the more "complete" version of Serialize, which handles single instances of mapped types.
            The value is written as a complete field, including field-header and (for sub-objects) a
            length-prefix
            In addition to that, this provides support for:
             - basic values; individual int / string / Guid / etc
             - IEnumerable sequences of any type handled by TrySerializeAuxiliaryType
             
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(System.IO.Stream,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(System.IO.Stream,System.Object,ProtoBuf.SerializationContext)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(ProtoBuf.ProtoWriter,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied writer.
            </summary>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination writer to write to.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.Serializer.TypeResolver)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="expectedField">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="resolver">Used to resolve types on a per-field basis.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.Serializer.TypeResolver,System.Int32@)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="expectedField">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="resolver">Used to resolve types on a per-field basis.</param>
            <param name="bytesRead">Returns the number of bytes consumed by this operation (includes length-prefix overheads and any skipped data).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.Serializer.TypeResolver,System.Int64@)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="type">The type being merged.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="expectedField">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="resolver">Used to resolve types on a per-field basis.</param>
            <param name="bytesRead">Returns the number of bytes consumed by this operation (includes length-prefix overheads and any skipped data).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems(System.IO.Stream,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.Serializer.TypeResolver)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Serializer.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <param name="resolver">On a field-by-field basis, the type of object to deserialize (can be null if "type" is specified). </param>
            <param name="type">The type of object to deserialize (can be null if "resolver" is specified).</param>
            <returns>The sequence of deserialized objects.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems(System.IO.Stream,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.Serializer.TypeResolver,ProtoBuf.SerializationContext)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Serializer.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <param name="resolver">On a field-by-field basis, the type of object to deserialize (can be null if "type" is specified). </param>
            <param name="type">The type of object to deserialize (can be null if "resolver" is specified).</param>
            <returns>The sequence of deserialized objects.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems``1(System.IO.Stream,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Serializer.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <typeparam name="T">The type of object to deserialize.</typeparam>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <returns>The sequence of deserialized objects.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeserializeItems``1(System.IO.Stream,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.SerializationContext)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Serializer.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignores for fixed-length prefixes.
            </summary>
            <typeparam name="T">The type of object to deserialize.</typeparam>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="expectedField">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <returns>The sequence of deserialized objects.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.SerializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream,
            with a length-prefix. This is useful for socket programming,
            as DeserializeWithLengthPrefix can be used to read the single object back
            from an ongoing stream.
            </summary>
            <param name="type">The type being serialized.</param>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.SerializeWithLengthPrefix(System.IO.Stream,System.Object,System.Type,ProtoBuf.PrefixStyle,System.Int32,ProtoBuf.SerializationContext)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream,
            with a length-prefix. This is useful for socket programming,
            as DeserializeWithLengthPrefix can be used to read the single object back
            from an ongoing stream.
            </summary>
            <param name="type">The type being serialized.</param>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="dest">The destination stream to write to.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,ProtoBuf.SerializationContext)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int32)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int64)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int32,ProtoBuf.SerializationContext)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume (or -1 to read to the end of the stream).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.IO.Stream,System.Object,System.Type,System.Int64,ProtoBuf.SerializationContext)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="length">The number of bytes to consume (or -1 to read to the end of the stream).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(ProtoBuf.ProtoReader,System.Object,System.Type)">
            <summary>
            Applies a protocol-buffer reader to an existing instance (which may be null).
            </summary>
            <param name="type">The type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The reader to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.TryDeserializeAuxiliaryType(ProtoBuf.ProtoReader,ProtoBuf.DataFormat,System.Int32,System.Type,System.Object@,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Object)">
            <summary>
            This is the more "complete" version of Deserialize, which handles single instances of mapped types.
            The value is read as a complete field, including field-header and (for sub-objects) a
            length-prefix..kmc  
            
            In addition to that, this provides support for:
             - basic values; individual int / string / Guid / etc
             - IList sets of any type handled by TryDeserializeAuxiliaryType
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Create">
            <summary>
            Creates a new runtime model, to which the caller
            can add support for a range of types. A model
            can be used "as is", or can be compiled for
            optimal performance.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ResolveProxies(System.Type)">
            <summary>
            Applies common proxy scenarios, resolving the actual type to consider
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.IsDefined(System.Type)">
            <summary>
            Indicates whether the supplied type is explicitly modelled by the model
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetKey(System.Type@)">
            <summary>
            Provides the key that represents a given type in the current model.
            The type is also normalized for proxies at the same time.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ResetKeyCache">
            <summary>
            Advertise that a type's key can have changed
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetKeyImpl(System.Type)">
            <summary>
            Provides the key that represents a given type in the current model.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Serialize(System.Int32,System.Object,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="key">Represents the type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.Deserialize(System.Int32,System.Object,ProtoBuf.ProtoReader)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (which may be null).
            </summary>
            <param name="key">Represents the type (including inheritance) to consider.</param>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="T:ProtoBuf.Meta.TypeModel.CallbackType">
            <summary>
            Indicates the type of callback to be used
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.BeforeSerialize">
            <summary>
            Invoked before an object is serialized
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.AfterSerialize">
            <summary>
            Invoked after an object is serialized
            </summary>
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.BeforeDeserialize">
            <summary>
            Invoked before an object is deserialized (or when a new instance is created)
            </summary>            
        </member>
        <member name="F:ProtoBuf.Meta.TypeModel.CallbackType.AfterDeserialize">
            <summary>
            Invoked after an object is deserialized
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.DeepClone(System.Object)">
            <summary>
            Create a deep clone of the supplied instance; any sub-items are also cloned.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowUnexpectedSubtype(System.Type,System.Type)">
            <summary>
            Indicates that while an inheritance tree exists, the exact type encountered was not
            specified in that hierarchy and cannot be processed.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowUnexpectedType(System.Type)">
            <summary>
            Indicates that the given type was not expected, and cannot be processed.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.ThrowCannotCreateInstance(System.Type)">
            <summary>
            Indicates that the given type cannot be constructed; it may still be possible to 
            deserialize into existing instances.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CanSerializeContractType(System.Type)">
            <summary>
            Returns true if the type supplied is either a recognised contract type,
            or a *list* of a recognised contract type. 
            </summary>
            <remarks>Note that primitives always return false, even though the engine
            will, if forced, try to serialize such</remarks>
            <returns>True if this type is recognised as a serializable entity, else false</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CanSerialize(System.Type)">
            <summary>
            Returns true if the type supplied is a basic type with inbuilt handling,
            a recognised contract type, or a *list* of a basic / contract type. 
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CanSerializeBasicType(System.Type)">
            <summary>
            Returns true if the type supplied is a basic type with inbuilt handling,
            or a *list* of a basic type with inbuilt handling
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetSchema(System.Type)">
            <summary>
            Suggest a .proto definition for the given type
            </summary>
            <param name="type">The type to generate a .proto definition for, or <c>null</c> to generate a .proto that represents the entire model</param>
            <returns>The .proto definition as a string</returns>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.GetSchema(System.Type,ProtoBuf.Meta.ProtoSyntax)">
            <summary>
            Suggest a .proto definition for the given type
            </summary>
            <param name="type">The type to generate a .proto definition for, or <c>null</c> to generate a .proto that represents the entire model</param>
            <returns>The .proto definition as a string</returns>
            <param name="syntax">The .proto syntax to use for the operation</param>
        </member>
        <member name="E:ProtoBuf.Meta.TypeModel.DynamicTypeFormatting">
            <summary>
            Used to provide custom services for writing and parsing type names when using dynamic types. Both parsing and formatting
            are provided on a single API as it is essential that both are mapped identically at all times.
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.TypeModel.CreateFormatter(System.Type)">
            <summary>
            Creates a new IFormatter that uses protocol-buffer [de]serialization.
            </summary>
            <returns>A new IFormatter to be used during [de]serialization.</returns>
            <param name="type">The type of object to be [de]deserialized by the formatter.</param>
        </member>
        <member name="T:ProtoBuf.Meta.ValueMember">
            <summary>
            Represents a member (property/field) that is mapped to a protobuf field
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.FieldNumber">
            <summary>
            The number that identifies this member in a protobuf stream
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.Member">
            <summary>
            Gets the member (field/property) which this member relates to.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.BackingMember">
            <summary>
            Gets the backing member (field/property) which this member relates to
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.ItemType">
            <summary>
            Within a list / array / etc, the type of object for each item in the list (especially useful with ArrayList)
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.MemberType">
            <summary>
            The underlying type of the member
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.DefaultType">
            <summary>
            For abstract types (IList etc), the type of concrete object to create (if required)
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.ParentType">
            <summary>
            The type the defines the member
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.DefaultValue">
            <summary>
            The default value of the item (members with this value will not be serialized)
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.ValueMember.#ctor(ProtoBuf.Meta.RuntimeTypeModel,System.Type,System.Int32,System.Reflection.MemberInfo,System.Type,System.Type,System.Type,ProtoBuf.DataFormat,System.Object)">
            <summary>
            Creates a new ValueMember instance
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.ValueMember.#ctor(ProtoBuf.Meta.RuntimeTypeModel,System.Int32,System.Type,System.Type,System.Type,ProtoBuf.DataFormat)">
            <summary>
            Creates a new ValueMember instance
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.DataFormat">
            <summary>
            Specifies the rules used to process the field; this is used to determine the most appropriate
            wite-type, but also to describe subtypes <i>within</i> that wire-type (such as SignedVariant)
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.IsStrict">
            <summary>
            Indicates whether this field should follow strict encoding rules; this means (for example) that if a "fixed32"
            is encountered when "variant" is defined, then it will fail (throw an exception) when parsing. Note that
            when serializing the defined type is always used.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.IsPacked">
            <summary>
            Indicates whether this field should use packed encoding (which can save lots of space for repeated primitive values).
            This option only applies to list/array data of primitive types (int, double, etc).
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.OverwriteList">
            <summary>
            Indicates whether this field should *repace* existing values (the default is false, meaning *append*).
            This option only applies to list/array data.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.IsRequired">
            <summary>
            Indicates whether this field is mandatory.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.AsReference">
            <summary>
            Enables full object-tracking/full-graph support.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.DynamicType">
            <summary>
            Embeds the type information into the stream, allowing usage with types not known in advance.
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.IsMap">
            <summary>
            Indicates that the member should be treated as a protobuf Map
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.MapKeyFormat">
            <summary>
            Specifies the data-format that should be used for the key, when IsMap is enabled
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.MapValueFormat">
            <summary>
            Specifies the data-format that should be used for the value, when IsMap is enabled
            </summary>
        </member>
        <member name="M:ProtoBuf.Meta.ValueMember.SetSpecified(System.Reflection.MethodInfo,System.Reflection.MethodInfo)">
            <summary>
            Specifies methods for working with optional data members.
            </summary>
            <param name="getSpecified">Provides a method (null for none) to query whether this member should
            be serialized; it must be of the form "bool {Method}()". The member is only serialized if the
            method returns true.</param>
            <param name="setSpecified">Provides a method (null for none) to indicate that a member was
            deserialized; it must be of the form "void {Method}(bool)", and will be called with "true"
            when data is found.</param>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.Name">
            <summary>
            Gets the logical name for this member in the schema (this is not critical for binary serialization, but may be used
            when inferring a schema).
            </summary>
        </member>
        <member name="P:ProtoBuf.Meta.ValueMember.SupportNull">
            <summary>
            Should lists have extended support for null values? Note this makes the serialization less efficient.
            </summary>
        </member>
        <member name="T:ProtoBuf.PrefixStyle">
            <summary>
            Specifies the type of prefix that should be applied to messages.
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.None">
            <summary>
            No length prefix is applied to the data; the data is terminated only be the end of the stream.
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.Base128">
            <summary>
            A base-128 ("varint", the default prefix format in protobuf) length prefix is applied to the data (efficient for short messages).
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.Fixed32">
            <summary>
            A fixed-length (little-endian) length prefix is applied to the data (useful for compatibility).
            </summary>
        </member>
        <member name="F:ProtoBuf.PrefixStyle.Fixed32BigEndian">
            <summary>
            A fixed-length (big-endian) length prefix is applied to the data (useful for compatibility).
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoContractAttribute">
            <summary>
            Indicates that a type is defined for protocol-buffer serialization.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.Name">
            <summary>
            Gets or sets the defined name of the type.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.ImplicitFirstTag">
            <summary>
            Gets or sets the fist offset to use with implicit field tags;
            only uesd if ImplicitFields is set.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.UseProtoMembersOnly">
            <summary>
            If specified, alternative contract markers (such as markers for XmlSerailizer or DataContractSerializer) are ignored.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.IgnoreListHandling">
            <summary>
            If specified, do NOT treat this type as a list, even if it looks like one.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.ImplicitFields">
            <summary>
            Gets or sets the mechanism used to automatically infer field tags
            for members. This option should be used in advanced scenarios only.
            Please review the important notes against the ImplicitFields enumeration.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.InferTagFromName">
            <summary>
            Enables/disables automatic tag generation based on the existing name / order
            of the defined members. This option is not used for members marked
            with ProtoMemberAttribute, as intended to provide compatibility with
            WCF serialization. WARNING: when adding new fields you must take
            care to increase the Order for new elements, otherwise data corruption
            may occur.
            </summary>
            <remarks>If not explicitly specified, the default is assumed from Serializer.GlobalOptions.InferTagFromName.</remarks>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.InferTagFromNameHasValue">
            <summary>
            Has a InferTagFromName value been explicitly set? if not, the default from the type-model is assumed.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.DataMemberOffset">
            <summary>
            Specifies an offset to apply to [DataMember(Order=...)] markers;
            this is useful when working with mex-generated classes that have
            a different origin (usually 1 vs 0) than the original data-contract.
            
            This value is added to the Order of each member.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.SkipConstructor">
            <summary>
            If true, the constructor for the type is bypassed during deserialization, meaning any field initializers
            or other initialization code is skipped.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.AsReferenceDefault">
            <summary>
            Should this type be treated as a reference by default? Please also see the implications of this,
            as recorded on ProtoMemberAttribute.AsReference
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.IsGroup">
            <summary>
            Indicates whether this type should always be treated as a "group" (rather than a string-prefixed sub-message)
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.EnumPassthru">
            <summary>
            Applies only to enums (not to DTO classes themselves); gets or sets a value indicating that an enum should be treated directly as an int/short/etc, rather
            than enforcing .proto enum rules. This is useful *in particul* for [Flags] enums.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.Surrogate">
            <summary>
            Allows to define a surrogate type used for serialization/deserialization purpose.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoContractAttribute.EnumPassthruHasValue">
            <summary>
            Has a EnumPassthru value been explicitly set?
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoConverterAttribute">
            <summary>
            Indicates that a static member should be considered the same as though
            were an implicit / explicit conversion operator; in particular, this
            is useful for conversions that operator syntax does not allow, such as
            to/from interface types.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoEnumAttribute">
            <summary>
            Used to define protocol-buffer specific behavior for
            enumerated values.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoEnumAttribute.Value">
            <summary>
            Gets or sets the specific value to use for this enum during serialization.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoEnumAttribute.HasValue">
            <summary>
            Indicates whether this instance has a customised value mapping
            </summary>
            <returns>true if a specific value is set</returns>
        </member>
        <member name="P:ProtoBuf.ProtoEnumAttribute.Name">
            <summary>
            Gets or sets the defined name of the enum, as used in .proto
            (this name is not used during serialization).
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoException">
            <summary>
            Indicates an error during serialization/deserialization of a proto stream.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor(System.String)">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor(System.String,System.Exception)">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="M:ProtoBuf.ProtoException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Creates a new ProtoException instance.</summary>
        </member>
        <member name="T:ProtoBuf.ProtoIgnoreAttribute">
            <summary>
            Indicates that a member should be excluded from serialization; this
            is only normally used when using implict fields.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoPartialIgnoreAttribute">
            <summary>
            Indicates that a member should be excluded from serialization; this
            is only normally used when using implict fields. This allows
            ProtoIgnoreAttribute usage
            even for partial classes where the individual members are not
            under direct control.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoPartialIgnoreAttribute.#ctor(System.String)">
            <summary>
            Creates a new ProtoPartialIgnoreAttribute instance.
            </summary>
            <param name="memberName">Specifies the member to be ignored.</param>
        </member>
        <member name="P:ProtoBuf.ProtoPartialIgnoreAttribute.MemberName">
            <summary>
            The name of the member to be ignored.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoIncludeAttribute">
            <summary>
            Indicates the known-types to support for an individual
            message. This serializes each level in the hierarchy as
            a nested message to retain wire-compatibility with
            other protocol-buffer implementations.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoIncludeAttribute.#ctor(System.Int32,System.Type)">
            <summary>
             Creates a new instance of the ProtoIncludeAttribute.
             </summary>
             <param name="tag">The unique index (within the type) that will identify this data.</param>
             <param name="knownType">The additional type to serialize/deserialize.</param>
        </member>
        <member name="M:ProtoBuf.ProtoIncludeAttribute.#ctor(System.Int32,System.String)">
            <summary>
            Creates a new instance of the ProtoIncludeAttribute.
            </summary>
            <param name="tag">The unique index (within the type) that will identify this data.</param>
            <param name="knownTypeName">The additional type to serialize/deserialize.</param>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.Tag">
            <summary>
            Gets the unique index (within the type) that will identify this data.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.KnownTypeName">
            <summary>
            Gets the additional type to serialize/deserialize.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.KnownType">
            <summary>
            Gets the additional type to serialize/deserialize.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoIncludeAttribute.DataFormat">
            <summary>
            Specifies whether the inherited sype's sub-message should be
            written with a length-prefix (default), or with group markers.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoMapAttribute">
            <summary>
            Controls the formatting of elements in a dictionary, and indicates that
            "map" rules should be used: duplicates *replace* earlier values, rather
            than throwing an exception
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMapAttribute.KeyFormat">
            <summary>
            Describes the data-format used to store the key
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMapAttribute.ValueFormat">
            <summary>
            Describes the data-format used to store the value
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMapAttribute.DisableMap">
            <summary>
            Disables "map" handling; dictionaries will use ".Add(key,value)" instead of  "[key] = value",
            which means duplicate keys will cause an exception (instead of retaining the final value); if
            a proto schema is emitted, it will be produced using "repeated" instead of "map"
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoMemberAttribute">
            <summary>
            Declares a member to be used in protocol-buffer serialization, using
            the given Tag. A DataFormat may be used to optimise the serialization
            format (for instance, using zigzag encoding for negative numbers, or 
            fixed-length encoding for large values.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoMemberAttribute.CompareTo(System.Object)">
            <summary>
            Compare with another ProtoMemberAttribute for sorting purposes
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoMemberAttribute.CompareTo(ProtoBuf.ProtoMemberAttribute)">
            <summary>
            Compare with another ProtoMemberAttribute for sorting purposes
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoMemberAttribute.#ctor(System.Int32)">
            <summary>
            Creates a new ProtoMemberAttribute instance.
            </summary>
            <param name="tag">Specifies the unique tag used to identify this member within the type.</param>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.Name">
            <summary>
            Gets or sets the original name defined in the .proto; not used
            during serialization.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.DataFormat">
            <summary>
            Gets or sets the data-format to be used when encoding this value.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.Tag">
            <summary>
            Gets the unique tag used to identify this member within the type.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.IsRequired">
            <summary>
            Gets or sets a value indicating whether this member is mandatory.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.IsPacked">
            <summary>
            Gets a value indicating whether this member is packed.
            This option only applies to list/array data of primitive types (int, double, etc).
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.OverwriteList">
            <summary>
            Indicates whether this field should *repace* existing values (the default is false, meaning *append*).
            This option only applies to list/array data.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.AsReference">
            <summary>
            Enables full object-tracking/full-graph support.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.DynamicType">
            <summary>
            Embeds the type information into the stream, allowing usage with types not known in advance.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoMemberAttribute.Options">
            <summary>
            Gets or sets a value indicating whether this member is packed (lists/arrays).
            </summary>
        </member>
        <member name="T:ProtoBuf.MemberSerializationOptions">
            <summary>
            Additional (optional) settings that control serialization of members
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.None">
            <summary>
            Default; no additional options
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.Packed">
            <summary>
            Indicates that repeated elements should use packed (length-prefixed) encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.Required">
            <summary>
            Indicates that the given item is required
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.AsReference">
            <summary>
            Enables full object-tracking/full-graph support
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.DynamicType">
            <summary>
            Embeds the type information into the stream, allowing usage with types not known in advance
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.OverwriteList">
            <summary>
            Indicates whether this field should *repace* existing values (the default is false, meaning *append*).
            This option only applies to list/array data.
            </summary>
        </member>
        <member name="F:ProtoBuf.MemberSerializationOptions.AsReferenceHasValue">
            <summary>
            Determines whether the types AsReferenceDefault value is used, or whether this member's AsReference should be used
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoPartialMemberAttribute">
            <summary>
            Declares a member to be used in protocol-buffer serialization, using
            the given Tag and MemberName. This allows ProtoMemberAttribute usage
            even for partial classes where the individual members are not
            under direct control.
            A DataFormat may be used to optimise the serialization
            format (for instance, using zigzag encoding for negative numbers, or 
            fixed-length encoding for large values.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoPartialMemberAttribute.#ctor(System.Int32,System.String)">
            <summary>
            Creates a new ProtoMemberAttribute instance.
            </summary>
            <param name="tag">Specifies the unique tag used to identify this member within the type.</param>
            <param name="memberName">Specifies the member to be serialized.</param>
        </member>
        <member name="P:ProtoBuf.ProtoPartialMemberAttribute.MemberName">
            <summary>
            The name of the member to be serialized.
            </summary>
        </member>
        <member name="T:ProtoBuf.ProtoReader">
            <summary>
            A stateful reader, used to read a protobuf stream. Typical usage would be (sequentially) to call
            ReadFieldHeader and (after matching the field) an appropriate Read* method.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.FieldNumber">
            <summary>
            Gets the number of the field being processed.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.WireType">
            <summary>
            Indicates the underlying proto serialization format on the wire.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.#ctor(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
        </member>
        <member name="P:ProtoBuf.ProtoReader.InternStrings">
            <summary>
            Gets / sets a flag indicating whether strings should be checked for repetition; if
            true, any repeated UTF-8 byte sequence will result in the same String instance, rather
            than a second instance of the same string. Enabled by default. Note that this uses
            a <i>custom</i> interner - the system-wide string interner is not used.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.#ctor(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext,System.Int32)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
            <param name="length">The number of bytes to read, or -1 to read until the end of the stream</param>
        </member>
        <member name="M:ProtoBuf.ProtoReader.#ctor(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext,System.Int64)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
            <param name="length">The number of bytes to read, or -1 to read until the end of the stream</param>
        </member>
        <member name="P:ProtoBuf.ProtoReader.Context">
            <summary>
            Addition information about this deserialization operation.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Dispose">
            <summary>
            Releases resources used by the reader, but importantly <b>does not</b> Dispose the 
            underlying stream; in many typical use-cases the stream is used for different
            processes, so it is assumed that the consumer will Dispose their stream separately.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadUInt32">
            <summary>
            Reads an unsigned 32-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.Position">
            <summary>
            Returns the position of the current reader (note that this is not necessarily the same as the position
            in the underlying stream, if multiple readers are used on the same stream)
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.LongPosition">
            <summary>
            Returns the position of the current reader (note that this is not necessarily the same as the position
            in the underlying stream, if multiple readers are used on the same stream)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadInt16">
            <summary>
            Reads a signed 16-bit integer from the stream: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadUInt16">
            <summary>
            Reads an unsigned 16-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadByte">
            <summary>
            Reads an unsigned 8-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadSByte">
            <summary>
            Reads a signed 8-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadInt32">
            <summary>
            Reads a signed 32-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadInt64">
            <summary>
            Reads a signed 64-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadString">
            <summary>
            Reads a string from the stream (using UTF8); supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ThrowEnumException(System.Type,System.Int32)">
            <summary>
            Throws an exception indication that the given value cannot be mapped to an enum.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadDouble">
            <summary>
            Reads a double-precision number from the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadObject(System.Object,System.Int32,ProtoBuf.ProtoReader)">
            <summary>
            Reads (merges) a sub-message from the stream, internally calling StartSubItem and EndSubItem, and (in between)
            parsing the message in accordance with the model associated with the reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.EndSubItem(ProtoBuf.SubItemToken,ProtoBuf.ProtoReader)">
            <summary>
            Makes the end of consuming a nested message in the stream; the stream must be either at the correct EndGroup
            marker, or all fields of the sub-message must have been consumed (in either case, this means ReadFieldHeader
            should return zero)
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.StartSubItem(ProtoBuf.ProtoReader)">
            <summary>
            Begins consuming a nested message in the stream; supported wire-types: StartGroup, String
            </summary>
            <remarks>The token returned must be help and used when callining EndSubItem</remarks>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadFieldHeader">
            <summary>
            Reads a field header from the stream, setting the wire-type and retuning the field number. If no
            more fields are available, then 0 is returned. This methods respects sub-messages.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.TryReadFieldHeader(System.Int32)">
            <summary>
            Looks ahead to see whether the next field in the stream is what we expect
            (typically; what we've just finished reading - for example ot read successive list items)
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoReader.Model">
            <summary>
            Get the TypeModel associated with this reader
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Hint(ProtoBuf.WireType)">
            <summary>
            Compares the streams current wire-type to the hinted wire-type, updating the reader if necessary; for example,
            a Variant may be updated to SignedVariant. If the hinted wire-type is unrelated then no change is made.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Assert(ProtoBuf.WireType)">
            <summary>
            Verifies that the stream's current wire-type is as expected, or a specialized sub-type (for example,
            SignedVariant) - in which case the current wire-type is updated. Otherwise an exception is thrown.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.SkipField">
            <summary>
            Discards the data for the current field.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadUInt64">
            <summary>
            Reads an unsigned 64-bit integer from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadSingle">
            <summary>
            Reads a single-precision number from the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadBoolean">
            <summary>
            Reads a boolean value from the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
            <returns></returns>
        </member>
        <member name="M:ProtoBuf.ProtoReader.AppendBytes(System.Byte[],ProtoBuf.ProtoReader)">
            <summary>
            Reads a byte-sequence from the stream, appending them to an existing byte-sequence (which can be null); supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadLengthPrefix(System.IO.Stream,System.Boolean,ProtoBuf.PrefixStyle,System.Int32@)">
            <summary>
            Reads the length-prefix of a message from a stream without buffering additional data, allowing a fixed-length
            reader to be created.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadLittleEndianInt32(System.IO.Stream)">
            <summary>
            Reads a little-endian encoded integer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadBigEndianInt32(System.IO.Stream)">
            <summary>
            Reads a big-endian encoded integer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadVarintInt32(System.IO.Stream)">
            <summary>
            Reads a varint encoded integer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadBytes(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a string (of a given lenth, in bytes) directly from the source into a pre-existing buffer. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadBytes(System.IO.Stream,System.Int32)">
            <summary>
            Reads a given number of bytes directly from the source. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.DirectReadString(System.IO.Stream,System.Int32)">
            <summary>
            Reads a string (of a given lenth, in bytes) directly from the source. An exception is thrown if the data is not all available.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadLengthPrefix(System.IO.Stream,System.Boolean,ProtoBuf.PrefixStyle,System.Int32@,System.Int32@)">
            <summary>
            Reads the length-prefix of a message from a stream without buffering additional data, allowing a fixed-length
            reader to be created.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadLongLengthPrefix(System.IO.Stream,System.Boolean,ProtoBuf.PrefixStyle,System.Int32@,System.Int32@)">
            <summary>
            Reads the length-prefix of a message from a stream without buffering additional data, allowing a fixed-length
            reader to be created.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.TryReadUInt64Variant(System.IO.Stream,System.UInt64@)">
            <returns>The number of bytes consumed; 0 if no data available</returns>
        </member>
        <member name="M:ProtoBuf.ProtoReader.AppendExtensionData(ProtoBuf.IExtensible)">
            <summary>
            Copies the current field into the instance as extension data
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.HasSubValue(ProtoBuf.WireType,ProtoBuf.ProtoReader)">
            <summary>
            Indicates whether the reader still has data remaining in the current sub-item,
            additionally setting the wire-type for the next field if there is more data.
            This is used when decoding packed data.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.NoteObject(System.Object,ProtoBuf.ProtoReader)">
            <summary>
            Utility method, not intended for public use; this helps maintain the root object is complex scenarios
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.ReadType">
            <summary>
            Reads a Type from the stream, using the model's DynamicTypeFormatting if appropriate; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Merge(ProtoBuf.ProtoReader,System.Object,System.Object)">
            <summary>
            Merge two objects using the details from the current reader; this is used to change the type
            of objects when an inheritance relationship is discovered later than usual during deserilazation.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoReader.Create(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext,System.Int64)">
            <summary>
            Creates a new reader against a stream
            </summary>
            <param name="source">The source stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to deserialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
            <param name="length">The number of bytes to read, or -1 to read until the end of the stream</param>
        </member>
        <member name="T:ProtoBuf.ProtoWriter">
            <summary>
            Represents an output stream for writing protobuf data.
            
            Why is the API backwards (static methods with writer arguments)?
            See: http://marcgravell.blogspot.com/2010/03/last-will-be-first-and-first-will-be.html
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteObject(System.Object,System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Write an encapsulated sub-object, using the supplied unique key (reprasenting a type).
            </summary>
            <param name="value">The object to write.</param>
            <param name="key">The key that uniquely identifies the type within the model.</param>
            <param name="writer">The destination.</param>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteRecursionSafeObject(System.Object,System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Write an encapsulated sub-object, using the supplied unique key (reprasenting a type) - but the
            caller is asserting that this relationship is non-recursive; no recursion check will be
            performed.
            </summary>
            <param name="value">The object to write.</param>
            <param name="key">The key that uniquely identifies the type within the model.</param>
            <param name="writer">The destination.</param>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteFieldHeader(System.Int32,ProtoBuf.WireType,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a field-header, indicating the format of the next data we plan to write.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteBytes(System.Byte[],ProtoBuf.ProtoWriter)">
            <summary>
            Writes a byte-array to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteBytes(System.Byte[],System.Int32,System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a byte-array to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.StartSubItem(System.Object,ProtoBuf.ProtoWriter)">
            <summary>
            Indicates the start of a nested record.
            </summary>
            <param name="instance">The instance to write.</param>
            <param name="writer">The destination.</param>
            <returns>A token representing the state of the stream; this token is given to EndSubItem.</returns>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.EndSubItem(ProtoBuf.SubItemToken,ProtoBuf.ProtoWriter)">
            <summary>
            Indicates the end of a nested record.
            </summary>
            <param name="token">The token obtained from StartubItem.</param>
            <param name="writer">The destination.</param>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.Create(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext)">
            <summary>
            Creates a new writer against a stream
            </summary>
            <param name="dest">The destination stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to serialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.#ctor(System.IO.Stream,ProtoBuf.Meta.TypeModel,ProtoBuf.SerializationContext)">
            <summary>
            Creates a new writer against a stream
            </summary>
            <param name="dest">The destination stream</param>
            <param name="model">The model to use for serialization; this can be null, but this will impair the ability to serialize sub-objects</param>
            <param name="context">Additional context about this serialization operation</param>
        </member>
        <member name="P:ProtoBuf.ProtoWriter.Context">
            <summary>
            Addition information about this serialization operation.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.Close">
            <summary>
            Flushes data to the underlying stream, and releases any resources. The underlying stream is *not* disposed
            by this operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.ProtoWriter.Model">
            <summary>
            Get the TypeModel associated with this writer
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.Flush(ProtoBuf.ProtoWriter)">
            <summary>
            Writes any buffered data (if possible) to the underlying stream.
            </summary>
            <param name="writer">The writer to flush</param>
            <remarks>It is not always possible to fully flush, since some sequences
            may require values to be back-filled into the byte-stream.</remarks>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteUInt32Variant(System.UInt32,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 32-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteString(System.String,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a string to the stream; supported wire-types: String
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteUInt64(System.UInt64,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 64-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteInt64(System.Int64,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 64-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteUInt32(System.UInt32,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteInt16(System.Int16,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteUInt16(System.UInt16,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 16-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteByte(System.Byte,ProtoBuf.ProtoWriter)">
            <summary>
            Writes an unsigned 8-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteSByte(System.SByte,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 8-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteInt32(System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a signed 32-bit integer to the stream; supported wire-types: Variant, Fixed32, Fixed64, SignedVariant
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteDouble(System.Double,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a double-precision number to the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteSingle(System.Single,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a single-precision number to the stream; supported wire-types: Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.ThrowEnumException(ProtoBuf.ProtoWriter,System.Object)">
            <summary>
            Throws an exception indicating that the given enum cannot be mapped to a serialized value.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteBoolean(System.Boolean,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a boolean to the stream; supported wire-types: Variant, Fixed32, Fixed64
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.AppendExtensionData(ProtoBuf.IExtensible,ProtoBuf.ProtoWriter)">
            <summary>
            Copies any extension data stored for the instance to the underlying stream
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.SetPackedField(System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Used for packed encoding; indicates that the next field should be skipped rather than
            a field header written. Note that the field number must match, else an exception is thrown
            when the attempt is made to write the (incorrect) field. The wire-type is taken from the
            subsequent call to WriteFieldHeader. Only primitive types can be packed.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.ClearPackedField(System.Int32,ProtoBuf.ProtoWriter)">
            <summary>
            Used for packed encoding; explicitly reset the packed field marker; this is not required
            if using StartSubItem/EndSubItem
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WritePackedPrefix(System.Int32,ProtoBuf.WireType,ProtoBuf.ProtoWriter)">
            <summary>
            Used for packed encoding; writes the length prefix using fixed sizes rather than using
            buffering. Only valid for fixed-32 and fixed-64 encoding.
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.SetRootObject(System.Object)">
            <summary>
            Specifies a known root object to use during reference-tracked serialization
            </summary>
        </member>
        <member name="M:ProtoBuf.ProtoWriter.WriteType(System.Type,ProtoBuf.ProtoWriter)">
            <summary>
            Writes a Type to the stream, using the model's DynamicTypeFormatting if appropriate; supported wire-types: String
            </summary>
        </member>
        <member name="T:ProtoBuf.SerializationContext">
            <summary>
            Additional information about a serialization operation
            </summary>
        </member>
        <member name="P:ProtoBuf.SerializationContext.Context">
            <summary>
            Gets or sets a user-defined object containing additional information about this serialization/deserialization operation.
            </summary>
        </member>
        <member name="P:ProtoBuf.SerializationContext.Default">
            <summary>
            A default SerializationContext, with minimal information.
            </summary>
        </member>
        <member name="P:ProtoBuf.SerializationContext.State">
            <summary>
            Gets or sets the source or destination of the transmitted data.
            </summary>
        </member>
        <member name="M:ProtoBuf.SerializationContext.op_Implicit(ProtoBuf.SerializationContext)~System.Runtime.Serialization.StreamingContext">
            <summary>
            Convert a SerializationContext to a StreamingContext
            </summary>
        </member>
        <member name="M:ProtoBuf.SerializationContext.op_Implicit(System.Runtime.Serialization.StreamingContext)~ProtoBuf.SerializationContext">
            <summary>
            Convert a StreamingContext to a SerializationContext
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializer">
            <summary>
            Provides protocol-buffer serialization capability for concrete, attributed types. This
            is a *default* model, but custom serializer models are also supported.
            </summary>
            <remarks>
            Protocol-buffer serialization is a compact binary format, designed to take
            advantage of sparse data and knowledge of specific data types; it is also
            extensible, allowing a type to be deserialized / merged even if some data is
            not recognised.
            </remarks>
        </member>
        <member name="M:ProtoBuf.Serializer.GetProto``1">
            <summary>
            Suggest a .proto definition for the given type
            </summary>
            <typeparam name="T">The type to generate a .proto definition for</typeparam>
            <returns>The .proto definition as a string</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.GetProto``1(ProtoBuf.Meta.ProtoSyntax)">
            <summary>
            Suggest a .proto definition for the given type
            </summary>
            <typeparam name="T">The type to generate a .proto definition for</typeparam>
            <returns>The .proto definition as a string</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.DeepClone``1(``0)">
            <summary>
            Create a deep clone of the supplied instance; any sub-items are also cloned.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializer.Merge``1(System.IO.Stream,``0)">
            <summary>
            Applies a protocol-buffer stream to an existing instance.
            </summary>
            <typeparam name="T">The type being merged.</typeparam>
            <param name="instance">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.Deserialize``1(System.IO.Stream)">
            <summary>
            Creates a new instance from a protocol-buffer stream
            </summary>
            <typeparam name="T">The type to be created.</typeparam>
            <param name="source">The binary stream to apply to the new instance (cannot be null).</param>
            <returns>A new, initialized instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.Deserialize(System.Type,System.IO.Stream)">
            <summary>
            Creates a new instance from a protocol-buffer stream
            </summary>
            <param name="type">The type to be created.</param>
            <param name="source">The binary stream to apply to the new instance (cannot be null).</param>
            <returns>A new, initialized instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.Serialize``1(System.IO.Stream,``0)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="destination">The destination stream to write to.</param>
        </member>
        <member name="M:ProtoBuf.Serializer.ChangeType``2(``0)">
            <summary>
            Serializes a given instance and deserializes it as a different type;
            this can be used to translate between wire-compatible objects (where
            two .NET types represent the same data), or to promote/demote a type
            through an inheritance hierarchy.
            </summary>
            <remarks>No assumption of compatibility is made between the types.</remarks>
            <typeparam name="TFrom">The type of the object being copied.</typeparam>
            <typeparam name="TTo">The type of the new object to be created.</typeparam>
            <param name="instance">The existing instance to use as a template.</param>
            <returns>A new instane of type TNewType, with the data from TOldType.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.Serialize``1(System.Runtime.Serialization.SerializationInfo,``0)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied SerializationInfo.
            </summary>
            <typeparam name="T">The type being serialized.</typeparam>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="info">The destination SerializationInfo to write to.</param>
        </member>
        <member name="M:ProtoBuf.Serializer.Serialize``1(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext,``0)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied SerializationInfo.
            </summary>
            <typeparam name="T">The type being serialized.</typeparam>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="info">The destination SerializationInfo to write to.</param>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Serializer.Serialize``1(System.Xml.XmlWriter,``0)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied XmlWriter.
            </summary>
            <typeparam name="T">The type being serialized.</typeparam>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="writer">The destination XmlWriter to write to.</param>
        </member>
        <member name="M:ProtoBuf.Serializer.Merge``1(System.Xml.XmlReader,``0)">
            <summary>
            Applies a protocol-buffer from an XmlReader to an existing instance.
            </summary>
            <typeparam name="T">The type being merged.</typeparam>
            <param name="instance">The existing instance to be modified (cannot be null).</param>
            <param name="reader">The XmlReader containing the data to apply to the instance (cannot be null).</param>
        </member>
        <member name="M:ProtoBuf.Serializer.Merge``1(System.Runtime.Serialization.SerializationInfo,``0)">
            <summary>
            Applies a protocol-buffer from a SerializationInfo to an existing instance.
            </summary>
            <typeparam name="T">The type being merged.</typeparam>
            <param name="instance">The existing instance to be modified (cannot be null).</param>
            <param name="info">The SerializationInfo containing the data to apply to the instance (cannot be null).</param>
        </member>
        <member name="M:ProtoBuf.Serializer.Merge``1(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext,``0)">
            <summary>
            Applies a protocol-buffer from a SerializationInfo to an existing instance.
            </summary>
            <typeparam name="T">The type being merged.</typeparam>
            <param name="instance">The existing instance to be modified (cannot be null).</param>
            <param name="info">The SerializationInfo containing the data to apply to the instance (cannot be null).</param>
            <param name="context">Additional information about this serialization operation.</param>
        </member>
        <member name="M:ProtoBuf.Serializer.PrepareSerializer``1">
            <summary>
            Precompiles the serializer for a given type.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializer.CreateFormatter``1">
            <summary>
            Creates a new IFormatter that uses protocol-buffer [de]serialization.
            </summary>
            <typeparam name="T">The type of object to be [de]deserialized by the formatter.</typeparam>
            <returns>A new IFormatter to be used during [de]serialization.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.DeserializeItems``1(System.IO.Stream,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Reads a sequence of consecutive length-prefixed items from a stream, using
            either base-128 or fixed-length prefixes. Base-128 prefixes with a tag
            are directly comparable to serializing multiple items in succession
            (use the <see cref="F:ProtoBuf.Serializer.ListItemTag"/> tag to emulate the implicit behavior
            when serializing a list/array). When a tag is
            specified, any records with different tags are silently omitted. The
            tag is ignored. The tag is ignored for fixed-length prefixes.
            </summary>
            <typeparam name="T">The type of object to deserialize.</typeparam>
            <param name="source">The binary stream containing the serialized records.</param>
            <param name="style">The prefix style used in the data.</param>
            <param name="fieldNumber">The tag of records to return (if non-positive, then no tag is
            expected and all records are returned).</param>
            <returns>The sequence of deserialized objects.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.DeserializeWithLengthPrefix``1(System.IO.Stream,ProtoBuf.PrefixStyle)">
            <summary>
            Creates a new instance from a protocol-buffer stream that has a length-prefix
            on data (to assist with network IO).
            </summary>
            <typeparam name="T">The type to be created.</typeparam>
            <param name="source">The binary stream to apply to the new instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <returns>A new, initialized instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.DeserializeWithLengthPrefix``1(System.IO.Stream,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Creates a new instance from a protocol-buffer stream that has a length-prefix
            on data (to assist with network IO).
            </summary>
            <typeparam name="T">The type to be created.</typeparam>
            <param name="source">The binary stream to apply to the new instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="fieldNumber">The expected tag of the item (only used with base-128 prefix style).</param>
            <returns>A new, initialized instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.MergeWithLengthPrefix``1(System.IO.Stream,``0,ProtoBuf.PrefixStyle)">
            <summary>
            Applies a protocol-buffer stream to an existing instance, using length-prefixed
            data - useful with network IO.
            </summary>
            <typeparam name="T">The type being merged.</typeparam>
            <param name="instance">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.SerializeWithLengthPrefix``1(System.IO.Stream,``0,ProtoBuf.PrefixStyle)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream,
            with a length-prefix. This is useful for socket programming,
            as DeserializeWithLengthPrefix/MergeWithLengthPrefix can be used to read the single object back
            from an ongoing stream.
            </summary>
            <typeparam name="T">The type being serialized.</typeparam>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="destination">The destination stream to write to.</param>
        </member>
        <member name="M:ProtoBuf.Serializer.SerializeWithLengthPrefix``1(System.IO.Stream,``0,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream,
            with a length-prefix. This is useful for socket programming,
            as DeserializeWithLengthPrefix/MergeWithLengthPrefix can be used to read the single object back
            from an ongoing stream.
            </summary>
            <typeparam name="T">The type being serialized.</typeparam>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="destination">The destination stream to write to.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
        </member>
        <member name="M:ProtoBuf.Serializer.TryReadLengthPrefix(System.IO.Stream,ProtoBuf.PrefixStyle,System.Int32@)">
            <summary>Indicates the number of bytes expected for the next message.</summary>
            <param name="source">The stream containing the data to investigate for a length.</param>
            <param name="style">The algorithm used to encode the length.</param>
            <param name="length">The length of the message, if it could be identified.</param>
            <returns>True if a length could be obtained, false otherwise.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.TryReadLengthPrefix(System.Byte[],System.Int32,System.Int32,ProtoBuf.PrefixStyle,System.Int32@)">
            <summary>Indicates the number of bytes expected for the next message.</summary>
            <param name="buffer">The buffer containing the data to investigate for a length.</param>
            <param name="index">The offset of the first byte to read from the buffer.</param>
            <param name="count">The number of bytes to read from the buffer.</param>
            <param name="style">The algorithm used to encode the length.</param>
            <param name="length">The length of the message, if it could be identified.</param>
            <returns>True if a length could be obtained, false otherwise.</returns>
        </member>
        <member name="F:ProtoBuf.Serializer.ListItemTag">
            <summary>
            The field number that is used as a default when serializing/deserializing a list of objects.
            The data is treated as repeated message with field number 1.
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializer.NonGeneric">
            <summary>
            Provides non-generic access to the default serializer.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.DeepClone(System.Object)">
            <summary>
            Create a deep clone of the supplied instance; any sub-items are also cloned.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.Serialize(System.IO.Stream,System.Object)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream.
            </summary>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="dest">The destination stream to write to.</param>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.Deserialize(System.Type,System.IO.Stream)">
            <summary>
            Creates a new instance from a protocol-buffer stream
            </summary>
            <param name="type">The type to be created.</param>
            <param name="source">The binary stream to apply to the new instance (cannot be null).</param>
            <returns>A new, initialized instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.Merge(System.IO.Stream,System.Object)">
            <summary>Applies a protocol-buffer stream to an existing instance.</summary>
            <param name="instance">The existing instance to be modified (cannot be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <returns>The updated instance</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.SerializeWithLengthPrefix(System.IO.Stream,System.Object,ProtoBuf.PrefixStyle,System.Int32)">
            <summary>
            Writes a protocol-buffer representation of the given instance to the supplied stream,
            with a length-prefix. This is useful for socket programming,
            as DeserializeWithLengthPrefix/MergeWithLengthPrefix can be used to read the single object back
            from an ongoing stream.
            </summary>
            <param name="instance">The existing instance to be serialized (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="destination">The destination stream to write to.</param>
            <param name="fieldNumber">The tag used as a prefix to each record (only used with base-128 style prefixes).</param>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.TryDeserializeWithLengthPrefix(System.IO.Stream,ProtoBuf.PrefixStyle,ProtoBuf.Serializer.TypeResolver,System.Object@)">
            <summary>
            Applies a protocol-buffer stream to an existing instance (or null), using length-prefixed
            data - useful with network IO.
            </summary>
            <param name="value">The existing instance to be modified (can be null).</param>
            <param name="source">The binary stream to apply to the instance (cannot be null).</param>
            <param name="style">How to encode the length prefix.</param>
            <param name="resolver">Used to resolve types on a per-field basis.</param>
            <returns>The updated instance; this may be different to the instance argument if
            either the original instance was null, or the stream defines a known sub-type of the
            original instance.</returns>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.CanSerialize(System.Type)">
            <summary>
            Indicates whether the supplied type is explicitly modelled by the model
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializer.NonGeneric.PrepareSerializer(System.Type)">
            <summary>
            Precompiles the serializer for a given type.
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializer.GlobalOptions">
            <summary>
            Global switches that change the behavior of protobuf-net
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializer.GlobalOptions.InferTagFromName">
            <summary>
            <see cref="P:ProtoBuf.Meta.RuntimeTypeModel.InferTagFromNameDefault"/>
            </summary>
        </member>
        <member name="T:ProtoBuf.Serializer.TypeResolver">
            <summary>
            Maps a field-number to a type
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializer.FlushPool">
            <summary>
            Releases any internal buffers that have been reserved for efficiency; this does not affect any serialization
            operations; simply: it can be used (optionally) to release the buffers for garbage collection (at the expense
            of having to re-allocate a new buffer for the next operation, rather than re-use prior buffers).
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.IProtoSerializer.ExpectedType">
            <summary>
            The type that this serializer is intended to work for.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IProtoSerializer.Write(System.Object,ProtoBuf.ProtoWriter)">
            <summary>
            Perform the steps necessary to serialize this data.
            </summary>
            <param name="value">The value to be serialized.</param>
            <param name="dest">The writer entity that is accumulating the output data.</param>
        </member>
        <member name="M:ProtoBuf.Serializers.IProtoSerializer.Read(System.Object,ProtoBuf.ProtoReader)">
            <summary>
            Perform the steps necessary to deserialize this data.
            </summary>
            <param name="value">The current value, if appropriate.</param>
            <param name="source">The reader providing the input data.</param>
            <returns>The updated / replacement value.</returns>
        </member>
        <member name="P:ProtoBuf.Serializers.IProtoSerializer.RequiresOldValue">
            <summary>
            Indicates whether a Read operation <em>replaces</em> the existing value, or
            <em>extends</em> the value. If false, the "value" parameter to Read is
            discarded, and should be passed in as null.
            </summary>
        </member>
        <member name="P:ProtoBuf.Serializers.IProtoSerializer.ReturnsValue">
            <summary>
            Now all Read operations return a value (although most do); if false no
            value should be expected.
            </summary>
        </member>
        <member name="M:ProtoBuf.Serializers.IProtoSerializer.EmitWrite(ProtoBuf.Compiler.CompilerContext,ProtoBuf.Compiler.Local)">
            <summary>Emit the IL necessary to perform the given actions
            to serialize this data.
            </summary>
            <param name="ctx">Details and utilities for the method being generated.</param>
            <param name="valueFrom">The source of the data to work against;
            If the value is only needed once, then LoadValue is sufficient. If
            the value is needed multiple times, then note that a "null"
            means "the top of the stack", in which case you should create your
            own copy - GetLocalWithValue.</param>
        </member>
        <member name="M:ProtoBuf.Serializers.IProtoSerializer.EmitRead(ProtoBuf.Compiler.CompilerContext,ProtoBuf.Compiler.Local)">
            <summary>
            Emit the IL necessary to perform the given actions to deserialize this data.
            </summary>
            <param name="ctx">Details and utilities for the method being generated.</param>
            <param name="entity">For nested values, the instance holding the values; note
            that this is not always provided - a null means not supplied. Since this is always
            a variable or argument, it is not necessary to consume this value.</param>
        </member>
        <member name="T:ProtoBuf.ServiceModel.ProtoBehaviorAttribute">
            <summary>
            Uses protocol buffer serialization on the specified operation; note that this
            must be enabled on both the client and server.
            </summary>
        </member>
        <member name="T:ProtoBuf.ServiceModel.ProtoBehaviorExtension">
            <summary>
            Configuration element to swap out DatatContractSerilaizer with the XmlProtoSerializer for a given endpoint.
            </summary>
            <seealso cref="T:ProtoBuf.ServiceModel.ProtoEndpointBehavior"/>
        </member>
        <member name="M:ProtoBuf.ServiceModel.ProtoBehaviorExtension.#ctor">
            <summary>
            Creates a new ProtoBehaviorExtension instance.
            </summary>
        </member>
        <member name="P:ProtoBuf.ServiceModel.ProtoBehaviorExtension.BehaviorType">
            <summary>
            Gets the type of behavior.
            </summary>     
        </member>
        <member name="M:ProtoBuf.ServiceModel.ProtoBehaviorExtension.CreateBehavior">
            <summary>
            Creates a behavior extension based on the current configuration settings.
            </summary>
            <returns>The behavior extension.</returns>
        </member>
        <member name="T:ProtoBuf.ServiceModel.ProtoEndpointBehavior">
            <summary>
            Behavior to swap out DatatContractSerilaizer with the XmlProtoSerializer for a given endpoint.
             <example>
            Add the following to the server and client app.config in the system.serviceModel section:
             <behaviors>
               <endpointBehaviors>
                 <behavior name="ProtoBufBehaviorConfig">
                   <ProtoBufSerialization/>
                 </behavior>
               </endpointBehaviors>
             </behaviors>
             <extensions>
               <behaviorExtensions>
                 <add name="ProtoBufSerialization" type="ProtoBuf.ServiceModel.ProtoBehaviorExtension, protobuf-net, Version=*********, Culture=neutral, PublicKeyToken=257b51d87d2e4d67"/>
               </behaviorExtensions>
             </extensions>
            
            Configure your endpoints to have a behaviorConfiguration as follows:
            
             <service name="TK.Framework.Samples.ServiceModel.Contract.SampleService">
               <endpoint address="http://myhost:9003/SampleService" binding="basicHttpBinding" behaviorConfiguration="ProtoBufBehaviorConfig"
                bindingConfiguration="basicHttpBindingConfig" name="basicHttpProtoBuf" contract="ISampleServiceContract" />
             </service>
             <client>
                 <endpoint address="http://myhost:9003/SampleService" binding="basicHttpBinding"
                     bindingConfiguration="basicHttpBindingConfig" contract="ISampleServiceContract"
                     name="BasicHttpProtoBufEndpoint" behaviorConfiguration="ProtoBufBehaviorConfig"/>
              </client>
            </example>
            </summary>
        </member>
        <member name="T:ProtoBuf.ServiceModel.ProtoOperationBehavior">
            <summary>
            Describes a WCF operation behaviour that can perform protobuf serialization
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.ProtoOperationBehavior.#ctor(System.ServiceModel.Description.OperationDescription)">
            <summary>
            Create a new ProtoOperationBehavior instance
            </summary>
        </member>
        <member name="P:ProtoBuf.ServiceModel.ProtoOperationBehavior.Model">
            <summary>
            The type-model that should be used with this behaviour
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.ProtoOperationBehavior.CreateSerializer(System.Type,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Collections.Generic.IList{System.Type})">
            <summary>
            Creates a protobuf serializer if possible (falling back to the default WCF serializer)
            </summary>
        </member>
        <member name="T:ProtoBuf.ServiceModel.XmlProtoSerializer">
            <summary>
            An xml object serializer that can embed protobuf data in a base-64 hunk (looking like a byte[])
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.XmlProtoSerializer.TryCreate(ProtoBuf.Meta.TypeModel,System.Type)">
            <summary>
            Attempt to create a new serializer for the given model and type
            </summary>
            <returns>A new serializer instance if the type is recognised by the model; null otherwise</returns>
        </member>
        <member name="M:ProtoBuf.ServiceModel.XmlProtoSerializer.#ctor(ProtoBuf.Meta.TypeModel,System.Type)">
            <summary>
            Creates a new serializer for the given model and type
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.XmlProtoSerializer.WriteEndObject(System.Xml.XmlDictionaryWriter)">
            <summary>
            Ends an object in the output
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.XmlProtoSerializer.WriteStartObject(System.Xml.XmlDictionaryWriter,System.Object)">
            <summary>
            Begins an object in the output
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.XmlProtoSerializer.WriteObjectContent(System.Xml.XmlDictionaryWriter,System.Object)">
            <summary>
            Writes the body of an object in the output
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.XmlProtoSerializer.IsStartObject(System.Xml.XmlDictionaryReader)">
            <summary>
            Indicates whether this is the start of an object we are prepared to handle
            </summary>
        </member>
        <member name="M:ProtoBuf.ServiceModel.XmlProtoSerializer.ReadObject(System.Xml.XmlDictionaryReader,System.Boolean)">
            <summary>
            Reads the body of an object
            </summary>
        </member>
        <member name="T:ProtoBuf.SubItemToken">
            <summary>
            Used to hold particulars relating to nested objects. This is opaque to the caller - simply
            give back the token you are given at the end of an object.
            </summary>
        </member>
        <member name="T:ProtoBuf.WireType">
            <summary>
            Indicates the encoding used to represent an individual value in a protobuf stream
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.None">
            <summary>
            Represents an error condition
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.Variant">
            <summary>
            Base-128 variant-length encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.Fixed64">
            <summary>
            Fixed-length 8-byte encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.String">
            <summary>
            Length-variant-prefixed encoding
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.StartGroup">
            <summary>
            Indicates the start of a group
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.EndGroup">
            <summary>
            Indicates the end of a group
            </summary>
        </member>
        <member name="F:ProtoBuf.WireType.Fixed32">
            <summary>
            Fixed-length 4-byte encoding
            </summary>10
        </member>
        <member name="F:ProtoBuf.WireType.SignedVariant">
            <summary>
            This is not a formal wire-type in the "protocol buffers" spec, but
            denotes a variant integer that should be interpreted using
            zig-zag semantics (so -ve numbers aren't a significant overhead)
            </summary>
        </member>
    </members>
</doc>
