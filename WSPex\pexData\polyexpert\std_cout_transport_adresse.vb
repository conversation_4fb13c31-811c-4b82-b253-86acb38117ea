﻿Imports ProtoBuf

Interface std_cout_transport

    Property id As Integer

    Property cout_transport_pondere_can As Decimal?     ' = calculé: combien
    Property cout_transport_can As Decimal?             ' = cout a utiliser selon parametres (directives de <PERSON>, table _parametre)
    Property cout_transport_facture_can As Decimal?     ' = cout a utiliser + upcharges, arrondissements, etc. (selon parametre_operation)
    Property cout_transport_standard_can As Decimal?    ' = cout calculé en incluant les pickups et autre couts réduits ou à zéro

    Property cout_transport_pondere_us As Decimal?
    Property cout_transport_us As Decimal?
    Property cout_transport_facture_us As Decimal?
    Property cout_transport_standard_us As Decimal?

    Property taux_us As Decimal?

End Interface

<ProtoContract>
Partial Public Class std_cout_transport_secteur
    Implements std_cout_transport
    'Public Property id_std_cout_transport_secteur As Integer
    <ProtoMember(1)> Public Property id_secteur As Integer Implements std_cout_transport.id
    <ProtoMember(2)> Public Property date_cout As DateTime
    <ProtoMember(3)> Public Property id_transport_moyen As Integer
    <ProtoMember(4)> Public Property cout_transport_pondere_can As Decimal? Implements std_cout_transport.cout_transport_pondere_can
    <ProtoMember(5)> Public Property cout_transport_can As Decimal? Implements std_cout_transport.cout_transport_can
    <ProtoMember(6)> Public Property cout_transport_facture_can As Decimal? Implements std_cout_transport.cout_transport_facture_can '-fix- invalide pour secteur?
    <ProtoMember(7)> Public Property cout_transport_standard_can As Decimal? Implements std_cout_transport.cout_transport_standard_can
    <ProtoMember(8)> Public Property entreposage_can As Decimal? '-fix- invalide pour secteur?
    <ProtoMember(9)> Public Property douane_assurance_can As Decimal? '-fix- invalide pour secteur?
    <ProtoMember(10)> Public Property cout_transport_pondere_us As Decimal? Implements std_cout_transport.cout_transport_pondere_us
    <ProtoMember(11)> Public Property cout_transport_us As Decimal? Implements std_cout_transport.cout_transport_us
    <ProtoMember(12)> Public Property cout_transport_facture_us As Decimal? Implements std_cout_transport.cout_transport_facture_us '-fix- invalide pour secteur?
    <ProtoMember(13)> Public Property cout_transport_standard_us As Decimal? Implements std_cout_transport.cout_transport_standard_us '-fix- invalide pour secteur?
    <ProtoMember(14)> Public Property entreposage_us As Decimal? '-fix- invalide pour secteur?
    <ProtoMember(15)> Public Property douane_assurance_us As Decimal? '-fix- invalide pour secteur?
    <ProtoMember(16)> Public Property taux_us As Decimal? Implements std_cout_transport.taux_us
    <ProtoMember(17)> Public Property stats_total_cout_can As Decimal?
    <ProtoMember(18)> Public Property stats_total_poids_lb As Decimal?
    <ProtoMember(19)> Public Property stats_nombre_palette As Integer?
    <ProtoMember(20)> Public Property id_cout_transport_source_calcul As Integer?
End Class

<ProtoContract>
<PetaPoco.PrimaryKey("id_std_cout_transport_adresse")>
Partial Public Class std_cout_transport_adresse
    Implements std_cout_transport

    'Public Property id_std_cout_transport_adresse As Integer
    <ProtoMember(1)> Public Property id_adresse As Integer Implements std_cout_transport.id
    <ProtoMember(2)> Public Property date_cout As DateTime
    <ProtoMember(3)> Public Property id_transport_moyen As Integer
    <ProtoMember(4)> Public Property id_secteur_transport As Integer
    <ProtoMember(5)> Public Property cout_transport_pondere_can As Decimal? Implements std_cout_transport.cout_transport_pondere_can
    <ProtoMember(6)> Public Property cout_transport_can As Decimal? Implements std_cout_transport.cout_transport_can
    <ProtoMember(7)> Public Property cout_transport_facture_can As Decimal? Implements std_cout_transport.cout_transport_facture_can
    <ProtoMember(8)> Public Property cout_transport_standard_can As Decimal? Implements std_cout_transport.cout_transport_standard_can
    <ProtoMember(9)> Public Property entreposage_can As Decimal?
    <ProtoMember(10)> Public Property entrepot_externe_can As Decimal?
    <ProtoMember(11)> Public Property douane_assurance_can As Decimal?
    <ProtoMember(12)> Public Property rabais_client_can As Decimal?
    <ProtoMember(13)> Public Property variable_client_can As Decimal?
    <ProtoMember(14)> Public Property cout_transport_pondere_us As Decimal? Implements std_cout_transport.cout_transport_pondere_us
    <ProtoMember(15)> Public Property cout_transport_us As Decimal? Implements std_cout_transport.cout_transport_us
    <ProtoMember(16)> Public Property cout_transport_facture_us As Decimal? Implements std_cout_transport.cout_transport_facture_us
    <ProtoMember(17)> Public Property cout_transport_standard_us As Decimal? Implements std_cout_transport.cout_transport_standard_us
    <ProtoMember(18)> Public Property entreposage_us As Decimal?
    <ProtoMember(19)> Public Property entrepot_externe_us As Decimal?
    <ProtoMember(20)> Public Property douane_assurance_us As Decimal?
    <ProtoMember(21)> Public Property rabais_client_us As Decimal?
    <ProtoMember(22)> Public Property variable_client_us As Decimal?
    <ProtoMember(23)> Public Property taux_us As Decimal? Implements std_cout_transport.taux_us
    <ProtoMember(24)> Public Property stats_total_cout_can As Decimal?
    <ProtoMember(25)> Public Property stats_total_poids_lb As Decimal?
    <ProtoMember(26)> Public Property stats_nombre_palette As Integer?
    <ProtoMember(27)> Public Property nom_client As String
    <ProtoMember(28)> Public Property type_adresse As String
    <ProtoMember(29)> Public Property adresse As String
    <ProtoMember(30)> Public Property client_paie_transport As Boolean
    <ProtoMember(31)> Public Property prix_vente_inclut_transport As Boolean?
    <ProtoMember(32)> Public Property id_cout_transport_source_calcul As Integer?
    Public Function ShallowCopy() As std_cout_transport_adresse
        Return DirectCast(MemberwiseClone(), std_cout_transport_adresse)
    End Function


End Class

<ProtoContract>
<PetaPoco.PrimaryKey("id_std_cout_transport_client")>
Partial Public Class std_cout_transport_client
    Implements std_cout_transport
    'Public Property id_std_cout_transport_client As Integer
    <ProtoMember(1)> Public Property id_client As Integer Implements std_cout_transport.id
    <ProtoMember(2)> Public Property date_cout As DateTime
    <ProtoMember(3)> Public Property id_transport_moyen As Integer
    <ProtoMember(4)> Public Property id_secteur_transport As Integer
    <ProtoMember(5)> Public Property cout_transport_pondere_can As Decimal? Implements std_cout_transport.cout_transport_pondere_can
    <ProtoMember(6)> Public Property cout_transport_can As Decimal? Implements std_cout_transport.cout_transport_can
    <ProtoMember(7)> Public Property cout_transport_facture_can As Decimal? Implements std_cout_transport.cout_transport_facture_can
    <ProtoMember(8)> Public Property cout_transport_standard_can As Decimal? Implements std_cout_transport.cout_transport_standard_can
    <ProtoMember(9)> Public Property entreposage_can As Decimal?
    <ProtoMember(10)> Public Property entrepot_externe_can As Decimal?
    <ProtoMember(11)> Public Property douane_assurance_can As Decimal?
    <ProtoMember(12)> Public Property rabais_client_can As Decimal?
    <ProtoMember(13)> Public Property variable_client_can As Decimal?
    <ProtoMember(14)> Public Property cout_transport_pondere_us As Decimal? Implements std_cout_transport.cout_transport_pondere_us
    <ProtoMember(15)> Public Property cout_transport_us As Decimal? Implements std_cout_transport.cout_transport_us
    <ProtoMember(16)> Public Property cout_transport_facture_us As Decimal? Implements std_cout_transport.cout_transport_facture_us
    <ProtoMember(17)> Public Property cout_transport_standard_us As Decimal? Implements std_cout_transport.cout_transport_standard_us
    <ProtoMember(18)> Public Property entreposage_us As Decimal?
    <ProtoMember(19)> Public Property entrepot_externe_us As Decimal?
    <ProtoMember(20)> Public Property douane_assurance_us As Decimal?
    <ProtoMember(21)> Public Property rabais_client_us As Decimal?
    <ProtoMember(22)> Public Property variable_client_us As Decimal?
    <ProtoMember(23)> Public Property taux_us As Decimal? Implements std_cout_transport.taux_us
    <ProtoMember(24)> Public Property stats_total_cout_can As Decimal?
    <ProtoMember(25)> Public Property stats_total_poids_lb As Decimal?
    <ProtoMember(26)> Public Property stats_nombre_palette As Integer?
    <ProtoMember(27)> Public Property nom_client As String
    <ProtoMember(28)> Public Property type_adresse As String
    <ProtoMember(29)> Public Property adresse As String
    <ProtoMember(30)> Public Property client_paie_transport As Boolean
    <ProtoMember(31)> Public Property prix_vente_inclut_transport As Boolean?
    <ProtoMember(32)> Public Property id_cout_transport_source_calcul As Integer?
    <PetaPoco.Ignore> Public Property trace As String



End Class
