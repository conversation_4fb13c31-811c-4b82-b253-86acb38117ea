﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_CLIENT")>
<ProtoContract>
Partial Public Class client
    <ProtoMember(1)> Public Property id_client As Integer
    '	Public Property no_client As String
    <ProtoMember(3)> Public Property nom As String
    '	Public Property type_entreprise As String
    <ProtoMember(5)> Public Property type_client As String
    '	Public Property reference As String
    '	Public Property langue As String
    '	Public Property sae As String
    '	Public Property id_palette As Integer?
    '	Public Property rabais_volume As Boolean
    '	Public Property rabais_volume_lbs As Decimal?
    '	Public Property date_volume As DateTime?
    '	Public Property logo As String
    '	Public Property logo_client As Byte()
    '	Public Property statement_name As String
    <ProtoMember(16)> Public Property id_vendeur As Integer
    '	Public Property id_terme As Integer?
    <ProtoMember(18)> Public Property id_classe As Integer
    <ProtoMember(19)> Public Property id_territoire As Integer
    '	Public Property credit_see as double?
    '	Public Property duree_see As String
    '	Public Property date_saisie As DateTime?
    '	Public Property limite_entrepot As Integer?
    '	Public Property limite_temps As Integer?
    '	Public Property nb_employe As Integer?
    '	Public Property nb_extruders As Integer?
    '	Public Property nb_bag As Integer?
    '	Public Property nb_print As Integer?
    '	Public Property potentiel_livre As Integer?
    '	Public Property potentiel_$ as double?
    '	Public Property superficie As Integer?
    '	Public Property evaluation As String
    '	Public Property prod_converted As Integer?
    '	Public Property prod_general As Integer?
    '	Public Property distribution As Integer?
    <ProtoMember(36)> Public Property nom_tri As String
    '	Public Property dia_rouleau_max As Integer?
    '	Public Property poids_rouleau_max As Integer?
    '	Public Property date_modification As DateTime?
    '	Public Property imprime As Boolean
    '	Public Property couleur As String
    '	Public Property couleur_lettrage As String
    '	Public Property id_devise As Integer?
    '	Public Property unite As String
    '	Public Property conf_commande_vendeur As Boolean
    '	Public Property nb_etiquette_rol As Integer?
    '	Public Property liste_prix_change As Boolean
    '	Public Property client_echantillon As Boolean
    '	Public Property id_type_client As Integer?
    '	Public Property sae_facturation As String
    '	Public Property unite_facturation As String
    '	Public Property nb_feuille_skid As Integer?
    '	Public Property no_overun As Boolean
    '	Public Property com_production As String
    '	Public Property com_interne As String
    '	Public Property com_emballage As String
    '	Public Property bloc_note As String
    '	Public Property rabais_actif As Boolean
    '	Public Property rabais_type As Integer?
    '	Public Property rabais_montant as double?
    '	Public Property conf_exped_vendeur As Boolean
    '	Public Property impression_facture As Boolean
    '	Public Property impression_ps As Boolean
    '	Public Property impression_bol As Boolean
    '	Public Property credit_car as double?
    '	Public Property credit_cmd as double?
    '	Public Property credit_pourc_acc as double?
    '	Public Property credit_note As String
    '	Public Property credit_overide As Integer?
    '	Public Property credit_overide_note As String
    '	Public Property credit_bloque_prod As Boolean
    '	Public Property credit_bloque_exped As Boolean
    '	Public Property contact_csr As String
    '	Public Property telephone_csr As String
    '	Public Property intltelephone_csr As Boolean
    '	Public Property fax_csr As String
    '	Public Property intlfax_csr As Boolean
    '	Public Property poste_csr As String
    '	Public Property email_csr As String
    '	Public Property interurbain_csr As Boolean
    '	Public Property id_modele_logo As Integer?
    '	Public Property id_employe As Integer?
    '	Public Property imp_detail_rouleau As Boolean
    '	Public Property appliquersurcharge As Boolean
    '	Public Property montantsurcharge as double?
    '	Public Property nbcopiedetailrouleau As Integer?
    '	Public Property id_core As Integer?
    '	Public Property nb_etiquette_preid_rol As Integer?
    '	Public Property nb_etiquette_preid_sample As Integer?
    '	Public Property id_rapport_client_detskidroll As Integer?
    '	Public Property id_rapport_client_etiqsupp As Integer?
    '	Public Property taux_commission As Decimal?
    '	Public Property echantillon As Boolean
    '	Public Property rptskidslipid As Integer
    '	Public Property recycleur As Boolean
    '	Public Property cout_emballage As Decimal
    '	Public Property recycleur_default As Boolean?
    '	Public Property client_ignore_polyexpert_report As Boolean
    '	Public Property id_categorie_client As Integer?
    '	Public Property emb_nb_etage_max As Integer?
    '	Public Property emb_nb_rouleau_max_largeur As Integer?
    '	Public Property emb_nb_rouleau_max_longueur As Integer?
    '	Public Property emb_feuille_double_stacking As Boolean
    '	Public Property emb_docket_echantillon As Boolean
    '	Public Property id_entrepot As Integer?
    '	Public Property dia_rouleau_min As Integer?
    '	Public Property emb_ligne_traitement As Boolean
    '	Public Property emb_deux_planches_sous_palette_old As Boolean
    '	Public Property emb_feuille_palette_dans_docket As Boolean
    '	Public Property verif_diff_poids_pesee As Boolean
    '	Public Property has_release_number As Boolean
    '	Public Property rowversion As Byte()
    '	Public Property bolt_seal_obligatoire As Boolean
    '	Public Property id_emb_feuille_palette As Integer
    '	Public Property use_email_facture As Boolean
    '	Public Property email_from As String
    '	Public Property email_tos As String
    '	Public Property email_subject As String
    '	Public Property email_body As String
    '	Public Property is_show_cos_on_order_confirmation As Boolean
End Class
