﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_STD_PRODUIT_CLIENT")>
<ProtoContract>
Partial Public Class std_produit_client
    <ProtoMember(1)> Public Property id_std_produit_client As Integer
    <ProtoMember(2)> Public Property id_produit_client As Integer
    <ProtoMember(3)> Public Property annee As Integer
    <ProtoMember(4)> Public Property mois As Integer
    <ProtoMember(5)> Public Property obj_escompte_can As Decimal
    <ProtoMember(6)> Public Property obj_escompte_us As Decimal
    <ProtoMember(7)> Public Property obj_escompte_revise_can As Decimal
    <ProtoMember(8)> Public Property obj_escompte_revise_us As Decimal
    <ProtoMember(9)> Public Property obj_escompte_reforecast_can As Decimal
    <ProtoMember(10)> Public Property obj_escompte_reforecast_us As Decimal
    <ProtoMember(11)> Public Property obj_escompte_revise_reforecast_can As Decimal
    <ProtoMember(12)> Public Property obj_escompte_revise_reforecast_us As Decimal
    <ProtoMember(13)> Public Property obj_volume As Decimal
    <ProtoMember(14)> Public Property obj_volume_revise As Decimal
    <ProtoMember(15)> Public Property obj_volume_reforecast As Decimal
    <ProtoMember(16)> Public Property obj_volume_revise_reforecast As Decimal
    'Public Property produit_del As String
    'Public Property produit_del_old As String
    'Public Property client_del As String
    'Public Property to_delete As Boolean
    'Public Property commentaire As String
    'Public Property obj_total_zero As Boolean
End Class