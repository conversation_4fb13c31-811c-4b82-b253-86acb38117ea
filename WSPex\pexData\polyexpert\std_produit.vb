﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_STD_PRODUIT")>
<ProtoContract>
Partial Public Class std_produit
    <ProtoMember(1)> Public Property id_std_produit As Integer
    <ProtoMember(2)> Public Property id_produit As Integer
    <ProtoMember(3)> Public Property annee As Integer
    <ProtoMember(4)> Public Property mois As Integer
    <ProtoMember(5)> Public Property obj_cm_can As Decimal
    <ProtoMember(6)> Public Property obj_cm_us As Decimal
    <ProtoMember(7)> Public Property old_obj_cm_ecart_std_vs_reel_can As Decimal
    <ProtoMember(8)> Public Property old_obj_cm_ecart_std_vs_reel_us As Decimal
    <ProtoMember(9)> Public Property obj_cm_revise_can As Decimal
    <ProtoMember(10)> Public Property obj_cm_revise_us As Decimal
End Class