﻿Imports ProtoBuf

'<PetaPoco.PrimaryKey("UniqueID")>
<ProtoContract()>
<PetaPoco.TableName("Facture")>
Partial Public Class pexFacture
    Implements ISortID
    <ProtoMember(1)> Public Property UniqueID As Long Implements ISortID.UniqueID
    <ProtoMember(2)> Public Property FactureID As Integer Implements ISortID.SortID, ISortID.SortID2
    <ProtoMember(3)> Public Property CommandeID As Integer
    <ProtoMember(4)> Public Property ClientID As Integer
    <ProtoMember(5)> Public Property Facture As String
    <ProtoMember(6)> Public Property ProduitID As Integer
    <ProtoMember(7)> Public Property Produit As String
    <ProtoMember(8)> Public Property ProduitClientID As Integer
    <ProtoMember(9)> Public Property PvID As Integer
    <ProtoMember(10)> Public Property Pv As String
    <ProtoMember(11)> Public Property FactureDate As DateTime
    <ProtoMember(12)> Public Property FactureDateAnneeFiscale As Integer
    <ProtoMember(13)> Public Property FactureDateMoisFiscale As Integer
    <ProtoMember(14)> Public Property FactureTaux As Decimal
    <ProtoMember(15)> Public Property FactureDeviseID As Integer
    <ProtoMember(16)> Public Property FactureDevise As String
    <ProtoMember(17)> Public Property FactureInitialLbs As Decimal
    <ProtoMember(18)> Public Property FactureAnnulationLbs As Decimal
    <ProtoMember(19)> Public Property FactureLbs As Decimal
    <ProtoMember(20)> Public Property FactureInitialMontant As Decimal
    <ProtoMember(21)> Public Property FactureAnnulationMontant As Decimal
    <ProtoMember(22)> Public Property FactureMontant As Decimal
    <ProtoMember(23)> Public Property FactureMontantLbs As Decimal
    <ProtoMember(24)> Public Property FactureNbRouleau As Decimal
    <ProtoMember(25)> Public Property FactureType As String
    <ProtoMember(26)> Public Property FactureRetourID As Integer
    <ProtoMember(27)> Public Property FacturePlainteEntenteID As Integer
    <ProtoMember(28)> Public Property CommandeLbs As Decimal
    <ProtoMember(29)> Public Property BudgetSetupLb As Decimal
    <ProtoMember(30)> Public Property REELPrixVenteBrutTauxUS As Decimal
    <ProtoMember(31)> Public Property REELRabaisVolumeTauxUS As Decimal
    <ProtoMember(32)> Public Property REELResineTauxUs As Decimal
    <ProtoMember(33)> Public Property REELResineScrapTauxUs As Decimal
    <ProtoMember(34)> Public Property REELVariableGlobaleTauxUS As Decimal
    <ProtoMember(35)> Public Property REELVariableClientTauxUS As Decimal
    <ProtoMember(36)> Public Property REELPrixVenteInitialCanBrutMontantLbs As Decimal
    <ProtoMember(37)> Public Property REELEscompteCanMontantLbs As Decimal
    <ProtoMember(38)> Public Property REELPrixVenteCanBrutMontantLbs As Decimal
    <ProtoMember(39)> Public Property REELRabaisVolumeCanMontantLbs As Decimal
    <ProtoMember(40)> Public Property REELCommissionVendeurCanMontantLbs As Decimal
    <ProtoMember(41)> Public Property REELRedevanceClientCanMontantLbs As Decimal
    <ProtoMember(42)> Public Property REELPrixVenteCanNetMontantLbs As Decimal
    <ProtoMember(43)> Public Property REELResineCanMontantLbs As Decimal
    <ProtoMember(44)> Public Property REELQuantiteAjustementCanMontantLbs As Decimal
    <ProtoMember(45)> Public Property REELSpreadCanBrutMontantLbs As Decimal
    <ProtoMember(46)> Public Property REELScrapCanMontantLbs As Decimal
    <ProtoMember(47)> Public Property REELSpreadCanNetMontantLbs As Decimal
    <ProtoMember(48)> Public Property REELVariableGlobaleCanMontantLbs As Decimal
    <ProtoMember(49)> Public Property REELCmCanBrutMontantLbs As Decimal
    <ProtoMember(50)> Public Property REELVariableClientCanMontantLbs As Decimal
    <ProtoMember(51)> Public Property REELCmCanNetMontantLbs As Decimal
    <ProtoMember(52)> Public Property REELPrixVenteCanBrutMontant As Decimal
    <ProtoMember(53)> Public Property REELRabaisVolumeCanMontant_OLD As Decimal
    <ProtoMember(54)> Public Property REELPrixVenteCanNetMontant As Decimal
    <ProtoMember(55)> Public Property REELResineCanMontant As Decimal
    <ProtoMember(56)> Public Property REELQuantiteAjustementCanMontant As Decimal
    <ProtoMember(57)> Public Property REELSpreadCanBrutMontant As Decimal
    <ProtoMember(58)> Public Property REELScrapCanMontant As Decimal
    <ProtoMember(59)> Public Property REELSpreadCanNetMontant As Decimal
    <ProtoMember(60)> Public Property REELVariableGlobaleCanMontant As Decimal
    <ProtoMember(61)> Public Property REELCmCanBrutMontant As Decimal
    <ProtoMember(62)> Public Property REELVariableClientCanMontant As Decimal
    <ProtoMember(63)> Public Property REELCmCanNetMontant As Decimal
    <ProtoMember(64)> Public Property REELRabaisClientUsMontantLbs As Decimal
    <ProtoMember(65)> Public Property REELRabaisClientCanMontantLbs As Decimal
    <ProtoMember(66)> Public Property REELPrixVenteInitialUsBrutMontantLbs As Decimal
    <ProtoMember(67)> Public Property REELEscompteUsMontantLbs As Decimal
    <ProtoMember(68)> Public Property REELPrixVenteUsBrutMontantLbs As Decimal
    <ProtoMember(69)> Public Property REELRabaisVolumeUsMontantLbs As Decimal
    <ProtoMember(70)> Public Property REELCommissionVendeurUSMontantLbs As Decimal
    <ProtoMember(71)> Public Property REELRedevanceClientUSMontantLbs As Decimal
    <ProtoMember(72)> Public Property REELPrixVenteUsNetMontantLbs As Decimal
    <ProtoMember(73)> Public Property REELResineUsMontantLbs As Decimal
    <ProtoMember(74)> Public Property REELQuantiteAjustementUsMontantLbs As Decimal
    <ProtoMember(75)> Public Property REELSpreadUsBrutMontantLbs As Decimal
    <ProtoMember(76)> Public Property REELScrapUsMontantLbs As Decimal
    <ProtoMember(77)> Public Property REELSpreadUsNetMontantLbs As Decimal
    <ProtoMember(78)> Public Property REELVariableGlobaleUsMontantLbs As Decimal
    <ProtoMember(79)> Public Property REELCmUsBrutMontantLbs As Decimal
    <ProtoMember(80)> Public Property REELVariableClientUsMontantLbs As Decimal
    <ProtoMember(81)> Public Property REELCmUsNetMontantLbs As Decimal
    <ProtoMember(82)> Public Property REELPrixVenteUsBrutMontant As Decimal
    <ProtoMember(83)> Public Property REELRabaisVolumeUsMontant_OLD As Decimal
    <ProtoMember(84)> Public Property REELPrixVenteUsNetMontant As Decimal
    <ProtoMember(85)> Public Property REELResineUsMontant As Decimal
    <ProtoMember(86)> Public Property REELQuantiteAjustementUsMontant As Decimal
    <ProtoMember(87)> Public Property REELSpreadUsBrutMontant As Decimal
    <ProtoMember(88)> Public Property REELScrapUsMontant As Decimal
    <ProtoMember(89)> Public Property REELSpreadUsNetMontant As Decimal
    <ProtoMember(90)> Public Property REELVariableGlobaleUsMontant As Decimal
    <ProtoMember(91)> Public Property REELCmUsBrutMontant As Decimal
    <ProtoMember(92)> Public Property REELVariableClientUsMontant As Decimal
    <ProtoMember(93)> Public Property REELCmUsNetMontant As Decimal
    <ProtoMember(94)> Public Property REELProductionExpediable As Decimal
    <ProtoMember(95)> Public Property REELRejetTotal As Decimal
    <ProtoMember(96)> Public Property STDProduitClientVolumeObjectif As Decimal
    <ProtoMember(97)> Public Property STDProduitClientVolumeObjectifRevise As Decimal
    <ProtoMember(98)> Public Property STDProduitClientVolumeObjectifPond As Decimal
    <ProtoMember(99)> Public Property STDProduitClientVolumeObjectifRevisePond As Decimal
    <ProtoMember(100)> Public Property STDProduitCmObjCanMontantLbs As Decimal
    <ProtoMember(101)> Public Property STDProduitCmObjReviseCanMontantLbs As Decimal
    <ProtoMember(102)> Public Property STDProduitCmObjUsMontantLbs As Decimal
    <ProtoMember(103)> Public Property STDProduitCmObjReviseUsMontantLbs As Decimal
    <ProtoMember(104)> Public Property STDProduitClientEscompteObjectifCanMontantLbs As Decimal
    <ProtoMember(105)> Public Property STDProduitClientEscompteObjectifReviseCanMontantLbs As Decimal
    <ProtoMember(106)> Public Property STDProduitClientEscompteObjectifUsMontantLbs As Decimal
    <ProtoMember(107)> Public Property STDProduitClientEscompteObjectifReviseUsMontantLbs As Decimal
    <ProtoMember(108)> Public Property STDCmObjectifCanMontantLbs As Decimal
    <ProtoMember(109)> Public Property STDCmObjectifReviseCanMontantLbs As Decimal
    <ProtoMember(110)> Public Property STDCmObjectifUsMontantLbs As Decimal
    <ProtoMember(111)> Public Property STDCmObjectifReviseUsMontantLbs As Decimal
    <ProtoMember(112)> Public Property STDCmObjectifCanMontant As Decimal
    <ProtoMember(113)> Public Property STDCmObjectifReviseCanMontant As Decimal
    <ProtoMember(114)> Public Property STDCmObjectifUsMontant As Decimal
    <ProtoMember(115)> Public Property STDCmObjectifReviseUsMontant As Decimal
    <ProtoMember(116)> Public Property REELCmObjectifCanMontantLbs As Decimal
    <ProtoMember(117)> Public Property REELCmObjectifUsMontantLbs As Decimal
    <ProtoMember(118)> Public Property REELCmObjectifCanMontant As Decimal
    <ProtoMember(119)> Public Property REELCmObjectifUsMontant As Decimal
    <ProtoMember(120)> Public Property Calculer As Boolean
    <ProtoMember(121)> Public Property REELResineSTDUsMontantLbs As Decimal
    <ProtoMember(122)> Public Property REELResineListeUsMontantLbs As Decimal
    <ProtoMember(123)> Public Property STDVCTransportSecteurvsHistorique As String
    <ProtoMember(124)> Public Property STDVCTransportInclus As Boolean
    <ProtoMember(125)> Public Property STDVCTransportSecteurCanMontantLbs As Decimal
    <ProtoMember(126)> Public Property STDVCTransportSecteurUsMontantLbs As Decimal
    <ProtoMember(127)> Public Property STDVCTransportHistoriqueCanMontantLbs As Decimal
    <ProtoMember(128)> Public Property STDVCTransportHistoriqueUsMontantLbs As Decimal
    <ProtoMember(129)> Public Property STDVCTransportCanMontantLbs As Decimal
    <ProtoMember(130)> Public Property STDVCTransportUsMontantLbs As Decimal
    <ProtoMember(131)> Public Property STDVCDouaneCanMontantLbs As Decimal
    <ProtoMember(132)> Public Property STDVCDouaneUsMontantLbs As Decimal
    <ProtoMember(133)> Public Property STDVCEntreposageCanMontantLbs As Decimal
    <ProtoMember(134)> Public Property STDVCEntreposageUsMontantLbs As Decimal
    <ProtoMember(135)> Public Property STDVCRabaisClientCanMontantLbs As Decimal
    <ProtoMember(136)> Public Property STDVCRabaisClientUsMontantLbs As Decimal
    <ProtoMember(137)> Public Property STDVCEntrepotExterneCanMontantLbs As Decimal
    <ProtoMember(138)> Public Property STDVCEntrepotExterneUsMontantLbs As Decimal
    <ProtoMember(139)> Public Property STDVCTransportSecteurCanMontant As Decimal
    <ProtoMember(140)> Public Property STDVCTransportSecteurUsMontant As Decimal
    <ProtoMember(141)> Public Property STDVCTransportHistoriqueCanMontant As Decimal
    <ProtoMember(142)> Public Property STDVCTransportHistoriqueUsMontant As Decimal
    <ProtoMember(143)> Public Property STDVCTransportCanMontant As Decimal
    <ProtoMember(144)> Public Property STDVCTransportUsMontant As Decimal
    <ProtoMember(145)> Public Property STDVCDouaneCanMontant As Decimal
    <ProtoMember(146)> Public Property STDVCDouaneUsMontant As Decimal
    <ProtoMember(147)> Public Property STDVCEntreposageCanMontant As Decimal
    <ProtoMember(148)> Public Property STDVCEntreposageUsMontant As Decimal
    <ProtoMember(149)> Public Property STDVCRabaisClientCanMontant As Decimal
    <ProtoMember(150)> Public Property STDVCRabaisClientUsMontant As Decimal
    <ProtoMember(151)> Public Property STDVCEntrepotExterneCanMontant As Decimal
    <ProtoMember(152)> Public Property STDVCEntrepotExterneUsMontant As Decimal
    <ProtoMember(153)> Public Property REELVCTransportCanMontantLbs As Decimal
    <ProtoMember(154)> Public Property REELVCTransportUsMontantLbs As Decimal
    <ProtoMember(155)> Public Property REELVCDouaneCanMontantLbs As Decimal
    <ProtoMember(156)> Public Property REELVCDouaneUsMontantLbs As Decimal
    <ProtoMember(157)> Public Property REELVCEntreposageCanMontantLbs As Decimal
    <ProtoMember(158)> Public Property REELVCEntreposageUsMontantLbs As Decimal
    <ProtoMember(159)> Public Property REELVCRabaisClientCanMontantLbs As Decimal
    <ProtoMember(160)> Public Property REELVCRabaisClientUsMontantLbs As Decimal
    <ProtoMember(161)> Public Property REELVCEntrepotExterneCanMontantLbs As Decimal
    <ProtoMember(162)> Public Property REELVCEntrepotExterneUsMontantLbs As Decimal
    <ProtoMember(163)> Public Property REELRevenuTransportCanMontantLbs As Decimal
    <ProtoMember(164)> Public Property REELRevenuTransportUSMontantLbs As Decimal
    <ProtoMember(165)> Public Property REELPrixVenteBrutInclusTransportCanMontantLbs As Decimal
    <ProtoMember(166)> Public Property REELPrixVenteBrutInclusTransportUSMontantLbs As Decimal
    <ProtoMember(167)> Public Property REELRabaisTransportFactureCanMontantLbs As Decimal
    <ProtoMember(168)> Public Property REELRabaisTransportFactureUSMontantLbs As Decimal
    <ProtoMember(169)> Public Property REELPrixVenteCanMontantLbs As Decimal
    <ProtoMember(170)> Public Property REELPrixVenteUSMontantLbs As Decimal

    <PetaPoco.Ignore> Public Property totProd As totauxProduction

End Class