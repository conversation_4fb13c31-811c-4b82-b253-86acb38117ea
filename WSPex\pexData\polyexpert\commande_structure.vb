﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_commande_structure")>
<ProtoContract>
Partial Public Class commande_structure
    <ProtoMember(1)> Public Property id_commande_structure As Integer
    <ProtoMember(2)> Public Property id_commande As Integer?
    <ProtoMember(3)> Public Property id_structure As Integer?
    <ProtoMember(4)> Public Property id_pv As Integer?
    <ProtoMember(5)> Public Property nom As String
    <ProtoMember(6)> Public Property cout As Decimal
    <ProtoMember(7)> Public Property cout_us As Decimal
    <ProtoMember(8)> Public Property cout_std_us As Decimal
    <ProtoMember(9)> Public Property cout_liste_us As Decimal
    <ProtoMember(10)> Public Property taux_us As Decimal
    <ProtoMember(11)> Public Property note As Decimal?
    <ProtoMember(12)> Public Property no_ordre As Integer?
    <ProtoMember(13)> Public Property id_commande_machine As Integer
    <ProtoMember(14)> Public Property dateheure As DateTime?
End Class