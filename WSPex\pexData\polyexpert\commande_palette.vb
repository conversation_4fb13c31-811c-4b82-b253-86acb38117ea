﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_COMMANDE_PALETTE")>
<ProtoContract>
Partial Public Class commande_palette
    <ProtoMember(1)> Public Property id_commande_palette As Integer
    'Public Property no_palette As Integer?
    <ProtoMember(3)> Public Property id_commande As Integer
    'Public Property id_operateur As Integer?
    'Public Property id_emballeur As Integer?
    'Public Property id_equipe As Integer?
    <ProtoMember(7)> Public Property nb_rouleau As Integer?
    'Public Property poids_brut As Integer?
    <ProtoMember(9)> Public Property poids_net As Integer
    'Public Property to_print As Integer?
    'Public Property date_palette As DateTime?
    'Public Property date_modification As DateTime?
    'Public Property ouverte_flag As Boolean
    <ProtoMember(14)> Public Property emballee_flag As Boolean
    <ProtoMember(15)> Public Property id_expedition As Integer
    'Public Property prep_expedition_x As Boolean
    'Public Property charge_expedition_x As Boolean
    <ProtoMember(18)> Public Property id_statut_palette As Integer
    <ProtoMember(19)> Public Property id_facture_item As Integer
    'Public Property date_emballage As DateTime?
    'Public Property date_annulation As DateTime?
    'Public Property id_facture_item_credit As Integer?
    'Public Property date_relachement_x As DateTime?
    'Public Property largeur As Single?
    'Public Property profondeur As Single?
    'Public Property hauteur As Single?
    'Public Property emplacements As String
    'Public Property pret_exped_flag_x As Boolean
    'Public Property id_emplacement_exped_x As Integer?
    'Public Property zone_expedition_x As Boolean
    'Public Property pret_gun_x As Boolean
    'Public Property id_emplacement_tampon As Integer?
    'Public Property transferer_a_x As Integer?
    'Public Property preparationexpeditionid_x As Integer?
    'Public Property date_envoyer As DateTime?
    <ProtoMember(36)> Public Property id_requisition_transport As Integer
    'Public Property date_demande_transport As DateTime?
    'Public Property flag_x As Integer?
    'Public Property nbrouleauetage As Integer?
    'Public Property nbetage As Integer?
    'Public Property paletteid As Integer?
    'Public Property emballageid As Integer?
    'Public Property id_entrepot As Integer?
    'Public Property id_entrepot_dest As Integer?
    'Public Property rowversion As Byte()
    'Public Property id_statut_exped_palette As Integer
    'Public Property old_no_palette As Integer?
    <ProtoMember(48)> <PetaPoco.ResultColumn> Public Property fac_itm_unite As String
    <ProtoMember(49)> <PetaPoco.ResultColumn> Public Property cmd_poids_rou_cal As Single?
End Class