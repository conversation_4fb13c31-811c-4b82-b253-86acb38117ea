﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.PrimaryKey("id_requisition_transport")>
Partial Public Class requisition_transport
    <ProtoMember(1)> Public Property id_requisition_transport As Integer
    <ProtoMember(2)> Public Property no_requisition As Integer
    '<ProtoMember(3)> Public Property id_voiturier As Integer
    '<ProtoMember(4)> Public Property coutFixe As Single
    '<ProtoMember(5)> Public Property coutSupp As Single
    '<ProtoMember(6)> Public Property coutSuppTotal As Single
    '<ProtoMember(7)> Public Property noPrep As Integer
    <ProtoMember(8)> Public Property dateCreationAuto As DateTime
    '<ProtoMember(9)> Public Property date_requisition As DateTime
    '<ProtoMember(10)> Public Property date_ramassage As DateTime
    '<ProtoMember(11)> Public Property date_livraison As DateTime
    '<ProtoMember(12)> Public Property commentaire As String
    '<ProtoMember(13)> Public Property creer As Boolean
    '<ProtoMember(14)> Public Property expedier As Boolean
    '<ProtoMember(15)> Public Property date_relachement As DateTime
    '<ProtoMember(16)> Public Property pourcentSurchargeEssence As Single
    <ProtoMember(17)> Public Property id_devise As Integer
    <ProtoMember(18)> Public Property montant As Decimal
    '<ProtoMember(19)> Public Property date_exped As DateTime
    '<ProtoMember(20)> Public Property provisionDatePaiement As DateTime
    '<ProtoMember(21)> Public Property provisionPaiement As Decimal
    '<ProtoMember(22)> Public Property provisionDeviseId As Integer
    '<ProtoMember(23)> Public Property provisionVoiturierId As Integer
    '<ProtoMember(24)> Public Property provisionBonLivraison As String
    '<ProtoMember(25)> Public Property provisionFacture As String
    '<ProtoMember(26)> Public Property provisionFactureDouane As String
    '<ProtoMember(27)> Public Property provisionCommentaire As String
    '<ProtoMember(28)> Public Property ramassage As Boolean
    '<ProtoMember(29)> Public Property papierDouaneParDrop As Boolean
    '<ProtoMember(30)> Public Property RowVersion As Byte()
    '<ProtoMember(31)> Public Property idTransportType As Integer
    '<ProtoMember(32)> Public Property idRequisitionTransportFraisSupplRaison As Integer
    '<ProtoMember(33)> Public Property distance_livraison_km As Decimal
    <ProtoMember(34)> Public Property id_transport_moyen As Integer
End Class
