﻿Imports System.IO
Imports System.IO.Compression
Imports System.Reflection
Imports Utilities

Imports DualID = System.Int64

Module Common

    '* netsh http add urlacl url=http://+:8184/ user=Everyone

    'Public Const USE_NEW As Boolean = True

    Public Const DB_ENV As String = "ini"

    Public Const DAT_CHANGEMENT_CALCUL_SCRAP = #2021-09-01#

    Public INI As IniFile

    Public Const EPSILON_DECIMAL As Decimal = 0.00001D ' différence minimale pour que 2 variable "Decimal" soient considérées différentes.

    Public PRD_EXCLUS_POUR_CALCUL_COUT As String() = {"AUTRE", "RESINE", "TRANSPORT", "CORES", "TRANS/MANU EST", "TARIF DOUANIER"}
    Public PRD_EXCLUS_POUR_CALCUL_POIDS As String() = {"AUTRE", "RESINE", "SURCHAR<PERSON>", "TRANSPORT", "CORES", "TRANS/MANU EST", "TARIF DOUANIER"}

    Public SERVICE_PEX_REPORT_RUNNING As Boolean
    Public SERVICE_COUT_TRANSPORT_RUNNING As Boolean
    Public SERVICE_COUT_PV_ACTUEL_RUNNING As Boolean

    Public Const DEBUT_NOUVEAU_CALCUL As DateTime = #2022-09-01# ' changé de 2023-09-01 à la demande de Donald

    Sub New()
        Dim filename = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, $"{WCF_NAME}.ini") '-todo- external naming...
        INI = New IniFile(filename)
    End Sub

    Public Function GetDB(Optional db As String = "PE") As PetaPoco.Database
        Return New PetaPoco.Database(INI.ReadKey("Database", "ConnectionString" + db, "none"), "System.Data.SqlClient")
    End Function

    Public Function WCF_PORT() As Integer
        Return CInt(INI.ReadKey("WebService", "Port", "8184"))
    End Function

    Public Function GetData(Of T)(ppdb As PetaPoco.Database, sql As String, cachePath As String, Optional getFromDB As Boolean = False, Optional compress As Boolean = True) As List(Of T)

        Dim result As List(Of T) = Nothing
        Dim typeName = GetType(T).Name
        Dim hash = GetHash($"SQL:{sql}")
        Dim filename = $"{cachePath}{If(cachePath.EndsWith(Path.DirectorySeparatorChar), "", Path.DirectorySeparatorChar)}{typeName}.{hash}.dat{If(compress, "z", "")}"

        Dim fileExists = File.Exists(filename)

        If fileExists Then
            Dim fi = New FileInfo(filename)
            If (Now - fi.LastWriteTime).TotalDays > 1 Then getFromDB = True ' too old
            If fi.Length = 0 Then getFromDB = True ' empty
        End If

        If getFromDB OrElse Not fileExists Then
            Try
                Log($"Fetching {typeName} from DB...")
                result = ppdb.Fetch(Of T)(sql)
            Catch ex As Exception
                Log($"{typeName} error : " & ex.Message)
            End Try
            If Not getFromDB Then
                Log($"     Saving {filename} to disk...")
                Using fs = File.Create(filename)
                    If compress Then
                        Using zip = New GZipStream(fs, CompressionMode.Compress, True)
                            ProtoBuf.Serializer.Serialize(zip, result)
                        End Using
                    Else
                        ProtoBuf.Serializer.Serialize(fs, result)
                    End If
                End Using
            End If
        Else
            Log($"Reading {typeName} from {filename}...")
            Using rd = File.Open(filename, FileMode.Open)
                If compress Then
                    Using zip = New GZipStream(rd, CompressionMode.Decompress, True)
                        result = ProtoBuf.Serializer.Deserialize(Of List(Of T))(zip)
                    End Using
                Else
                    result = ProtoBuf.Serializer.Deserialize(Of List(Of T))(rd)
                End If
            End Using
        End If

        Log($" - {typeName}: {result.Count:#,##0} records.")
        Return result

    End Function

    Function GetHash(text As String) As String
        If String.IsNullOrEmpty(text) Then Return String.Empty
        Using sha = New System.Security.Cryptography.SHA256Managed
            Dim textData = System.Text.Encoding.UTF8.GetBytes(text)
            Dim hash = sha.ComputeHash(textData)
            Return BitConverter.ToString(hash).Replace("-", String.Empty).ToLowerInvariant
        End Using
    End Function

    Public Const EPSILON = 0.000001D
    Public Function NotZero(dec As Decimal, Optional epsilon As Decimal = EPSILON) As Boolean ' 1 millionth
        Return Math.Abs(dec) > epsilon
    End Function

    Public Function TryGetValue(Of Tkey, Tval As {New})(dic As Dictionary(Of Tkey, Tval), key As Tkey) As Tval
        Dim val As Tval = Nothing
        If dic.TryGetValue(key, val) Then Return val
        Return New Tval
    End Function

    Function AnneeFiscale(dat As Date?) As Integer
        If dat Is Nothing Then Return 0
        Dim dtValue As Date = CDate(dat)
        Return If(dtValue.Month >= 9, dtValue.Year + 1, dtValue.Year)
    End Function

    Function MoisFiscal(dat As Date?) As Integer
        If dat Is Nothing Then Return 0
        Dim dtValue As Date = CDate(dat)
        Return If(dtValue.Month >= 9, dtValue.Month - 8, dtValue.Month + 4)
    End Function

    Function SemaineFiscale(dat As Date?) As Integer
        If dat Is Nothing Then Return 0
        Dim debut = DateSerial(AnneeFiscale(dat) - 1, 9, 1)
        Dim diff = DateDiff(DateInterval.Day, DateAdd(DateInterval.Day, 8 - DatePart(DateInterval.Weekday, debut), debut), CDate(dat).Date)
        Return CInt(If(diff < 0, 1, diff \ 7 + 2))
    End Function

    Function JourFiscal(dat As Date?) As Integer
        If dat Is Nothing Then Return 0
        Return CInt(DateDiff(DateInterval.Day, DateSerial(AnneeFiscale(dat) - 1, 9, 1), CDate(dat).Date) + 1)
    End Function

    Function Div0(dividend As Decimal, divisor As Decimal) As Decimal
        If divisor = 0D Then Return 0D
        Return dividend / divisor
    End Function

    Function Div0(dividend As Double, divisor As Double) As Double
        If divisor = 0D Then Return 0D
        Return dividend / divisor
    End Function

    Function FormatFraction(value As Decimal, base As Integer, Optional showSign As Boolean = False) As String

        Dim signe = If(value >= 0, "+", "-")
        Dim num = Math.Ceiling(Math.Abs(value) * base)
        Dim entier = CInt(Math.Floor(num / base))
        num = num - (entier * base)
        Dim den = CDec(base)

        Dim numDiv = num / 2
        Dim denDiv = den / 2
        Do While Math.Floor(numDiv) = numDiv AndAlso Math.Floor(denDiv) = denDiv
            num = numDiv
            den = denDiv
            numDiv = num / 2
            denDiv = den / 2
        Loop

        Dim ret = If(showSign, signe, "") &
                  If(entier = 0, "", CStr(entier)) &
                  If(entier <> 0 AndAlso den <> 1, " ", "") &
                  If(den = 1, "", CStr(num) & "/" & CStr(Int(den)))

        Return ret

    End Function

    Function GetLargeurFractionnaire(lrg1 As Decimal, Optional lrg2 As Decimal = 0D, Optional lrg3 As Decimal = 0D) As String

        Dim lst = New List(Of String)

        If lrg1 <> 0 Then lst.Add(FormatFraction(lrg1, 16))
        If lrg2 <> 0 Then lst.Add(FormatFraction(lrg2, 16))
        If lrg3 <> 0 Then lst.Add(FormatFraction(lrg3, 16))

        Return String.Join(" x ", lst)

    End Function

    Function NDate(dat As DateTime?) As Date
        Return If(dat.HasValue, dat.Value, Nothing)
    End Function

    ' Dual key id:
    ' id_commande_machine + id_commande
    ' id_produit + id_client
    Function GetDualIdKey(id1 As Long, id2 As Long) As DualID
        Return (id1 << 32) Or id2
    End Function

    Function SplitDualIdKey(key As DualID) As (id1 As Long, id2 As Long)
        Dim id2 = CLng((key << 32) >> 32)
        Dim id1 As Long = Convert.ToInt64(key >> 32)
        Return (id1, id2)
    End Function

    '' Id/Year/Month
    '' id_produit_client + year + month
    'Function GetIdYearMonth(id1 As Long, year As Integer, month As Integer) As IdYearMonth
    '    Return (id1 << 32) Or CLng(year * 100 + month)
    'End Function

    Function Trunc(d As Decimal, decimals As Byte) As Decimal
        Dim r = Math.Round(d, decimals)
        If (d > 0 AndAlso r > d) Then
            Return r - New Decimal(1, 0, 0, False, decimals)
        ElseIf (d < 0 AndAlso r < d) Then
            Return r + New Decimal(1, 0, 0, False, decimals)
        End If
        Return r
    End Function

    ' Requires ProtoBuf labeled objects
    Function ObjectsAreTheSame(Of T)(obj1 As T, obj2 As T) As Boolean
        Dim b1 = GetBytes(obj1)
        Dim b2 = GetBytes(obj2)
        Return Enumerable.SequenceEqual(b1, b2)
    End Function

    ' Returns a protobuf serialized byte array
    ' Requires ProtoBuf labeled objects
    Public Function GetBytes(Of T)(obj As T) As Byte()

        Dim bytes As Byte()
        Using ms = New MemoryStream
            ProtoBuf.Serializer.Serialize(ms, obj)
            bytes = ms.ToArray
        End Using
        Return bytes

    End Function

    Public Interface ISortID
        Property UniqueID As Long
        Property SortID As Integer
        Property SortID2 As Integer
    End Interface

    Function GetUpdatesAndDeletes(Of T As ISortID)(previous As List(Of T), actual As List(Of T)) As (Updates As List(Of T), Deletes As List(Of T))

        Dim lstUpdates = New List(Of T)
        Dim lstDeletes = New List(Of T)

        previous = previous.OrderBy(Function(x) x.SortID).ThenBy(Function(x) x.SortID2).ToList
        actual = actual.OrderBy(Function(x) x.SortID).ThenBy(Function(x) x.SortID2).ToList

        ' compare matching records
        Dim prev = 0, actu = 0 ' ptr previous, actual
        Do

            Dim oPrev = previous(prev)
            Dim oActu = actual(actu)

CompareObjects:
            ' Check that we are comparing the same records
            If (oPrev.SortID <> oActu.SortID) OrElse oPrev.SortID2 <> oPrev.SortID2 Then

                ' put correct object in proper list
                If oPrev.SortID < oActu.SortID OrElse (oPrev.SortID = oActu.SortID AndAlso oPrev.SortID2 < oActu.SortID2) Then
                    ' previous is deleted
                    lstDeletes.Add(oPrev)
                    prev += 1
                    If prev >= previous.Count Then GoTo AllDone
                    oPrev = previous(prev)
                Else
                    ' actual will be inserted
                    lstUpdates.Add(oActu)
                    actu += 1
                    If actu >= actual.Count Then GoTo AllDone
                    oActu = actual(actu)
                End If

                GoTo CompareObjects ' try again until "keys" match
            End If

            If Not ObjectsAreTheSame(oPrev, oActu) Then
                lstUpdates.Add(oActu)
            End If

            prev += 1
            actu += 1
AllDone:
            If prev >= previous.Count Then
                ' at the end for previous rows; insert any remaining in actual
                If actu < actual.Count Then
                    For i = actu To actual.Count - 1
                        lstUpdates.Add(actual(i))
                    Next
                End If
                Exit Do
            End If

            If actu >= actual.Count Then
                ' at the end for actual rows; delete any remaining in previous
                Debug.Write("") ' never tested
                If prev < previous.Count Then
                    For i = prev To previous.Count - 1
                        lstDeletes.Add(previous(i))
                    Next
                End If
                Exit Do
            End If

        Loop

        Return (lstUpdates, lstDeletes)

    End Function

    '' todo: name is too generic
    'Sub GetFile(Of T As ISortID)(name As String, ByRef lst As List(Of T))
    '    Dim compress = name.EndsWith("z")
    '    Using rd = File.Open(name, FileMode.Open)
    '        If compress Then
    '            Using zip = New GZipStream(rd, CompressionMode.Decompress, True)
    '                lst = ProtoBuf.Serializer.Deserialize(Of List(Of T))(zip)
    '            End Using
    '        Else
    '            lst = ProtoBuf.Serializer.Deserialize(Of List(Of T))(rd)
    '        End If
    '    End Using
    'End Sub

    Public Sub ProtoBufSaveFile(Of T)(lst As List(Of T), path As String, name As String, Optional extension As String = "dat", Optional compress As Boolean = True)
        Dim fname = $"{name}.{extension}{If(compress, "z", "")}"
        Dim fullpath = IO.Path.Combine(path, fname)
        If compress Then
            Using fs = IO.File.Create(fullpath)
                Using zip = New GZipStream(fs, CompressionMode.Compress, True)
                    ProtoBuf.Serializer.Serialize(zip, lst)
                End Using
            End Using
        Else
            Using fs = IO.File.Create(fullpath)
                ProtoBuf.Serializer.Serialize(fs, lst)
            End Using
        End If
    End Sub

    Public Function ProtoBufReadFile(Of T)(path As String, name As String, Optional extension As String = "dat", Optional deleteAfterRead As Boolean = False) As List(Of T)

        Dim fname = $"{name}.{extension}"
        Dim fullpath = IO.Path.Combine(path, fname)

        Dim lst = New List(Of T)

        If Not IO.File.Exists(fullpath) Then
            fullpath &= "z" ' check for compressed version
            If IO.File.Exists(fullpath) Then
                Using rd = File.Open(fullpath, FileMode.Open)
                    Using zip = New GZipStream(rd, CompressionMode.Decompress, True)
                        lst = ProtoBuf.Serializer.Deserialize(Of List(Of T))(zip)
                    End Using
                End Using
            Else
                Throw New FileNotFoundException($"{fullpath} not found.")
                Return Nothing
            End If
        Else
            ' uncompressed version
            Using rd = File.Open(fullpath, FileMode.Open)
                lst = ProtoBuf.Serializer.Deserialize(Of List(Of T))(rd)
            End Using
        End If

        If deleteAfterRead Then IO.File.Delete(fullpath)

        Return lst

    End Function

    ' Serializes and deserializes a protobuf list to normalize the values
    Sub NormalizeList(Of T)(ByRef liste As List(Of T))
        Using ms = New IO.MemoryStream
            ProtoBuf.Serializer.Serialize(ms, liste)
            ms.Position = 0 ' reset position for read
            liste = ProtoBuf.Serializer.Deserialize(Of List(Of T))(ms)
        End Using
    End Sub

    ' Process items individually, saves a lot of memory on big lists
    Sub NormalizeByItem(Of T)(ByRef liste As List(Of T))
        For i = 0 To liste.Count - 1
            Dim item = liste(i)
            Using ms = New IO.MemoryStream
                ProtoBuf.Serializer.Serialize(ms, item)
                ms.Position = 0 ' reset position for read
                liste(i) = ProtoBuf.Serializer.Deserialize(Of T)(ms)
            End Using
        Next
    End Sub

    Sub NormalizeDecimals(Of T)(ByRef liste As List(Of T))

        'Dim PreciseOne = 1.0000000000000000000000000001D - 0.0000000000000000000000000001D ' 27 zeroes

        If liste.Count = 0 Then Exit Sub
        Dim obj = liste.First
        Dim typObj = obj.GetType
        Dim props = typObj.GetProperties

        Dim decimals = New List(Of PropertyInfo)
        For Each p In props
            Dim typProp = If(Nullable.GetUnderlyingType(p.PropertyType), p.PropertyType)
            If typProp Is GetType(Decimal) Then
                decimals.Add(p)
            End If
        Next

        For Each itm In liste
            For Each p In decimals
                Dim dec = DirectCast(p.GetValue(itm, Nothing), Decimal)
                'dec /= PreciseOne ' remove trailing zeroes
                dec = dec + 0.000000005D ' we want to keep 8 decimal digits
                dec = Math.Floor(dec * 100000000)
                dec = dec / 100000000
                p.SetValue(itm, dec)
            Next
        Next
    End Sub

    Public Sub SaveRecordsToDisk(Of T)(lst As List(Of T), table As String, path As String)
        Using New Chrono(Sub(ms) Log($"     Saving {table} to disk took {ms} ms"), Sub() Log($"     Saving {table} to disk for next pass..."))
            ProtoBufSaveFile(lst, path, $"pex{table}")
        End Using
    End Sub

    Public Sub UpdateTables(Of T As ISortID)(ByRef newLst As List(Of T), table As String, key1 As String, key2 As String, cns As String)

        Using New Chrono(Sub(ms) Log($"        Elapsed:  {ms:#0}ms"), Sub() Log($" - writing {table}", color:=ConsoleColor.White))

            ' set UniqueID
            If key2 = "" Then
                'single key
                For Each row As ISortID In newLst
                    row.UniqueID = row.SortID
                Next
            Else
                'dual key
                For Each row As ISortID In newLst
                    row.UniqueID = GetDualIdKey(row.SortID, row.SortID2)
                Next
            End If

            Using New Chrono(Sub(ms) Log($"     NORMALIZE List(Of pex{table}) took {ms} ms"))
                NormalizeByItem(newLst) ' protobuf faster than NormalizeDecimals
            End Using

            Dim hh = Now.Hour
            Dim iniHours = $",{INI.ReadKey("Config", "FullStagingReloadAtHours", "x")}," ' "6" devient ",6,"; "6,9,12" devient ",6,9,12,"

            If INI.ReadKey("Config", "NoTableCaching", "0") = "1" OrElse iniHours.Contains($",{hh},") Then
                GoTo Staging_Reload ' temporaire - methode updates/deletes débalance...?
            End If

            Dim oldLst As List(Of T) = Nothing
            Dim outFile = $"{outPath}pex{table}.dat"
            If Not IO.File.Exists(outFile) Then outFile &= "z" ' check for compressed version (.datz)
            If IO.File.Exists(outFile) Then
                'GetFile(outFile, oldLst) ' get from disk
                oldLst = ProtoBufReadFile(Of T)(outPath, $"pex{table}", deleteAfterRead:=True) ' remove from disk... do not let old version lie around

                Dim diffs = GetUpdatesAndDeletes(oldLst, newLst)
                Log($"     {table} - Updates: {diffs.Updates.Count}   Deletes: {diffs.Deletes.Count}")

                Dim changes = diffs.Updates.Count + diffs.Deletes.Count

                If changes > 0 Then

                    If changes > 2000 Then GoTo Staging_Reload '-todo .ini param

                    Using tx = ppdb_report.GetTransaction

                        Dim lstDel = diffs.Updates.Select(Function(x) x.UniqueID).ToList
                        lstDel.AddRange(diffs.Deletes.Select(Function(x) x.UniqueID))
                        Dim csvDel = lstDel.ToCSV
                        Dim sqlDel = $"delete from {table} where UniqueID in ({csvDel})"

                        Using New Chrono(Sub(ms) Log($"     DELETE {table} took {ms} ms"))
                            ppdb_report.Execute(sqlDel)
                        End Using

                        Using New Chrono(Sub(ms) Log($"     BulkInsert {table} took {ms} ms"))
                            BulkCopy(diffs.Updates, tx, table)
                        End Using

                        tx.Complete()

                    End Using

                End If

                diffs.Deletes.Clear()
                diffs.Updates.Clear()

                oldLst.Clear()

            Else
Staging_Reload:

                Using New Chrono(Sub(ms) Log($"     Staging & Swap {table} took {ms} ms"), Sub() Log($"     Reloading {table} using staging and swap..."))
                    SaveToDB(newLst, cns, $"staging_{table}", truncateTable:=True)
                    SwapStagingTables(cns, {table})
                End Using

            End If

        End Using

    End Sub


    Public Function CalculDevise_Can(montant As Decimal, taux As Decimal) As Decimal
        Return montant * taux
    End Function

    Public Function CalculDevise_US(montant As Decimal, taux As Decimal) As Decimal
        If taux = 0 Then Return 0
        Return montant / taux
    End Function

    'Public Function CalculateDevise(montant As Decimal, taux As Decimal, op As Func(Of Decimal, Decimal, Decimal)) As Decimal
    '    Return op(montant, taux)
    'End Function

End Module

