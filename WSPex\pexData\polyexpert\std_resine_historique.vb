﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_std_resine_historique")>
<ProtoContract>
Partial Public Class std_resine_historique
    <ProtoMember(1)> Public Property id_std_resine_historique As Integer
    <ProtoMember(2)> Public Property id_resine As Integer
    <ProtoMember(3)> Public Property cout_us As Decimal
    <ProtoMember(4)> Public Property date_debut As DateTime
    <ProtoMember(5)> Public Property date_fin As DateTime
    <ProtoMember(6)> Public Property cout_liste_us As Decimal
    <ProtoMember(7)> Public Property commentaire As String
End Class
