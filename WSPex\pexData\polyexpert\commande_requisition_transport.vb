﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.PrimaryKey("ID_COMMANDE_REQUISITION_TRANSPORT")>
Partial Public Class commande_requisition_transport
    <ProtoMember(1)> Public Property id_commande_requisition_transport As Integer
    <ProtoMember(2)> Public Property id_commande As Integer
    <ProtoMember(3)> Public Property id_requisition_transport As Integer
    '<ProtoMember(4)> Public Property maxPalette As Integer
    '<ProtoMember(5)> Public Property maxPoids As Integer
    '<ProtoMember(6)> Public Property ordre As Integer
    '<ProtoMember(7)> Public Property RowVersion As Byte()
    '<ProtoMember(8)> Public Property releaseNb As String
End Class