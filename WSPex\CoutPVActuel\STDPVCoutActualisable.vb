﻿Module STDPVCoutActualisable

	Private Const BAD_COUT As Decimal = 9999.99D

	Private ReadOnly lock As New Object

	' retourne OK ou ERROR
	Public Function CalculSTDPVCoutActualisable(Optional idPV As Integer = -1) As String ' eventuellement, voir pour passer une liste de PV...

		SyncLock lock ' une seule exécution à la fois.

			Try

				Dim check_pv = 210945

				Dim cutOff = #2023-09-01# ' debut du mois actuel? mois passé? rolling 3 months (2023-09-01 étant le "floor", on ne recule pas plus loin)
				Dim EPSILON = 0.005D ' 0.005D ? était 0.0002D

				Dim lstPVHis = ppdb.Fetch(Of pv_historique_mensuel)("where id_client = 200251 and date_fin > @0 and (id_pv = @1 or @1 = -1)", cutOff, idPV)
				Dim dicPVHis = lstPVHis.GroupBy(Function(x) x.id_pv).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)

				Dim lstPrdHis As List(Of produit_historique_mensuel)
				If idPV = -1 Then
					lstPrdHis = ppdb.Fetch(Of produit_historique_mensuel)("where date_fin > @0", cutOff)
				Else
					lstPrdHis = ppdb.Fetch(Of produit_historique_mensuel)("where date_fin > @0 and id_produit = @1", cutOff, dicPVHis(idPV).First.id_produit)
				End If
				Dim dicPrdHis = lstPrdHis.GroupBy(Function(x) x.id_produit).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)

				' Historique des recettes
				Dim sql = <a><![CDATA[
select  
 pv.id_std_pv_resine_historique
,pv.id_std_pv_resine
,pv.id_pv
,pv.pourc_resine
,coalesce(pv.resine_type_id, rsn.resine_type_id) resine_type_id
,pv.id_resine
,pv.actif 
,dbo.greatest(pv.date_debut, rsn.date_debut) date_debut
,dbo.least(pv.date_fin, rsn.date_fin)        date_fin      
from std_pv_resine_historique        pv 
left join resine_historique_mensuel rsn on  rsn.id_resine   = pv.id_resine 
                                       and (rsn.date_debut <= pv.date_fin) 
                                       and (rsn.date_fin   >= pv.date_debut)
 where pv.date_fin > @0 
   and pv.id_pv > 0 
   and (pv.id_pv = @1 or @1 = -1)
]]></a>.Value.Trim
				'Dim lstRctStd = ppdb.Fetch(Of std_pv_resine_historique)("where date_fin > @0 and id_pv > 0 and (id_pv = @1 or @1 = -1)", cutOff, idPV)
				Dim lstRctStd = ppdb.Fetch(Of std_pv_resine_historique)(sql, cutOff, idPV)
				Dim dicRctStd = lstRctStd.GroupBy(Function(x) x.id_pv).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)

				' NOTE: spMAJVieuxPrixResine met les prix plus vieux que <polyexpert.dbo.preference.delai_maj_resine_prix_invalide> à 9999.99
				sql = <a><![CDATA[
select 
 srh.id_std_resine_historique
,srh.id_resine
,srh.cout_us
,dbo.greatest(srh.date_debut, his.date_debut) date_debut
,dbo.least(srh.date_fin, his.date_fin)        date_fin
,srh.cout_liste_us
,srh.commentaire
,srh.rowversion
,his.resine_type_id
from std_resine_historique          srh
left join resine_historique_mensuel his on  his.id_resine   = srh.id_resine 
                                       and (his.date_debut <= srh.date_fin)
                                       and (his.date_fin   >= srh.date_debut)
where  srh.date_fin > @0
]]></a>.Value.Trim

				'Dim lstStdRsnHis = ppdb.Fetch(Of std_resine_historique)("where date_fin > @0", cutOff)
				Dim lstStdRsnHis = ppdb.Fetch(Of std_resine_historique)(sql, cutOff)
				Dim dicStdRsnHis = lstStdRsnHis.GroupBy(Function(x) x.id_resine).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)

				' NOTE: spMAJVieuxPrixResine met les prix plus vieux que <polyexpert.dbo.preference.delai_maj_resine_prix_invalide> à 9999.99
				Dim lstRsnTypHis = ppdb.Fetch(Of resine_type_historique)("where date_fin > @0", cutOff)
				Dim dicRsnTypHis = lstRsnTypHis.GroupBy(Function(x) x.id_resine_type).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)

				Dim lstRsnTypHisMens = ppdb.Fetch(Of resine_type_historique_mensuel)("where date_fin > @0", cutOff)
				Dim dicRsnTypHisMens = lstRsnTypHisMens.GroupBy(Function(x) x.resine_type_id).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)

				'Dim delaiMAJResinePrixInvalide = ppdb.ExecuteScalar(Of Integer)("select delai_maj_resine_prix_invalide from preference").Dump("delaiMAJResinePrixInvalide")

				Dim lstRsnHis = ppdb.Fetch(Of resine_historique_mensuel)("")
				Dim dicRsnHis = lstRsnHis.GroupBy(Function(x) x.id_resine).ToDictionary(Function(k) k.Key, Function(v) v.OrderBy(Function(x) x.date_debut).ToList)

				' Lookups Rsn/Cat -> PV
				Dim dicCatPV As New Dictionary(Of Integer, HashSet(Of Integer)) ' id_type_resine (cat), {id_pv, id_pv, ...}		
				'
				' Batir les lookups rsn->PV et cat->PV
				For Each rct In lstRctStd
					If rct.resine_type_id.HasValue Then
						AddToDic(dicCatPV, rct.resine_type_id.Value, rct.id_pv)
					Else
						Debug.Assert(rct.id_resine.HasValue)
					End If
				Next

				' Batir la liste des évènements
				Dim dicEvents = New Dictionary(Of Integer, HashSet(Of DateTime)) ' ID_PV, {#dat#, #dat#, ...}

				Dim dicAddVal = New Dictionary(Of String, (DateTime, Boolean)) ' $"{ID_PV:periode}", (date_max, add_val)   periode is (annee*100+mois ... 202309)

				'pv change(additif_valide toggles, devient actif / inactif)
				For Each itm In lstPVHis
					If (itm.id_pv = check_pv) Then Debug.Write("")

					'Debug.Assert(itm.date_debut < #2025-02-01#)

					AddToDic(dicEvents, itm.id_pv, Greatest(cutOff, itm.date_debut))

					' ajouter une entrée pour chaque mois couvert par la période
					Dim months = CInt(DateDiff(DateInterval.Month, itm.date_debut, Least(itm.date_fin, Now)))
					For m = 0 To months
						'Dim per = $"{deb.AddMonths(Cint(m)):yyyyMM}".Dump
						Dim dat = itm.date_debut.AddMonths(m)
						Dim per = $"{itm.id_pv}:{dat:yyyyMM}"
						If dicAddVal.ContainsKey(per) Then
							If itm.date_debut > dicAddVal(per).Item1 Then
								dicAddVal(per) = (itm.date_debut, itm.additif_valide)
							End If
						Else
							dicAddVal(per) = (itm.date_debut, itm.additif_valide)
						End If
					Next
				Next

				'recette change
				For Each rct In lstRctStd
					'Debug.Assert(rct.id_pv <> 211856 Or (rct.id_pv = 211856 AndAlso rct.date_debut > #2023-10-01#))
					'Debug.Assert(rct.date_debut < #2025-02-01#)

					AddToDic(dicEvents, rct.id_pv, Greatest(cutOff, rct.date_debut))
				Next

				'categorie change de prix
				For Each cat In lstRsnTypHis
					Dim hs As HashSet(Of Integer) = Nothing
					'Debug.Assert(cat.id_resine_type <> 50)
					If dicCatPV.TryGetValue(cat.id_resine_type, hs) Then
						For Each id_pv In hs
							'Debug.Assert(id_pv <> 211432)
							AddToDic(dicEvents, id_pv, Greatest(cutOff, cat.date_debut))
						Next
					End If
				Next

				'resine change de prix
				For Each rsn In lstStdRsnHis
					'Debug.Assert(rsn.id_resine <> 1101)
					Dim hs As HashSet(Of Integer) = Nothing
					If dicCatPV.TryGetValue(rsn.resine_type_id, hs) Then
						For Each id_pv In hs
							'If id_pv = 211817 Then Debug.Write("")
							AddToDic(dicEvents, id_pv, Greatest(cutOff, rsn.date_debut))
						Next
					End If
				Next

				' Batir les STD_PV_COUT_ACTUALISABLE
				'	Note: pour corriger les produits dans le passé, modifier les tables PV_HISTORIQUE_MENSUEL, PRODUIT_HISTORIQUE_MENSUEL
				'		  et STD_PV_RESINE_HISTORIQUE pour le PV cible en mettant les dates de début au 1er du mois voulu.
				'
				'   Note 2: pour PV_HISTORIQUE_MENSUEL il faut aussi corriger les autres PV (ceux des clients autres que 200251)
				'           select * from PV_HISTORIQUE_MENSUEL where coalesce(id_pv_standard, id_pv) = 212005
				'           (S'assurer qu'il n'y a pas de chevauchement de dates)
				'
				'	select * From STD_PV_COUT Where id_pv_std = 212005
				'   select * from PV_HISTORIQUE_MENSUEL where coalesce(id_pv_standard, id_pv) = 212005 -- attention, plusieurs PVs
				'	select * From PRODUIT_HISTORIQUE_MENSUEL Where ID_PRODUIT = 201262 (id_produit dans la table PV_HISTORIQUE_MENSUEL)
				'	select * From STD_PV_RESINE_HISTORIQUE Where id_pv = 212005
				'   update STD_PV_RESINE_HISTORIQUE set date_debut = '2023-11-01' where id_pv = 212005
				'
				'   roulez PolyExperReport et revérifier avec select * from STD_PV_COUT...
				'
				Dim lstSPCA = New List(Of std_pv_cout_actualisable)
				For Each kvp In dicEvents

					Dim id_pv = kvp.Key
					If id_pv = check_pv Then Debug.Write("")
					Dim lstDat = kvp.Value.OrderBy(Function(x) x).ToList
					Dim lstTmp = New List(Of std_pv_cout_actualisable)
					Dim prdHis As produit_historique_mensuel = Nothing

					If lstDat.Count > 1 Then

						Dim last = lstDat.Last

						'Debug.Assert(last < #2025-02-01#)

						For i = lstDat.Count - 2 To 0 Step -1
							Dim dat = lstDat(i)

							'Debug.Assert(dat < #2025-02-01#)

							If Month(dat) = Month(last) AndAlso Year(dat) = Year(last) Then
								lstDat.RemoveAt(i) ' date is in same month, we remove it
							Else
								' we found a different month, we keep it and set "last" to point to it
								last = lstDat(i)
							End If
						Next

					End If

					For Each dat In lstDat

						If prdHis Is Nothing OrElse prdHis.date_fin <= dat Then
							Dim lst = New List(Of produit_historique_mensuel)

							Dim pvStd = dicPVHis(id_pv) '.Dump("pv")
							Dim id_pv_std = pvStd.Last.id_pv_standard

							If id_pv_std > 0 Then
								If Not dicPVHis.ContainsKey(id_pv_std) Then Continue For ' pv indiquant un pv_standard qui n'existe pas dans la table
								pvStd = dicPVHis(id_pv_std) '.Dump("std")
								id_pv_std = pvStd.Last.id_pv_standard
							End If
							If id_pv_std > 0 Then Debug.Assert(False) ' wtf
							id_pv = pvStd.Last.id_pv

							Dim id_prd = 0 ' on cherche par date, et si on ne trouve pas, on prend le id_produit le plus récent
							Dim std = pvStd.Where(Function(x) x.date_debut <= dat AndAlso dat < x.date_fin).SingleOrDefault
							If std IsNot Nothing Then
								id_prd = std.id_produit
							Else
								id_prd = pvStd.Last.id_produit
							End If

							If dicPrdHis.TryGetValue(id_prd, lst) Then
								prdHis = lst.Where(Function(x) x.date_debut <= dat AndAlso dat < x.date_fin).SingleOrDefault
							Else
								Continue For
							End If
						End If

						' 1. Trouver recette de la période
						Dim rct As List(Of std_pv_resine_historique) = Nothing
						Dim wrk As New List(Of std_pv_resine_historique) ' recette de travail, peut etre identique a rct ou est rct "rebalancée"
						If dicRctStd.TryGetValue(id_pv, rct) Then
							rct = rct.Where(Function(x) x.date_debut <= dat AndAlso dat < If(x.date_fin, #9999-12-31#)).ToList
						End If

						Dim spca = New std_pv_cout_actualisable With {.date_maj = Now}

						If id_pv = check_pv Then Debug.Write("")

						Dim rebalanced = False
						Dim rsnInactive = False ' au moins une des résines de la recette est inactive.
						If rct?.Count > 0 Then

							' 2. rebalancer à 100%
							wrk.AddRange(rct.Select(Function(x) x.GetClone))
							Dim sum = CDec(wrk.Sum(Function(x) x.pourc_resine))
							If Math.Abs(sum - 100D) > EPSILON Then

								If id_pv = check_pv Then Debug.Write("")

								spca.erreurs &= $"[rct={sum:0.0000}%] "

								For Each r In wrk
									r.pct_rsn_fixed = Math.Round(CDec(r.pourc_resine) / sum * 100, 4, MidpointRounding.AwayFromZero)
									r.pourc_resine = r.pct_rsn_fixed
								Next

								sum = CDec(wrk.Sum(Function(x) x.pourc_resine))
								If Math.Abs(sum - 100D) > EPSILON Then

									If id_pv = check_pv Then Debug.Write("")

									spca.erreurs &= $"[rct={sum:0.0000}% ?] "
									Debug.Assert(False) '-todo mauvais rebalancement - ne devrait jamais se produire
								End If

								rebalanced = True

							End If

							' 3. Calcul du cout TODO: si pas de cout pour la résine, prendre celui de la categorie. (RESINE_TYPE_PRODUIT?)				
							For Each itm In wrk

								' 3a. Lookups des couts rsn + cat	
								Dim cou_rsn = BAD_COUT
								Dim cou_cat = BAD_COUT
								Dim cou_rsn_cat = BAD_COUT
								Dim err = ""

								If itm.id_resine.HasValue Then

									'Dim rsnHis = dicRsnHis(itm.id_resine.Value).Where(Function(x) (itm.date_debut <= x.date_fin) And (itm.date_fin.Value >= x.date_debut)).Last
									Dim rsnHis = dicRsnHis(itm.id_resine.Value).Where(Function(x) (dat <= x.date_fin) And (dat >= x.date_debut)).Last
									If Not rsnHis.actif Then
										rsnInactive = True
										spca.erreurs &= $"[rsn#{rsnHis.id_resine}_inac] "
									End If

									cou_rsn = GetCoutResine(dicStdRsnHis, itm.id_resine.Value, dat, err)
									If err <> "" Then spca.erreurs &= err
									If cou_rsn = BAD_COUT Then
										' get cout from cat							
										err = ""
										cou_rsn = GetCoutResineType(dicRsnTypHis, dicRsnTypHisMens, rsnHis.resine_type_id, dat, err)
										If err <> "" Then spca.erreurs &= err
									End If
								ElseIf itm.resine_type_id.HasValue Then
									err = ""
									cou_cat = GetCoutResineType(dicRsnTypHis, dicRsnTypHisMens, itm.resine_type_id.Value, dat, err)
									If err <> "" Then spca.erreurs &= err
								End If

								If cou_rsn < 9000D Then
									itm.cout_base = cou_rsn
								Else
									itm.cout_base = cou_cat
								End If

								itm.cout_ratio = itm.cout_base * CDec(itm.pourc_resine) / 100D
							Next

						End If

						' 5. Création du record
						With spca

							'Dim tmp = dicPVHis

							Dim PV = dicPVHis(id_pv).Where(Function(x) dat >= x.date_debut AndAlso dat <= x.date_fin).SingleOrDefault ' had crash here event before PV existed
							If PV Is Nothing Then Continue For ' event avant la création du PV
							.id_pv_std = id_pv
							.pv = "" & PV.chaine_pv ' get rid of null value
							.date_debut = New Date(Year(dat), Month(dat), 1)

							'Debug.Assert(.date_debut < #2025-02-01#)

							If rct?.Count > 0 Then
								Dim cout_us = Math.Round(wrk.Sum(Function(x) x.cout_ratio), 4, MidpointRounding.AwayFromZero)
								If cout_us < 10 Then .cout = cout_us Else .cout = BAD_COUT : spca.erreurs &= "[cout_rsn] "
								If rebalanced Then .cout = BAD_COUT
								If rsnInactive Then .cout = BAD_COUT
							Else
								spca.erreurs &= "[rct_manq] "
								.cout = BAD_COUT
							End If
							If PV.inactif Then
								spca.erreurs &= "[pv_inac] "
								.cout = BAD_COUT
							End If

							Dim key = $"{ .id_pv_std}:{ .date_debut:yyyyMM}"
							If dicAddVal.ContainsKey(key) Then
								If dicAddVal(key).Item2 = False Then
									spca.erreurs &= "[additif_inval] "
									.cout = BAD_COUT
								End If
							Else
								Debug.Assert(False) ' entrée manquante dans dicValAdd
								spca.erreurs &= "[err_code_1] "
							End If

							If prdHis Is Nothing Then
								spca.erreurs &= $"[prd_his_cat_manq_def_5] "
								.id_std_categorie = 5
							Else
								.id_std_categorie = prdHis.id_std_categorie
							End If
						End With

						lstTmp.Add(spca)
					Next

					' date_fin sera assigné par trigger

					lstSPCA.AddRange(lstTmp)

				Next

#Region "Compare/Merge Data" ' TODO: Voir pour merge des records si les données n'ont pas changé depuis le mois précédent...

				' Vérifier pour merge des records
				lstSPCA = lstSPCA.OrderBy(Function(x) x.id_pv_std).ThenBy(Function(x) x.date_debut).ToList
				For i = lstSPCA.Count - 2 To 0 Step -1
					Dim rowA = lstSPCA(i)
					Dim rowB = lstSPCA(i + 1)
					If rowA.id_pv_std <> rowB.id_pv_std Then Continue For ' pas le même PV
					If rowB.Equals(rowA) Then lstSPCA.RemoveAt(i + 1) ' le row le plus récent est égale au précédent, on l'enlève.
				Next

				' Compare with existing, update only changes.
				Dim lstAct = ppdb.Fetch(Of std_pv_cout_actualisable)("where id_pv_std = @0 or @0 = -1", idPV)
				Dim lstUpdates As List(Of std_pv_cout_actualisable)
				Dim lstInsertIDs As New List(Of (id_pv_std As Integer, date_debut As DateTime))
				Dim lstDeleteIDs As New List(Of (id_pv_std As Integer, date_debut As DateTime))
				If lstAct.Count = 0 Then
					' table est vide
					lstUpdates = lstSPCA
				Else
					lstInsertIDs = lstSPCA.Select(Function(x) (x.id_pv_std, x.date_debut)).Except(lstAct.Select(Function(x) (x.id_pv_std, x.date_debut))).ToList ' new pvID+DateDeb to add
					lstDeleteIDs = lstAct.Select(Function(x) (x.id_pv_std, x.date_debut)).Except(lstSPCA.Select(Function(x) (x.id_pv_std, x.date_debut))).ToList ' old IDs to delete
					lstUpdates = New List(Of std_pv_cout_actualisable)
					For Each act In lstAct
						Dim neo = lstSPCA.Where(Function(x) x.id_pv_std = act.id_pv_std AndAlso x.date_debut = act.date_debut).SingleOrDefault
						If neo IsNot Nothing AndAlso Not act.Equals(neo) Then lstUpdates.Add(neo)
					Next
				End If
#End Region

#Region "Update Table"
				If lstUpdates.Any OrElse lstInsertIDs.Any OrElse lstDeleteIDs.Any Then
					Dim table = "STD_PV_COUT_ACTUALISABLE"
					Using tx = ppdb.GetTransaction

						' Remove deleted and updated
						If lstAct.Any Then ' si la liste des actuels est vide, on n'as pas de DELETE a faire...
							lstDeleteIDs.AddRange(lstUpdates.Select(Function(x) (x.id_pv_std, x.date_debut)))
							For Each del In lstDeleteIDs
								Dim rec = ppdb.Execute($"delete from {table} where id_pv_std = @0 and date_debut = @1", del.id_pv_std, del.date_debut)
								If rec <> 1 Then Debug.Assert(False)
							Next
						End If

						' Insert updated and inserted
						If lstInsertIDs.Any Then lstUpdates.AddRange(lstSPCA.Where(Function(x) lstInsertIDs.Contains((x.id_pv_std, x.date_debut))))
						If lstUpdates.Any Then
							' date_fin: On ne déclenche pas les triggers durant le bulk copy, et on déclenche de force après
							Try
								UtilitiesData.BulkCopy(lstUpdates, tx, table, skipIdentity:=True, fireTriggers:=False)
								ppdb.Execute($"update {table} set tmp = tmp")
							Catch ex As Exception
								Log($"CalculSTDPVCoutActualisable - BulkCopy() - {ex}")
							End Try
						End If
						tx.Complete()
					End Using
				End If
#End Region

#Region "Cleanup"

				lstPVHis.Clear()
				dicPVHis.Clear()
				lstPrdHis.Clear()
				dicPrdHis.Clear()
				lstRctStd.Clear()
				dicRctStd.Clear()
				lstStdRsnHis.Clear()
				dicStdRsnHis.Clear()
				lstRsnTypHis.Clear()
				dicRsnTypHis.Clear()
				lstRsnTypHisMens.Clear()
				dicRsnTypHisMens.Clear()
				lstRsnHis.Clear()
				dicRsnHis.Clear()
				dicCatPV.Clear()
				dicEvents.Clear()
				dicAddVal.Clear()
				lstSPCA.Clear()

#End Region

			Catch ex As Exception
				Return "ERROR"
			End Try

			Return "OK"

		End SyncLock

	End Function

	Private Function Greatest(dat1 As DateTime, dat2 As DateTime) As DateTime
		Return If(dat1 > dat2, dat1, dat2)
	End Function

	Private Function Least(dat1 As DateTime, dat2 As DateTime) As DateTime
		Return If(dat1 < dat2, dat1, dat2)
	End Function

	Private Function GetCoutResine(dicRsnHis As Dictionary(Of Integer, List(Of std_resine_historique)), id_resine As Integer, dat As DateTime, ByRef err As String) As Decimal

		Dim cou_rsn = BAD_COUT

		Dim lstStdRsn As List(Of std_resine_historique) = Nothing
		If dicRsnHis.TryGetValue(id_resine, lstStdRsn) Then

			Dim rsn As std_resine_historique = Nothing
			Try
				rsn = lstStdRsn.Where(Function(x) x.date_debut <= dat AndAlso dat < If(x.date_fin, #9999-12-31#)).SingleOrDefault
			Catch
				Debug.Assert(False)
			End Try
			If rsn Is Nothing Then
				err = $"[histo_rsn#{id_resine}_manq_{dat:yyyyMMdd}] "
			Else
				cou_rsn = If(rsn.cout_us, BAD_COUT)
			End If
			'If cou_rsn > 9000 Then err = $"rns_cou_9999_{id_resine} "
		Else
			err = $"[histo_rsn#{id_resine}_manq] "
		End If

		Return cou_rsn

	End Function

	Private Function GetCoutResineType(dicRTH As Dictionary(Of Integer, List(Of resine_type_historique)), dicRTHM As Dictionary(Of Integer, List(Of resine_type_historique_mensuel)), resine_type_id As Integer, dat As DateTime, ByRef err As String) As Decimal
		Dim cou_cat = BAD_COUT
		Dim lstRTH As List(Of resine_type_historique) = Nothing
		If dicRTH.TryGetValue(resine_type_id, lstRTH) Then

			Dim rth As resine_type_historique = Nothing
			Try
				rth = lstRTH.Where(Function(x) x.date_debut <= dat AndAlso dat < If(x.date_fin, #9999-12-31#)).SingleOrDefault
			Catch
				Debug.Assert(False)
			End Try
			If rth Is Nothing Then Debug.Assert(False)
			Dim rthm = dicRTHM(resine_type_id).Where(Function(x) x.date_debut <= dat AndAlso dat < x.date_fin).SingleOrDefault
			If rthm.actif AndAlso rth.cout_us_std < 100 Then
				cou_cat = rth.cout_us_std
			Else
				err &= $"[rth#{resine_type_id}_inactive] "
			End If
		Else
			err &= $"[rth#{resine_type_id}_histo_manq] "
		End If
		Return cou_cat
	End Function

	Private Sub AddToDic(Of T As Dictionary(Of Integer, HashSet(Of Integer)))(dic As T, key As Integer, value As Integer)
		If dic.ContainsKey(key) Then
			dic(key).Add(value)
		Else
			dic(key) = New HashSet(Of Integer) From {value}
		End If
	End Sub

	Private Sub AddToDic(Of T As Dictionary(Of Integer, HashSet(Of DateTime)))(dic As T, key As Integer, value As DateTime)
		If dic.ContainsKey(key) Then

			'Debug.Assert(value < #2025-02-01#)

			dic(key).Add(value)
		Else
			dic(key) = New HashSet(Of DateTime) From {value}
		End If
	End Sub

	<PetaPoco.PrimaryKey("id_std_pv_cout")>
	Partial Public Class std_pv_cout_actualisable
		Implements IEquatable(Of std_pv_cout_actualisable)

		Public Property id_std_pv_cout As Integer
		Public Property pv As String
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime?
		Public Property cout As Decimal ' $US from calcul PV + Recettes + cout resines + couts categories
		Public Property id_std_categorie As Integer ' from STD_CATEGORIE via PRODUIT_HISTORIQUE
		Public Property tmp As Integer?
		Public Property sous_marche_auto As String  ' from produit historique? -- n'est pas utilisé?
		Public Property id_pv_std As Integer
		Public Property date_maj As DateTime?
		Public Property erreurs As String

		Public Overloads Function Equals(other As std_pv_cout_actualisable) As Boolean Implements IEquatable(Of std_pv_cout_actualisable).Equals

			' on ignore le identity
			'If _id_std_pv_cout <> other.id_std_pv_cout Then Return False

			' id_pv_std + date_debut est la "primary key" de l'objet
			'If _id_pv_std <> other.id_pv_std Then Return False
			'If _date_debut <> other.date_debut Then Return False

			' on ignore la date de fin
			'If _date_fin <> other.date_fin Then Return False

			' on vérifie en ordre de plus susceptible de changer
			If _cout <> other.cout Then Return False
			If _id_std_categorie <> other.id_std_categorie Then Return False
			If _pv <> other.pv Then Return False

			' on ignore erreur et date_maj... s'il change, autre chose doit aussi avoir changé...
			'If _date_maj <> other.date_maj Then Return False
			If _erreurs <> other.erreurs Then Return False

			' autres vieux champs à ignorer	
			'if _tmp <> other
			'if _sous_marche_auto <> other

			Return True
		End Function

	End Class

	<PetaPoco.PrimaryKey("id_std_pv_resine_historique")>
	Private Class std_pv_resine_historique
		Public Property id_std_pv_resine_historique As Integer
		'Public Property id_std_pv_resine As Integer
		Public Property id_pv As Integer
		Public Property pourc_resine As Single
		Public Property resine_type_id As Integer?
		Public Property id_resine As Integer?

		<PetaPoco.Ignore> Public Property pct_rsn_fixed As Decimal

		<PetaPoco.Ignore> Public Property cout_base As Decimal
		<PetaPoco.Ignore> Public Property cout_ratio As Decimal

		Public Property actif As Boolean
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime?
		'Public Property rowversion As Byte()

		Public Function GetClone() As std_pv_resine_historique
			Return DirectCast(Me.MemberwiseClone, std_pv_resine_historique)
		End Function
	End Class

	<PetaPoco.PrimaryKey("id_historique_mensuel")>
	Private Class produit_historique_mensuel
		Public Property id_produit As Integer
		Public Property nom As String
		Public Property inactif As Boolean
		Public Property nb_couche As Integer
		Public Property id_std_categorie As Integer
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime
		Public Property id_historique_mensuel As Integer
	End Class

	<PetaPoco.PrimaryKey("id_historique_mensuel")>
	Public Class pv_historique_mensuel
		Public Property id_pv As Integer
		Public Property id_client As Integer
		Public Property id_produit As Integer
		Public Property chaine_pv As String
		Public Property inactif As Boolean
		Public Property id_pv_standard As Integer
		Public Property additif_valide As Boolean
		Public Property id_resine_recyclage As Integer
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime
		Public Property id_historique_mensuel As Integer
	End Class

	<PetaPoco.PrimaryKey("id_std_resine_historique")>
	Private Class std_resine_historique
		Public Property id_std_resine_historique As Integer
		Public Property id_resine As Integer
		Public Property cout_us As Decimal?
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime?
		'Public Property cout_liste_us As Decimal?
		'Public Property commentaire As String
		'Public Property rowversion As Byte()
		<PetaPoco.ResultColumn> Public Property resine_type_id As Integer
	End Class

	<PetaPoco.PrimaryKey("id_resine_type_historique")>
	Private Class resine_type_historique
		Public Property id_resine_type_historique As Integer
		Public Property id_resine_type As Integer
		'Public Property prix_lbs_us_normalise As Decimal
		'Public Property date_prix_lbs_us_normalise As DateTime
		'Public Property cout_us_cdi As Decimal
		'Public Property index_pe As Decimal
		Public Property cout_us_std As Decimal
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime?
		'Public Property commentaire As String
		'Public Property nom As String
		'Public Property prefix As String
		'Public Property std_resine As Boolean?
		'Public Property actif As Boolean?
		'Public Property hd As Boolean?
		'Public Property preblend As Boolean?
		'Public Property rowversion As Byte()
	End Class

	<PetaPoco.PrimaryKey("id_historique_mensuel")>
	Private Class resine_historique_mensuel
		Public Property id_resine As Integer
		Public Property nom As String
		Public Property densite As Decimal
		'Public Property id_fournisseur As Integer
		'Public Property densite_lbs_pied_cube As Decimal?
		'Public Property prix_lbs_can As Decimal?
		'Public Property prix_lbs_us As Decimal?
		Public Property actif As Boolean
		'Public Property resine_virtuelle As Boolean?
		'Public Property additif As Boolean?
		'Public Property id_resine_relier As Integer?
		'Public Property resine_std As Boolean?
		'Public Property id_std_resine As Integer?
		Public Property resine_type_id As Integer
		'Public Property id_type_rejet_resine_densite As Integer?
		'Public Property id_type_rejet_resine_couleur As Integer?
		'Public Property id_type_rejet_dispo_restriction As Integer?
		'Public Property id_resine_groupe As Integer?
		'Public Property is_trim As Boolean?
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime
		Public Property id_historique_mensuel As Integer
	End Class

	<PetaPoco.PrimaryKey("id_historique_mensuel")>
	Private Class resine_type_historique_mensuel
		Public Property resine_type_id As Integer
		'Public Property nom As String
		'Public Property prix_lbs_us_normalise As Decimal?
		'Public Property prefix As String
		'Public Property tmp As Decimal?
		Public Property std_resine As Boolean
		Public Property actif As Boolean
		'Public Property ordre As Decimal?
		'Public Property hd As Boolean?
		'Public Property preblend As Boolean?
		Public Property date_debut As DateTime
		Public Property date_fin As DateTime
		Public Property id_historique_mensuel As Integer
	End Class

End Module
