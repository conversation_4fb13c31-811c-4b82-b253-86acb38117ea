﻿Imports System.IO.Compression
Imports Utilities
Imports UtilitiesData
Imports WSPex.CoutTransport

Module pexReportECalculFinal

    Dim lstPexCM As List(Of pexCommandeMachine)

    Public Sub CleanMemory()
        lstPexCM.Clear()
        lstPexCM = Nothing
    End Sub

    Public Sub CalculFinal()

        Using New Chrono(Sub(ms) Log($"CalculFinal done. Elapsed: {ms:#0}ms"), Sub() Log("CalculFinal started...", color:=ConsoleColor.White))

            Using New Chrono(Sub(ms) Log($"CalculFinal Update done. Elapsed: {ms:#0}ms"), Sub() Log("CalculFinal Update started...", color:=ConsoleColor.White))

                UpdateCommande()
                UpdateFacture()

                '* Construire l'objet pour sa sauvegarde vu qu'il n'était pas utilisé pour les calculs.
                lstPexCM = New List(Of pexCommandeMachine)
                For Each oItem In dicPexCM
                    lstPexCM.Add(oItem.Value)
                Next

                '* Remplacer les "01-01-0001" pour les dates nulles.
                UpdateDateNulle()

            End Using

            'Debug.Assert(False) ' will write to DB

            If Not DataOKBeforeSave() Then
                Log("Processus interrompu avant la sauvegarde, car des problèmes sont survenus.", color:=ConsoleColor.White)
            Else

                Using New Chrono(Sub(ms) Log($"DB writing done. Data is ready. Elapsed: {ms:#0}ms"), Sub() Log("DB writing started...", color:=ConsoleColor.White))

                    Dim cns = ppdb_report.GetConnectionString

                    ' Rouler en parallèle
                    ' Dim t1 = Task.Factory.StartNew(Sub() UpdateTables(lstPexRF, "RequisitionFacture", "RequisitionID", "FactureID", cns))
                    Dim t2 = Task.Factory.StartNew(Sub() UpdateTables(lstPexCM, "CommandeMachine", "CommandeMachineID", "CommandeID", cns))
                    'Dim t3 = Task.Factory.StartNew(Sub() UpdateTables(lstPexFac, "Facture", "FactureID", "", cns))
                    'Dim t4 = Task.Factory.StartNew(Sub() UpdateTables(lstPexCmd, "Commande", "CommandeID", "", cns))
                    'Task.WaitAll(t1, t2, t3, t4)
                    Task.WaitAll(t2)

                    '-todo- make this error proof and use in code.

                    Dim monthsBack = INI.ReadInteger("Config", "STDvsReelMonthsBack", 6) ' default: on roule spSTDvsReelxxx pour les 6 derniers mois
                    Dim hh = Now.Hour
                    Dim iniHours = $",{INI.ReadKey("Config", "FullSTDVsReelAtHours", "x")}," ' "6" devient ",6,"; "6,9,12" devient ",6,9,12,"
                    Dim dat = New Date(Now.AddMonths(-monthsBack).Year, Now.AddMonths(-monthsBack).Month, 1) '-todo param
                    Dim param = If(iniHours.Contains($",{hh},"), "", $"{dat:yyyy-MM-dd}") ' a 6h, on roule "full", sinon 6 derniers mois

                    Log($"spSTDvsREEL '{param}' started...", color:=ConsoleColor.White)
                    ppdb_report.OneTimeCommandTimeout = 600
                    ppdb_report.Execute($"execute spSTDvsREEL '{param}', 1, 0")
                    Log("spSTDvsREEL done.")

                    Log($"spSTDvsREELFacture '{param}' started...", color:=ConsoleColor.White)
                    ppdb_report.OneTimeCommandTimeout = 600
                    ppdb_report.Execute($"execute spSTDvsREELFacture '{param}', 1, 0")
                    Log("spSTDvsREELFacture done.")

                    Dim sql = "select pexrpt d1, pex d2, pexrpt - pex as d3 from (select (select sum(commandelbs) from commande) as pexrpt, (select sum(poids) from polyexpert.dbo.commande) as pex) t"
                    Dim check = ppdb_report.Fetch(Of Trio)(sql).Single
                    Log($"{vbCrLf}CHECK: pexRpt: {check.d1,18:#,0.00} / pex: {check.d2,18:#,0.00} / diff: {check.d3,18:#,0.00}")

                End Using

                ' Save to disk - at this point, DB data is ready
                SaveRecordsToDisk(lstPexRF, "RequisitionFacture", outPath)
                SaveRecordsToDisk(lstPexCM, "CommandeMachine", outPath)
                SaveRecordsToDisk(lstPexFac, "Facture", outPath)
                SaveRecordsToDisk(lstPexCmd, "Commande", outPath)

            End If

            pexReportACommandeMachine.CleanMemory()
            pexReportBCommande.CleanMemory()
            pexReportCFacture.CleanMemory()
            pexReportDRequisitionFacture.CleanMemory()
            pexReportECalculFinal.CleanMemory()

        End Using

        Log($"### Fin = {Now}")

    End Sub

    Private Function DataOKBeforeSave() As Boolean
        Dim bOK As Boolean
        bOK = True

        '* Vérifier qu'il y a du data dans le output
        EmailAssert(lstPexCmd.Count > 0, "", "PolyExpertReport - Nombre de commandes incorrect", "Le nombre de commandes générées par PolyExpertReport est incorrect.  Le service n'a pas roulé correctement.",, EmailToError)
        EmailAssert(lstPexCM.Count > 0, "", "PolyExpertReport - Nombre de commande machine incorrect", "Le nombre de commande machine générés par PolyExpertReport est incorrect.  Le service n'a pas roulé correctement.",, EmailToError)
        EmailAssert(lstPexFac.Count > 0, "", "PolyExpertReport - Nombre de factures incorrect", "Le nombre de factures générées par PolyExpertReport est incorrect.  Le service n'a pas roulé correctement.",, EmailToError)
        EmailAssert(lstPexRF.Count > 0, "", "PolyExpertReport - Nombre de réquisition facture incorrect", "Le nombre de réquisition facture générés par PolyExpertReport est incorrect.  Le service n'a pas roulé correctement.",, EmailToError)
        If lstPexCmd.Count = 0 Or lstPexCM.Count = 0 Or lstPexFac.Count = 0 Or lstPexRF.Count = 0 Then
            bOK = False
            Log("Erreur - Nombre d'items incorrect avant la sauvegarde.")
        End If

        '* Fetchingérifier si certains chiffres balancent encore avant le save.
        Dim SommeCmdPER = lstPexCmd.Sum(Function(x) x.CommandeMontant)
        Dim SommeCmdPE = lstCommande.Sum(Function(x) x.poids * (x.prix_lbs + x.surcharge))
        If Math.Abs(Math.Round(SommeCmdPER, 0) - Math.Round(SommeCmdPE, 0)) > 100 Then
            Log("***** Différence total commande (PEReport = " + SommeCmdPER.ToString() + ", PE =  : " + SommeCmdPE.ToString() + ")")
            '* On n'arrête pas le processus pour l'instant, on fait juste le signaler.
            'bOK = False
        End If

        Log("Fin CheckData")
        Return bOK
    End Function

    Private Sub UpdateDateNulle()

        Dim dat = lstPexCmd.Where(Function(v) CBool(v.CommandeDate < Date.Parse("1754-01-01")))
        For Each dt In dat
            dt.CommandeDate = Date.Parse("1900-01-01")
        Next

        Dim datCM = lstPexCM.Where(Function(v) CBool(v.DateDebutCommande < Date.Parse("1754-01-01")))
        For Each dt In datCM
            dt.DateDebutCommande = Nothing
        Next
        datCM = lstPexCM.Where(Function(v) CBool(v.DateDebutSetup < Date.Parse("1754-01-01")))
        For Each dt In datCM
            dt.DateDebutSetup = Nothing
        Next
        datCM = lstPexCM.Where(Function(v) CBool(v.DateFinCommande < Date.Parse("1754-01-01")))
        For Each dt In datCM
            dt.DateFinCommande = Nothing
        Next
        datCM = lstPexCM.Where(Function(v) CBool(v.DateFinSetup < Date.Parse("1754-01-01")))
        For Each dt In datCM
            dt.DateFinSetup = Nothing
        Next


    End Sub

    Private Sub UpdateCommande()

        For Each pc In lstPexCmd

            ' Can
            'If pc.Commande = "P1012-0057" OrElse pc.Commande = "P0611-0055" Then
            If True Then

                CalculSTD(pc.STDCMDPrixVenteCanNetMontantLbs, pc.STDCMDSpreadCanBrutMontantLbs, pc.STDCMDSpreadCanNetMontantLbs, pc.STDCMDCmCanBrutMontantLbs, pc.STDCMDCmCanNetMontantLbs, pc.STDCMDPrixVenteCanBrutMontantLbs, pc.STDCMDRabaisVolumeCanMontantLbs, pc.STDCMDCommissionVendeurCanMontantLbs, pc.STDCMDRedevanceClientCanMontantLbs, pc.STDCMDResineCanMontantLbs, pc.STDCMDScrapCanMontantLbs, pc.STDCMDVariableGlobaleCanMontantLbs, pc.STDCMDVariableClientCanMontantLbs)
                CalculSTD(pc.STDPRDPrixVenteCanNetMontantLbs, pc.STDPRDSpreadCanBrutMontantLbs, pc.STDPRDSpreadCanNetMontantLbs, pc.STDPRDCmCanBrutMontantLbs, pc.STDPRDCmCanNetMontantLbs, pc.STDPRDPrixVenteCanBrutMontantLbs, pc.STDPRDRabaisVolumeCanMontantLbs, pc.STDPRDCommissionVendeurCanMontantLbs, pc.STDPRDRedevanceClientCanMontantLbs, pc.STDPRDResineCanMontantLbs, pc.STDPRDScrapCanMontantLbs, pc.STDPRDVariableGlobaleCanMontantLbs, pc.STDPRDVariableClientCanMontantLbs)
                CalculSTD(pc.STDFACPrixVenteCanNetMontantLbs, pc.STDFACSpreadCanBrutMontantLbs, pc.STDFACSpreadCanNetMontantLbs, pc.STDFACCmCanBrutMontantLbs, pc.STDFACCmCanNetMontantLbs, pc.STDFACPrixVenteCanBrutMontantLbs, pc.STDFACRabaisVolumeCanMontantLbs, pc.STDFACCommissionVendeurCanMontantLbs, pc.STDFACRedevanceClientCanMontantLbs, pc.STDFACResineCanMontantLbs, pc.STDFACScrapCanMontantLbs, pc.STDFACVariableGlobaleCanMontantLbs, pc.STDFACVariableClientCanMontantLbs)

                '-reel-new- corrigé CAN
                CalculREEL(pc.REELPrixVenteInitialCanBrutMontantLbs, pc.REELEscompteCanMontantLbs, pc.REELCommissionVendeurCanMontantLbs, pc.REELRedevanceClientCanMontantLbs, pc.REELPrixVenteCanBrutMontantLbs, pc.REELPrixVenteBrutInclusTransportCanMontantLbs, pc.REELPrixVenteCanNetMontantLbs, pc.REELSpreadCanBrutMontantLbs, pc.REELCmCanBrutMontantLbs, pc.REELCmCanNetMontantLbs, pc.REELQuantiteAjustementCanMontantLbs, pc.REELSpreadCanNetMontantLbs, pc.REELProductionExpediable, pc.REELRabaisVolumeCanMontantLbs, pc.REELScrapCanMontantLbs, pc.REELVariableGlobaleCanMontantLbs, pc.REELVariableClientCanMontantLbs, pc.FactureLbs, pc.REELPrixVenteCanMontantLbs, pc.REELRevenuTransportCanMontantLbs, pc.STDFACDate, pc.REELResineCanMontantLbs, pc.CommandeEscompteTarif, pc.ClientVendeurID, pc.ClientID, AddressOf CalculDevise_Can, If(pc.CommandeDeviseID = 3, 1D, pc.STDFACTauxUS), Devise_can)
                '-reel-new- corrigé US
                CalculREEL(pc.REELPrixVenteInitialUsBrutMontantLbs, pc.REELEscompteUsMontantLbs, pc.REELCommissionVendeurUsMontantLbs, pc.REELRedevanceClientUsMontantLbs, pc.REELPrixVenteUsBrutMontantLbs, pc.REELPrixVenteBrutInclusTransportUSMontantLbs, pc.REELPrixVenteUsNetMontantLbs, pc.REELSpreadUsBrutMontantLbs, pc.REELCmUsBrutMontantLbs, pc.REELCmUsNetMontantLbs, pc.REELQuantiteAjustementUsMontantLbs, pc.REELSpreadUsNetMontantLbs, pc.REELProductionExpediable, pc.REELRabaisVolumeUsMontantLbs, pc.REELScrapUsMontantLbs, pc.REELVariableGlobaleUsMontantLbs, pc.REELVariableClientUsMontantLbs, pc.FactureLbs, pc.REELPrixVenteUSMontantLbs, pc.REELRevenuTransportUSMontantLbs, pc.STDFACDate, pc.REELResineUsMontantLbs, pc.CommandeEscompteTarif, pc.ClientVendeurID, pc.ClientID, AddressOf CalculDevise_US, If(pc.CommandeDeviseID = 4, 1D, If(pc.STDFACTauxUS <> 0, pc.STDFACTauxUS, GetStdTaux(pc.STDFACDate))), devise_us)

                ' US
                CalculSTD(pc.STDCMDPrixVenteUsNetMontantLbs, pc.STDCMDSpreadUsBrutMontantLbs, pc.STDCMDSpreadUsNetMontantLbs, pc.STDCMDCmUsBrutMontantLbs, pc.STDCMDCmUsNetMontantLbs, pc.STDCMDPrixVenteUsBrutMontantLbs, pc.STDCMDRabaisVolumeUsMontantLbs, pc.STDCMDCommissionVendeurUsMontantLbs, pc.STDCMDRedevanceClientUsMontantLbs, pc.STDCMDResineUsMontantLbs, pc.STDCMDScrapUsMontantLbs, pc.STDCMDVariableGlobaleUsMontantLbs, pc.STDCMDVariableClientUsMontantLbs)
                CalculSTD(pc.STDPRDPrixVenteUsNetMontantLbs, pc.STDPRDSpreadUsBrutMontantLbs, pc.STDPRDSpreadUsNetMontantLbs, pc.STDPRDCmUsBrutMontantLbs, pc.STDPRDCmUsNetMontantLbs, pc.STDPRDPrixVenteUsBrutMontantLbs, pc.STDPRDRabaisVolumeUsMontantLbs, pc.STDPRDCommissionVendeurUsMontantLbs, pc.STDPRDRedevanceClientUsMontantLbs, pc.STDPRDResineUsMontantLbs, pc.STDPRDScrapUsMontantLbs, pc.STDPRDVariableGlobaleUsMontantLbs, pc.STDPRDVariableClientUsMontantLbs)
                CalculSTD(pc.STDFACPrixVenteUsNetMontantLbs, pc.STDFACSpreadUsBrutMontantLbs, pc.STDFACSpreadUsNetMontantLbs, pc.STDFACCmUsBrutMontantLbs, pc.STDFACCmUsNetMontantLbs, pc.STDFACPrixVenteUsBrutMontantLbs, pc.STDFACRabaisVolumeUsMontantLbs, pc.STDFACCommissionVendeurUSMontantLbs, pc.STDFACRedevanceClientUsMontantLbs, pc.STDFACResineUsMontantLbs, pc.STDFACScrapUsMontantLbs, pc.STDFACVariableGlobaleUsMontantLbs, pc.STDFACVariableClientUsMontantLbs)
            End If
        Next

    End Sub

    Private Sub CalculREEL(ByRef PrixVente_InitialBrutMontantLbs As Decimal, ByRef Escompte_MontantLbs As Decimal,
                           ByRef CommissionVendeur_MontantLbs As Decimal, ByRef RedevanceClient_MontantLbs As Decimal,
                           ByRef PrixVente_BrutMontantLbs As Decimal, ByRef PrixVenteBrutInclusTransport_MontantLbs As Decimal,
                           ByRef PrixVente_NetMontantLbs As Decimal, ByRef Spread_BrutMontantLbs As Decimal, ByRef Cm_BrutMontantLbs As Decimal,
                           ByRef Cm_NetMontantLbs As Decimal, ByRef QuantiteAjustement_MontantLbs As Decimal, ByRef Spread_NetMontantLbs As Decimal,
                           ProductionExpediable As Decimal, RabaisVolume_MontantLbs As Decimal, Scrap_MontantLbs As Decimal,
                           VariableGlobale_MontantLbs As Decimal, VariableClient_MontantLbs As Decimal, factureLbs As Decimal,
                           PrixVente_MontantLbs As Decimal, RevenuTransport_MontantLbs As Decimal, FACDate As DateTime?, Resine_MontantLbs As Decimal,
                           CommandeEscompteTarif As Decimal, VendeurID As Integer, ClientID As Integer,
                           calculDevise As Func(Of Decimal, Decimal, Decimal), tauxDevise As Decimal, DeviseIDDemande As Integer)

        PrixVente_BrutMontantLbs = PrixVente_MontantLbs + RevenuTransport_MontantLbs
        Escompte_MontantLbs = calculDevise(CommandeEscompteTarif, tauxDevise)
        PrixVente_InitialBrutMontantLbs = PrixVente_BrutMontantLbs + Escompte_MontantLbs

        CommissionVendeur_MontantLbs = Calculer_Vendeur_Taux_Commission(VendeurID, FACDate, PrixVente_BrutMontantLbs, calculDevise, tauxDevise, DeviseIDDemande)
        RedevanceClient_MontantLbs = Calculer_Client_Redevance(ClientID, FACDate, PrixVente_BrutMontantLbs, calculDevise, tauxDevise, DeviseIDDemande)

        If ProductionExpediable > 0 AndAlso FACDate IsNot Nothing Then '-todo- -reel- last field is REELPrixVenteUsBrutMontantLbs which must be pre-calced
            QuantiteAjustement_MontantLbs = (factureLbs - ProductionExpediable) / ProductionExpediable * PrixVente_BrutMontantLbs
        End If

        '-reel-new- corrigé 
        PrixVenteBrutInclusTransport_MontantLbs = PrixVente_BrutMontantLbs
        PrixVente_NetMontantLbs = PrixVenteBrutInclusTransport_MontantLbs - RabaisVolume_MontantLbs - CommissionVendeur_MontantLbs - RedevanceClient_MontantLbs
        Spread_BrutMontantLbs = PrixVente_NetMontantLbs - Resine_MontantLbs + QuantiteAjustement_MontantLbs
        Spread_NetMontantLbs = Spread_BrutMontantLbs - Scrap_MontantLbs
        Cm_BrutMontantLbs = Spread_NetMontantLbs - VariableGlobale_MontantLbs
        Cm_NetMontantLbs = Cm_BrutMontantLbs - VariableClient_MontantLbs
    End Sub

    Private Sub CalculSTD(ByRef PrixVente_NetMontantLbs As Decimal, ByRef Spread_BrutMontantLbs As Decimal, ByRef Spread_NetMontantLbs As Decimal,
                          ByRef Cm_BrutMontantLbs As Decimal, ByRef Cm_NetMontantLbs As Decimal, PrixVente_BrutMontantLbs As Decimal,
                          RabaisVolume_MontantLbs As Decimal, CommissionVendeur As Decimal, RedevanceClient As Decimal, Resine_MontantLbs As Decimal,
                          Scrap_MontantLbs As Decimal, VariableGlobale_MontantLbs As Decimal, VariableClient_MontantLbs As Decimal)

        PrixVente_NetMontantLbs = PrixVente_BrutMontantLbs - RabaisVolume_MontantLbs - CommissionVendeur - RedevanceClient

        Spread_BrutMontantLbs = PrixVente_NetMontantLbs - Resine_MontantLbs
        Spread_NetMontantLbs = Spread_BrutMontantLbs - Scrap_MontantLbs
        Cm_BrutMontantLbs = Spread_NetMontantLbs - VariableGlobale_MontantLbs
        Cm_NetMontantLbs = Cm_BrutMontantLbs - VariableClient_MontantLbs
    End Sub

    Private Sub UpdateFacture()

        For Each pf In lstPexFac

            '-reel-new- corrigé CAN+US nouveau calcul brut

            pf.REELPrixVenteCanBrutMontantLbs = pf.REELPrixVenteCanMontantLbs + pf.REELRevenuTransportCanMontantLbs

            Dim TauxPourCalculDevise_can As Decimal = If(pf.FactureDeviseID = 3, 1D, pf.FactureTaux)
            pf.REELEscompteCanMontantLbs = CalculDevise_Can(GetEscompteTarifFromCommande(pf.CommandeID), TauxPourCalculDevise_can)
            pf.REELPrixVenteInitialCanBrutMontantLbs = pf.REELPrixVenteCanBrutMontantLbs + pf.REELEscompteCanMontantLbs
            pf.REELRedevanceClientCanMontantLbs = Calculer_Client_Redevance(pf.ClientID, pf.FactureDate, pf.REELPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)
            pf.REELCommissionVendeurCanMontantLbs = Calculer_Vendeur_Taux_Commission(GetVendeurFromCommande(pf.CommandeID), pf.FactureDate, pf.REELPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)

            Dim TauxPourCalculDevise_us As Decimal = If(pf.FactureDeviseID = 4, 1D, If(pf.FactureTaux <> 0, pf.FactureTaux, GetStdTaux(pf.FactureDate)))
            pf.REELPrixVenteUsBrutMontantLbs = pf.REELPrixVenteUSMontantLbs + pf.REELRevenuTransportUSMontantLbs
            pf.REELEscompteUsMontantLbs = CalculDevise_US(GetEscompteTarifFromCommande(pf.CommandeID), TauxPourCalculDevise_us)

            pf.REELPrixVenteInitialUsBrutMontantLbs = pf.REELPrixVenteUsBrutMontantLbs + pf.REELEscompteUsMontantLbs
            pf.REELRedevanceClientUSMontantLbs = Calculer_Client_Redevance(pf.ClientID, pf.FactureDate, pf.REELPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_us, devise_us)
            pf.REELCommissionVendeurUSMontantLbs = Calculer_Vendeur_Taux_Commission(GetVendeurFromCommande(pf.CommandeID), pf.FactureDate, pf.REELPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_us, devise_us)

            If pf.REELProductionExpediable > 0 AndAlso pf.FactureLbs > 0 Then
                pf.REELQuantiteAjustementUsMontantLbs = (pf.FactureLbs - pf.REELProductionExpediable) / pf.REELProductionExpediable * pf.REELPrixVenteUsBrutMontantLbs
                pf.REELQuantiteAjustementCanMontantLbs = (pf.FactureLbs - pf.REELProductionExpediable) / pf.REELProductionExpediable * pf.REELPrixVenteCanBrutMontantLbs
            End If

            '-reel-new- corrigé CAN
            pf.REELPrixVenteBrutInclusTransportCanMontantLbs = pf.REELPrixVenteCanBrutMontantLbs '+ pf.REELRevenuTransportCanMontantLbs
            pf.REELPrixVenteCanNetMontantLbs = pf.REELPrixVenteBrutInclusTransportCanMontantLbs - pf.REELRabaisVolumeCanMontantLbs - pf.REELCommissionVendeurCanMontantLbs - pf.REELRedevanceClientCanMontantLbs
            pf.REELSpreadCanBrutMontantLbs = pf.REELPrixVenteCanNetMontantLbs - pf.REELResineCanMontantLbs + pf.REELQuantiteAjustementCanMontantLbs
            pf.REELSpreadCanNetMontantLbs = pf.REELSpreadCanBrutMontantLbs - pf.REELScrapCanMontantLbs
            pf.REELCmCanBrutMontantLbs = pf.REELSpreadCanNetMontantLbs - pf.REELVariableGlobaleCanMontantLbs
            pf.REELCmCanNetMontantLbs = pf.REELCmCanBrutMontantLbs - pf.REELVariableClientCanMontantLbs

            '-reel-new- corrigé US
            pf.REELPrixVenteBrutInclusTransportUSMontantLbs = pf.REELPrixVenteUsBrutMontantLbs '+ pf.REELRevenuTransportUSMontantLbs

            pf.REELPrixVenteUsNetMontantLbs = pf.REELPrixVenteBrutInclusTransportUSMontantLbs - pf.REELRabaisVolumeUsMontantLbs - pf.REELCommissionVendeurUSMontantLbs - pf.REELRedevanceClientUSMontantLbs
            pf.REELSpreadUsBrutMontantLbs = pf.REELPrixVenteUsNetMontantLbs - pf.REELResineUsMontantLbs + pf.REELQuantiteAjustementUsMontantLbs
            pf.REELSpreadUsNetMontantLbs = pf.REELSpreadUsBrutMontantLbs - pf.REELScrapUsMontantLbs
            pf.REELCmUsBrutMontantLbs = pf.REELSpreadUsNetMontantLbs - pf.REELVariableGlobaleUsMontantLbs
            pf.REELCmUsNetMontantLbs = pf.REELCmUsBrutMontantLbs - pf.REELVariableClientUsMontantLbs

        Next

    End Sub

    Private Class Trio
        Property d1 As Decimal
        Property d2 As Decimal
        Property d3 As Decimal
    End Class

End Module

