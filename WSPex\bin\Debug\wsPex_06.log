﻿
2025-08-06 09:57:43 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 09:57:43 WSPex started successfully at : 2025-08-06 9:57:43 AM

2025-08-06 10:56:55 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 10:56:55 WSPex started successfully at : 2025-08-06 10:56:55 AM

2025-08-06 10:57:02 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 10:57:02 WSPex started successfully at : 2025-08-06 10:57:02 AM
2025-08-06 11:41:18 ###
2025-08-06 11:41:18 ### Début = 2025-08-06 11:41:18 AM
2025-08-06 11:41:18 Fetching commande from DB...
2025-08-06 11:42:13 commande error : Format of the initialization string does not conform to specification starting at index 0.
2025-08-06 11:42:14 ### Une erreur est survenue: System.NullReferenceException: Object reference not set to an instance of an object.
   at WSPex.Common.GetData[T](Database ppdb, String sql, String cachePath, Boolean getFromDB, Boolean compress) in C:\Dev\WSPex\WSPex\Common.vb:line 91
   at WSPex.pexReport_Data.InitCaches() in C:\Dev\WSPex\WSPex\pexData\pexReport_Data.vb:line 134
   at WSPex.WSPex.StartPolyExpertReport(String ResponseEmail) in C:\Dev\WSPex\WSPex\_WCF\WSPex.vb:line 66
2025-08-06 11:42:33 ###
2025-08-06 11:42:35 ### Début = 2025-08-06 11:42:35 AM
2025-08-06 11:42:42 Fetching commande from DB...
2025-08-06 11:43:02 commande error : Format of the initialization string does not conform to specification starting at index 0.
2025-08-06 11:43:03 ### Une erreur est survenue: System.NullReferenceException: Object reference not set to an instance of an object.

   at WSPex.Common.GetData[T](Database ppdb, String sql, String cachePath, Boolean getFromDB, Boolean compress) in C:\Dev\WSPex\WSPex\Common.vb:line 91

2025-08-06 11:43:08 ###
2025-08-06 11:43:08 ### Début = 2025-08-06 11:43:08 AM
2025-08-06 11:43:22 Fetching commande from DB...
2025-08-06 11:44:11 commande error : Format of the initialization string does not conform to specification starting at index 0.
2025-08-06 11:44:12 ### Une erreur est survenue: System.NullReferenceException: Object reference not set to an instance of an object.

   at WSPex.Common.GetData[T](Database ppdb, String sql, String cachePath, Boolean getFromDB, Boolean compress) in C:\Dev\WSPex\WSPex\Common.vb:line 91

2025-08-06 11:44:18 ###
2025-08-06 11:44:18 ### Début = 2025-08-06 11:44:18 AM
2025-08-06 11:44:23 Fetching commande from DB...
2025-08-06 11:47:59 commande error : Format of the initialization string does not conform to specification starting at index 0.
2025-08-06 11:48:00 ### Une erreur est survenue: System.NullReferenceException: Object reference not set to an instance of an object.

   at WSPex.Common.GetData[T](Database ppdb, String sql, String cachePath, Boolean getFromDB, Boolean compress) in C:\Dev\WSPex\WSPex\Common.vb:line 91


2025-08-06 11:50:36 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 11:50:36 WSPex started successfully at : 2025-08-06 11:50:36 AM

2025-08-06 12:01:09 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 12:01:09 WSPex started successfully at : 2025-08-06 12:01:09 PM
2025-08-06 12:01:26 ###
2025-08-06 12:01:26 ### Début = 2025-08-06 12:01:26 PM
2025-08-06 12:01:30 Fetching commande from DB...
2025-08-06 12:05:47 commande error : Format of the initialization string does not conform to specification starting at index 0.
2025-08-06 12:05:48 ### Une erreur est survenue: System.NullReferenceException: Object reference not set to an instance of an object.
   at WSPex.Common.GetData[T](Database ppdb, String sql, String cachePath, Boolean getFromDB, Boolean compress) in C:\Dev\WSPex\WSPex\Common.vb:line 91
   at WSPex.pexReport_Data.InitCaches() in C:\Dev\WSPex\WSPex\pexData\pexReport_Data.vb:line 134
   at WSPex.WSPex.StartPolyExpertReport(String ResponseEmail) in C:\Dev\WSPex\WSPex\_WCF\WSPex.vb:line 66

2025-08-06 12:09:28 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 12:09:28 WSPex started successfully at : 2025-08-06 12:09:28 PM
2025-08-06 12:10:41 ###
2025-08-06 12:10:41 ### Début = 2025-08-06 12:10:41 PM
2025-08-06 12:10:54 Fetching commande from DB...
2025-08-06 12:11:44 commande error : Format of the initialization string does not conform to specification starting at index 0.
2025-08-06 12:11:45 ### Une erreur est survenue: System.NullReferenceException: Object reference not set to an instance of an object.
   at WSPex.Common.GetData[T](Database ppdb, String sql, String cachePath, Boolean getFromDB, Boolean compress) in C:\Dev\WSPex\WSPex\Common.vb:line 91
   at WSPex.pexReport_Data.InitCaches() in C:\Dev\WSPex\WSPex\pexData\pexReport_Data.vb:line 134
   at WSPex.WSPex.StartPolyExpertReport(String ResponseEmail) in C:\Dev\WSPex\WSPex\_WCF\WSPex.vb:line 66

2025-08-06 12:12:02 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 12:12:02 WSPex started successfully at : 2025-08-06 12:12:02 PM



2025-08-06 12:18:45 ### WCF Server started at http://TI12:8184/WSPex
2025-08-06 12:18:45 WSPex started successfully at : 2025-08-06 12:18:45 PM
2025-08-06 12:18:57 ###
2025-08-06 12:18:57 ### Début = 2025-08-06 12:18:57 PM
2025-08-06 12:19:03 Fetching commande from DB...
2025-08-06 12:19:28  - commande: 231,339 records.
2025-08-06 12:51:16  - dicCmd=231,339
2025-08-06 13:03:00 Fetching PV from DB...
2025-08-06 13:03:28  - PV: 16,145 records.
