﻿<PetaPoco.PrimaryKey("id_commande_recette_resine")>
Partial Public Class commande_recette_resine
    Public Property id_commande_recette_resine As Integer
    Public Property id_commande_recette As Integer
    Public Property id_resine As Integer
    Public Property ratio As Decimal?
    Public Property element As String
    Public Property id_resine_relier As Integer?
    '	Public Property densite As Decimal?
    Public Property note As Decimal?
    Public Property resine_nom As String
    Public Property cout As Decimal
    '   Public Property prix_lbs_us_normalise As Decimal
    '	Public Property cout_us As Decimal?
    '	Public Property taux_us As Decimal?
    '	Public Property hasprodmodifiedratio As Boolean?
End Class