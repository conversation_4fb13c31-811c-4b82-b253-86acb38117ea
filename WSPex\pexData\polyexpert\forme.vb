﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_FORME")>
<ProtoContract>
Partial Public Class forme
    <ProtoMember(1)> Public Property id_forme As Integer
    <ProtoMember(2)> Public Property nom As String
    'Public Property nb_mesure As Integer?
    'Public Property operation As String
    'Public Property multiple As Integer?
    'Public Property calcul_largeur As String
    'Public Property index_icone As Integer?
    'Public Property commentaire As String
    'Public Property up_multiple As Boolean
    'Public Property nb_rouleau_up As Integer?
    'Public Property nb_up_max As Integer?
    'Public Property y_a_deux_largeur As Boolean
    'Public Property abreviation As String
    'Public Property ordre As Integer?
    'Public Property label_largeur_1 As String
    'Public Property label_largeur_2 As String
    'Public Property label_largeur_3 As String
    'Public Property id_forme_calcul_largeur As Integer?
    'Public Property sheeting As Boolean
End Class