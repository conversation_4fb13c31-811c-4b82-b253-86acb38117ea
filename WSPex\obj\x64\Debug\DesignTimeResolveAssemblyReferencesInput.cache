   .winmd.dll.exe    C:\Dev\.editorconfig)C:\Dev\WSPex\My Project\Application.myapp)C:\Dev\WSPex\My Project\Settings.settingsC:\Dev\WSPex\App.configC:\Dev\WSPex\packages.config:C:\Dev\WSPex\packages\AsyncIO.0.1.69\lib\net40\AsyncIO.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Microsoft.CSharp.dll7C:\Dev\WSPex\packages\NetMQ.4.0.0.1\lib\net40\NetMQ.dllAC:\Dev\_Shared\PetaPoco.Net40\PetaPoco\bin\x64\Debug\PetaPoco.dll.C:\Dev\_Shared\ProtoBuf\net40\protobuf-net.dlluC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Configuration.Install.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dlljC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Deployment.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Net.Http.dlloC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Runtime.Caching.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ServiceModel.dllpC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ServiceModel.Web.dllnC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ServiceProcess.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.Linq.dll<C:\Dev\WSPex\packages\Topshelf.4.2.0\lib\net452\Topshelf.dll4C:\Dev\_Shared\Utilities\bin\x64\Debug\Utilities.dll<C:\Dev\_Shared\UtilitiesData\bin\x64\Debug\UtilitiesData.dll:C:\Dev\_Shared\UtilitiesWeb\bin\x64\Debug\UtilitiesWeb.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}
{RawFileName}!C:\Dev\WSPex\WSPex\bin\x64\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}JC:\Dev\WSPex\WSPex\obj\x64\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\.NETFramework,Version=v4.7.2.NET Framework 4.7.2v4.7.2amd64
v4.0.30319         