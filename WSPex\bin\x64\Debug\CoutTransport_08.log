﻿2023-02-08 10:46:07 CalculCoutTransport - START ---------------------------------------------------------
2023-02-08 10:46:07 Reading from DB: Data Source=SQL14;     Initial Catalog=PolyExpert
2023-02-08 10:46:10 Requisitions lues (drops)
2023-02-08 10:46:10 Requisitions cout réparti
2023-02-08 10:46:28 Calcul année 2005
2023-02-08 10:47:26 Calcul année 2006
2023-02-08 10:48:22 Calcul année 2007
2023-02-08 10:49:15 Calcul année 2008
2023-02-08 10:50:09 Calcul année 2009
2023-02-08 10:50:59 Calcul année 2010
2023-02-08 10:51:51 Cal<PERSON><PERSON> année 2011
2023-02-08 10:52:44 Calcul année 2012
2023-02-08 10:53:36 Calcul année 2013
2023-02-08 10:54:30 <PERSON><PERSON><PERSON> année 2014
2023-02-08 10:55:27 Calcul année 2015
2023-02-08 10:56:29 Calcul année 2016
2023-02-08 10:57:31 Calcul année 2017
2023-02-08 10:58:32 <PERSON>cul année 2018
2023-02-08 10:59:34 <PERSON>cul année 2019
2023-02-08 11:00:36 <PERSON>cul année 2020
2023-02-08 11:01:37 <PERSON>cul année 2021
2023-02-08 11:02:38 <PERSON>cul année 2022
2023-02-08 11:03:37 Calcul année 2023
2023-02-08 11:03:47 Writing to DB: Data Source=SQL14;     Initial Catalog=PolyExpert
2023-02-08 11:04:04 Previous entry occurred 1 more time.
2023-02-08 11:04:04 Bulk insert table dbo.staging_std_cout_transport_v3_secteur - 45500 records
2023-02-08 11:04:05 Bulk insert table dbo.staging_std_cout_transport_v3_adresse - 3130230 records
2023-02-08 11:05:59 Bulk insert table dbo.staging_std_cout_transport_v3_client - 977490 records
2023-02-08 11:06:37 Bulk insert table dbo.staging_std_cout_transport_v3_palette - 779256 records
2023-02-08 11:06:50 Swapping staging tables
2023-02-08 11:06:54 CalculCoutTransport - END -----------------------------------------------------------
