﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_FACTURE")>
<ProtoContract>
Partial Public Class facture
    <ProtoMember(1)> Public Property id_facture As Integer
    <ProtoMember(2)> Public Property id_client As Integer
    <ProtoMember(3)> Public Property id_adresse As Integer
    <ProtoMember(4)> Public Property date_commande As DateTime
    <ProtoMember(5)> Public Property date_expedition As DateTime
    <ProtoMember(6)> Public Property date_facturation As DateTime
    <ProtoMember(7)> Public Property no_facture As String
    <ProtoMember(8)> Public Property id_commande As Integer
    <ProtoMember(9)> Public Property po_client As String
    'Public Property terme As Integer?
    'Public Property tps as double?
    'Public Property tvq as double?
    'Public Property tvh as double?
    'Public Property tvhautre as double?
    'Public Property tvhbc as double?
    'Public Property tvhns as double?
    'Public Property taxe As Integer?
    'Public Property imprime As Integer?
    <ProtoMember(19)> Public Property id_devise As Integer
    'Public Property posted As Boolean
    Public Property annule As Boolean
    'Public Property id_transport As Integer?
    'Public Property no_batch As String
    'Public Property date_batch As DateTime?
    'Public Property cedule As Boolean
    <ProtoMember(26)> Public Property taux_echange As Decimal
    'Public Property montant_commission as double?
    'Public Property remarque As String
    <ProtoMember(29)> Public Property type As String
    'Public Property usager_courant As String
    <ProtoMember(31)> Public Property id_facture_retour As Integer
    'Public Property a_verifier As Boolean
    <ProtoMember(33)> Public Property id_plainte_entente As Integer
    'Public Property rowversion As Byte()
    <PetaPoco.Ignore> Public Property calc_FactureLbs As Decimal
End Class