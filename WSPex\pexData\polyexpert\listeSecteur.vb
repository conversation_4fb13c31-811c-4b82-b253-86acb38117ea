﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_Secteur")>
<ProtoContract>
Partial Public Class listesecteur
    <ProtoMember(1)> Public Property id_secteur As Integer
    <ProtoMember(2)> Public Property nom As String
    <ProtoMember(3)> Public Property actif As Boolean
    'Public Property requisition_obligatoire As Boolean
    'Public Property douane As Boolean
    'Public Property bolcopie1 As Integer?
    'Public Property bolcopie2 As Integer?
    'Public Property bolcopie3 As Integer?
    'Public Property psnb As Integer?
    'Public Property psnbcopie As Integer?
    'Public Property proformanbcopie As Integer?
    'Public Property couttransport As Decimal?
    'Public Property douaneassurance As Decimal?
    'Public Property id_secteur_route As Integer?
    <ProtoMember(15)> Public Property pays_iso As String
    'Public Property old_name As String
End Class