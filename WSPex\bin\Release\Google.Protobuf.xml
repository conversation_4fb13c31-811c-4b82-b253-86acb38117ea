<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Protobuf</name>
    </assembly>
    <members>
        <member name="T:Google.Protobuf.ByteArray">
            <summary>
            Provides a utility routine to copy small arrays much more quickly than <PERSON>uffer.BlockCopy
            </summary>
        </member>
        <member name="F:Google.Protobuf.ByteArray.CopyThreshold">
            <summary>
            The threshold above which you should use Buffer.BlockCopy rather than ByteArray.Copy
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteArray.Copy(System.Byte[],System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Determines which copy routine to use based on the number of bytes to be copied.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteArray.Reverse(System.Byte[])">
            <summary>
            Reverses the order of bytes in the array
            </summary>
        </member>
        <member name="T:Google.Protobuf.ByteString">
            <summary>
            Immutable array of bytes.
            </summary>
        </member>
        <member name="T:Google.Protobuf.ByteString.Unsafe">
            <summary>
            Unsafe operations that can cause IO Failure and/or other catestrophic side-effects.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.Unsafe.FromBytes(System.Byte[])">
            <summary>
            Constructs a new ByteString from the given byte array. The array is
            *not* copied, and must not be modified after this constructor is called.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.Unsafe.GetBuffer(Google.Protobuf.ByteString)">
            <summary>
            Provides direct, unrestricted access to the bytes contained in this instance.
            You must not modify or resize the byte array returned by this method.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.AttachBytes(System.Byte[])">
            <summary>
            Internal use only.  Ensure that the provided array is not mutated and belongs to this instance.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.#ctor(System.Byte[])">
            <summary>
            Constructs a new ByteString from the given byte array. The array is
            *not* copied, and must not be modified after this constructor is called.
            </summary>
        </member>
        <member name="P:Google.Protobuf.ByteString.Empty">
            <summary>
            Returns an empty ByteString.
            </summary>
        </member>
        <member name="P:Google.Protobuf.ByteString.Length">
            <summary>
            Returns the length of this ByteString in bytes.
            </summary>
        </member>
        <member name="P:Google.Protobuf.ByteString.IsEmpty">
            <summary>
            Returns <c>true</c> if this byte string is empty, <c>false</c> otherwise.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.ToByteArray">
            <summary>
            Converts this <see cref="T:Google.Protobuf.ByteString"/> into a byte array.
            </summary>
            <remarks>The data is copied - changes to the returned array will not be reflected in this <c>ByteString</c>.</remarks>
            <returns>A byte array with the same data as this <c>ByteString</c>.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.ToBase64">
            <summary>
            Converts this <see cref="T:Google.Protobuf.ByteString"/> into a standard base64 representation.
            </summary>
            <returns>A base64 representation of this <c>ByteString</c>.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.FromBase64(System.String)">
            <summary>
            Constructs a <see cref="T:Google.Protobuf.ByteString" /> from the Base64 Encoded String.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.FromStream(System.IO.Stream)">
            <summary>
            Constructs a <see cref="T:Google.Protobuf.ByteString"/> from data in the given stream, synchronously.
            </summary>
            <remarks>If successful, <paramref name="stream"/> will be read completely, from the position
            at the start of the call.</remarks>
            <param name="stream">The stream to copy into a ByteString.</param>
            <returns>A ByteString with content read from the given stream.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.FromStreamAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Constructs a <see cref="T:Google.Protobuf.ByteString"/> from data in the given stream, asynchronously.
            </summary>
            <remarks>If successful, <paramref name="stream"/> will be read completely, from the position
            at the start of the call.</remarks>
            <param name="stream">The stream to copy into a ByteString.</param>
            <param name="cancellationToken">The cancellation token to use when reading from the stream, if any.</param>
            <returns>A ByteString with content read from the given stream.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.CopyFrom(System.Byte[])">
            <summary>
            Constructs a <see cref="T:Google.Protobuf.ByteString" /> from the given array. The contents
            are copied, so further modifications to the array will not
            be reflected in the returned ByteString.
            This method can also be invoked in <c>ByteString.CopyFrom(0xaa, 0xbb, ...)</c> form
            which is primarily useful for testing.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.CopyFrom(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Constructs a <see cref="T:Google.Protobuf.ByteString" /> from a portion of a byte array.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.CopyFrom(System.String,System.Text.Encoding)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.ByteString" /> by encoding the specified text with
            the given encoding.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.CopyFromUtf8(System.String)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.ByteString" /> by encoding the specified text in UTF-8.
            </summary>
        </member>
        <member name="P:Google.Protobuf.ByteString.Item(System.Int32)">
            <summary>
            Retuns the byte at the given index.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.ToString(System.Text.Encoding)">
            <summary>
            Converts this <see cref="T:Google.Protobuf.ByteString"/> into a string by applying the given encoding.
            </summary>
            <remarks>
            This method should only be used to convert binary data which was the result of encoding
            text with the given encoding.
            </remarks>
            <param name="encoding">The encoding to use to decode the binary data into text.</param>
            <returns>The result of decoding the binary data with the given decoding.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.ToStringUtf8">
            <summary>
            Converts this <see cref="T:Google.Protobuf.ByteString"/> into a string by applying the UTF-8 encoding.
            </summary>
            <remarks>
            This method should only be used to convert binary data which was the result of encoding
            text with UTF-8.
            </remarks>
            <returns>The result of decoding the binary data with the given decoding.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.GetEnumerator">
            <summary>
            Returns an iterator over the bytes in this <see cref="T:Google.Protobuf.ByteString"/>.
            </summary>
            <returns>An iterator over the bytes in this object.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an iterator over the bytes in this <see cref="T:Google.Protobuf.ByteString"/>.
            </summary>
            <returns>An iterator over the bytes in this object.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.CreateCodedInput">
            <summary>
            Creates a CodedInputStream from this ByteString's data.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.op_Equality(Google.Protobuf.ByteString,Google.Protobuf.ByteString)">
            <summary>
            Compares two byte strings for equality.
            </summary>
            <param name="lhs">The first byte string to compare.</param>
            <param name="rhs">The second byte string to compare.</param>
            <returns><c>true</c> if the byte strings are equal; false otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.op_Inequality(Google.Protobuf.ByteString,Google.Protobuf.ByteString)">
            <summary>
            Compares two byte strings for inequality.
            </summary>
            <param name="lhs">The first byte string to compare.</param>
            <param name="rhs">The second byte string to compare.</param>
            <returns><c>false</c> if the byte strings are equal; true otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.Equals(System.Object)">
            <summary>
            Compares this byte string with another object.
            </summary>
            <param name="obj">The object to compare this with.</param>
            <returns><c>true</c> if <paramref name="obj"/> refers to an equal <see cref="T:Google.Protobuf.ByteString"/>; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.GetHashCode">
            <summary>
            Returns a hash code for this object. Two equal byte strings
            will return the same hash code.
            </summary>
            <returns>A hash code for this object.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.Equals(Google.Protobuf.ByteString)">
            <summary>
            Compares this byte string with another.
            </summary>
            <param name="other">The <see cref="T:Google.Protobuf.ByteString"/> to compare this with.</param>
            <returns><c>true</c> if <paramref name="other"/> refers to an equal byte string; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.ByteString.WriteRawBytesTo(Google.Protobuf.CodedOutputStream)">
            <summary>
            Used internally by CodedOutputStream to avoid creating a copy for the write
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.CopyTo(System.Byte[],System.Int32)">
            <summary>
            Copies the entire byte array to the destination array provided at the offset specified.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ByteString.WriteTo(System.IO.Stream)">
            <summary>
            Writes the entire byte array to the provided stream
            </summary>
        </member>
        <member name="T:Google.Protobuf.CodedInputStream">
            <summary>
            Reads and decodes protocol message fields.
            </summary>
            <remarks>
            <para>
            This class is generally used by generated code to read appropriate
            primitives from the stream. It effectively encapsulates the lowest
            levels of protocol buffer format.
            </para>
            <para>
            Repeated fields and map fields are not handled by this class; use <see cref="T:Google.Protobuf.Collections.RepeatedField`1"/>
            and <see cref="T:Google.Protobuf.Collections.MapField`2"/> to serialize such fields.
            </para>
            </remarks>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.leaveOpen">
            <summary>
            Whether to leave the underlying stream open when disposing of this stream.
            This is always true when there's no stream.
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.buffer">
            <summary>
            Buffer of data read from the stream or provided at construction time.
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.bufferSize">
            <summary>
            The index of the buffer at which we need to refill from the stream (if there is one).
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.bufferPos">
            <summary>
            The position within the current buffer (i.e. the next byte to read)
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.input">
            <summary>
            The stream to read further input from, or null if the byte array buffer was provided
            directly on construction, with no further data available.
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.lastTag">
            <summary>
            The last tag we read. 0 indicates we've read to the end of the stream
            (or haven't read anything yet).
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.nextTag">
            <summary>
            The next tag, used to store the value read by PeekTag.
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.totalBytesRetired">
            <summary>
            The total number of bytes read before the current buffer. The
            total bytes read up to the current position can be computed as
            totalBytesRetired + bufferPos.
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedInputStream.currentLimit">
            <summary>
            The absolute position of the end of the current message.
            </summary> 
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.#ctor(System.Byte[])">
            <summary>
            Creates a new CodedInputStream reading data from the given byte array.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.#ctor(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.CodedInputStream"/> that reads from the given byte array slice.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.#ctor(System.IO.Stream)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.CodedInputStream"/> reading data from the given stream, which will be disposed
            when the returned object is disposed.
            </summary>
            <param name="input">The stream to read from.</param>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.CodedInputStream"/> reading data from the given stream.
            </summary>
            <param name="input">The stream to read from.</param>
            <param name="leaveOpen"><c>true</c> to leave <paramref name="input"/> open when the returned
            <c cref="T:Google.Protobuf.CodedInputStream"/> is disposed; <c>false</c> to dispose of the given stream when the
            returned object is disposed.</param>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.#ctor(System.IO.Stream,System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Creates a new CodedInputStream reading data from the given
            stream and buffer, using the default limits.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.#ctor(System.IO.Stream,System.Byte[],System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Creates a new CodedInputStream reading data from the given
            stream and buffer, using the specified limits.
            </summary>
            <remarks>
            This chains to the version with the default limits instead of vice versa to avoid
            having to check that the default values are valid every time.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.CreateWithLimits(System.IO.Stream,System.Int32,System.Int32)">
            <summary>
            Creates a <see cref="T:Google.Protobuf.CodedInputStream"/> with the specified size and recursion limits, reading
            from an input stream.
            </summary>
            <remarks>
            This method exists separately from the constructor to reduce the number of constructor overloads.
            It is likely to be used considerably less frequently than the constructors, as the default limits
            are suitable for most use cases.
            </remarks>
            <param name="input">The input stream to read from</param>
            <param name="sizeLimit">The total limit of data to read from the stream.</param>
            <param name="recursionLimit">The maximum recursion depth to allow while reading.</param>
            <returns>A <c>CodedInputStream</c> reading from <paramref name="input"/> with the specified size
            and recursion limits.</returns>
        </member>
        <member name="P:Google.Protobuf.CodedInputStream.Position">
            <summary>
            Returns the current position in the input stream, or the position in the input buffer
            </summary>
        </member>
        <member name="P:Google.Protobuf.CodedInputStream.LastTag">
            <summary>
            Returns the last tag read, or 0 if no tags have been read or we've read beyond
            the end of the stream.
            </summary>
        </member>
        <member name="P:Google.Protobuf.CodedInputStream.SizeLimit">
            <summary>
            Returns the size limit for this stream.
            </summary>
            <remarks>
            This limit is applied when reading from the underlying stream, as a sanity check. It is
            not applied when reading from a byte array data source without an underlying stream.
            The default value is Int32.MaxValue.
            </remarks>
            <value>
            The size limit.
            </value>
        </member>
        <member name="P:Google.Protobuf.CodedInputStream.RecursionLimit">
            <summary>
            Returns the recursion limit for this stream. This limit is applied whilst reading messages,
            to avoid maliciously-recursive data.
            </summary>
            <remarks>
            The default limit is 100.
            </remarks>
            <value>
            The recursion limit for this stream.
            </value>
        </member>
        <member name="P:Google.Protobuf.CodedInputStream.DiscardUnknownFields">
            <summary>
            Internal-only property; when set to true, unknown fields will be discarded while parsing.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.Dispose">
            <summary>
            Disposes of this instance, potentially closing any underlying stream.
            </summary>
            <remarks>
            As there is no flushing to perform here, disposing of a <see cref="T:Google.Protobuf.CodedInputStream"/> which
            was constructed with the <c>leaveOpen</c> option parameter set to <c>true</c> (or one which
            was constructed to read from a byte array) has no effect.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.CheckReadEndOfStreamTag">
            <summary>
            Verifies that the last call to ReadTag() returned tag 0 - in other words,
            we've reached the end of the stream when we expected to.
            </summary>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The 
            tag read was not the one specified</exception>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.PeekTag">
            <summary>
            Peeks at the next field tag. This is like calling <see cref="M:Google.Protobuf.CodedInputStream.ReadTag"/>, but the
            tag is not consumed. (So a subsequent call to <see cref="M:Google.Protobuf.CodedInputStream.ReadTag"/> will return the
            same value.)
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadTag">
            <summary>
            Reads a field tag, returning the tag of 0 for "end of stream".
            </summary>
            <remarks>
            If this method returns 0, it doesn't necessarily mean the end of all
            the data in this CodedInputStream; it may be the end of the logical stream
            for an embedded message, for example.
            </remarks>
            <returns>The next field tag, or 0 for end of stream. (0 is never a valid tag.)</returns>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.SkipLastField">
            <summary>
            Skips the data for the field with the tag we've just read.
            This should be called directly after <see cref="M:Google.Protobuf.CodedInputStream.ReadTag"/>, when
            the caller wishes to skip an unknown field.
            </summary>
            <remarks>
            This method throws <see cref="T:Google.Protobuf.InvalidProtocolBufferException"/> if the last-read tag was an end-group tag.
            If a caller wishes to skip a group, they should skip the whole group, by calling this method after reading the
            start-group tag. This behavior allows callers to call this method on any field they don't understand, correctly
            resulting in an error if an end-group tag has not been paired with an earlier start-group tag.
            </remarks>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The last tag was an end-group tag</exception>
            <exception cref="T:System.InvalidOperationException">The last read operation read to the end of the logical stream</exception>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.SkipGroup(System.UInt32)">
            <summary>
            Skip a group.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadDouble">
            <summary>
            Reads a double field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadFloat">
            <summary>
            Reads a float field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadUInt64">
            <summary>
            Reads a uint64 field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadInt64">
            <summary>
            Reads an int64 field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadInt32">
            <summary>
            Reads an int32 field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadFixed64">
            <summary>
            Reads a fixed64 field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadFixed32">
            <summary>
            Reads a fixed32 field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadBool">
            <summary>
            Reads a bool field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadString">
            <summary>
            Reads a string field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadMessage(Google.Protobuf.IMessage)">
            <summary>
            Reads an embedded message field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadGroup(Google.Protobuf.IMessage)">
            <summary>
            Reads an embedded group field from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadBytes">
            <summary>
            Reads a bytes field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadUInt32">
            <summary>
            Reads a uint32 field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadEnum">
            <summary>
            Reads an enum field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadSFixed32">
            <summary>
            Reads an sfixed32 field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadSFixed64">
            <summary>
            Reads an sfixed64 field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadSInt32">
            <summary>
            Reads an sint32 field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadSInt64">
            <summary>
            Reads an sint64 field value from the stream.
            </summary>   
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadLength">
            <summary>
            Reads a length for length-delimited data.
            </summary>
            <remarks>
            This is internally just reading a varint, but this method exists
            to make the calling code clearer.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.MaybeConsumeTag(System.UInt32)">
            <summary>
            Peeks at the next tag in the stream. If it matches <paramref name="tag"/>,
            the tag is consumed and the method returns <c>true</c>; otherwise, the
            stream is left in the original position and the method returns <c>false</c>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.SlowReadRawVarint32">
            <summary>
            Same code as ReadRawVarint32, but read each byte individually, checking for
            buffer overflow.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadRawVarint32">
            <summary>
            Reads a raw Varint from the stream.  If larger than 32 bits, discard the upper bits.
            This method is optimised for the case where we've got lots of data in the buffer.
            That means we can check the size just once, then just read directly from the buffer
            without constant rechecking of the buffer length.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadRawVarint32(System.IO.Stream)">
            <summary>
            Reads a varint from the input one byte at a time, so that it does not
            read any bytes after the end of the varint. If you simply wrapped the
            stream in a CodedInputStream and used ReadRawVarint32(Stream)
            then you would probably end up reading past the end of the varint since
            CodedInputStream buffers its input.
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadRawVarint64">
            <summary>
            Reads a raw varint from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadRawLittleEndian32">
            <summary>
            Reads a 32-bit little-endian integer from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadRawLittleEndian64">
            <summary>
            Reads a 64-bit little-endian integer from the stream.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.DecodeZigZag32(System.UInt32)">
            <summary>
            Decode a 32-bit value with ZigZag encoding.
            </summary>
            <remarks>
            ZigZag encodes signed integers into values that can be efficiently
            encoded with varint.  (Otherwise, negative values must be 
            sign-extended to 64 bits to be varint encoded, thus always taking
            10 bytes on the wire.)
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.DecodeZigZag64(System.UInt64)">
            <summary>
            Decode a 32-bit value with ZigZag encoding.
            </summary>
            <remarks>
            ZigZag encodes signed integers into values that can be efficiently
            encoded with varint.  (Otherwise, negative values must be 
            sign-extended to 64 bits to be varint encoded, thus always taking
            10 bytes on the wire.)
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.PushLimit(System.Int32)">
            <summary>
            Sets currentLimit to (current position) + byteLimit. This is called
            when descending into a length-delimited embedded message. The previous
            limit is returned.
            </summary>
            <returns>The old limit.</returns>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.PopLimit(System.Int32)">
            <summary>
            Discards the current limit, returning the previous limit.
            </summary>
        </member>
        <member name="P:Google.Protobuf.CodedInputStream.ReachedLimit">
            <summary>
            Returns whether or not all the data before the limit has been read.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Google.Protobuf.CodedInputStream.IsAtEnd">
            <summary>
            Returns true if the stream has reached the end of the input. This is the
            case if either the end of the underlying input source has been reached or
            the stream has reached a limit created using PushLimit.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.RefillBuffer(System.Boolean)">
            <summary>
            Called when buffer is empty to read more bytes from the
            input.  If <paramref name="mustSucceed"/> is true, RefillBuffer() gurantees that
            either there will be at least one byte in the buffer when it returns
            or it will throw an exception.  If <paramref name="mustSucceed"/> is false,
            RefillBuffer() returns false if no more bytes were available.
            </summary>
            <param name="mustSucceed"></param>
            <returns></returns>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadRawByte">
            <summary>
            Read one byte from the input.
            </summary>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">
            the end of the stream or the current limit was reached
            </exception>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.ReadRawBytes(System.Int32)">
            <summary>
            Reads a fixed size of bytes from the input.
            </summary>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">
            the end of the stream or the current limit was reached
            </exception>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.SkipRawBytes(System.Int32)">
            <summary>
            Reads and discards <paramref name="size"/> bytes.
            </summary>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">the end of the stream
            or the current limit was reached</exception>
        </member>
        <member name="M:Google.Protobuf.CodedInputStream.SkipImpl(System.Int32)">
            <summary>
            Abstraction of skipping to cope with streams which can't really skip.
            </summary>
        </member>
        <member name="T:Google.Protobuf.CodedOutputStream">
            <summary>
            Encodes and writes protocol message fields.
            </summary>
            <remarks>
            <para>
            This class is generally used by generated code to write appropriate
            primitives to the stream. It effectively encapsulates the lowest
            levels of protocol buffer format. Unlike some other implementations,
            this does not include combined "write tag and value" methods. Generated
            code knows the exact byte representations of the tags they're going to write,
            so there's no need to re-encode them each time. Manually-written code calling
            this class should just call one of the <c>WriteTag</c> overloads before each value.
            </para>
            <para>
            Repeated fields and map fields are not handled by this class; use <c>RepeatedField&lt;T&gt;</c>
            and <c>MapField&lt;TKey, TValue&gt;</c> to serialize such fields.
            </para>
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeDoubleSize(System.Double)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            double field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeFloatSize(System.Single)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            float field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeUInt64Size(System.UInt64)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            uint64 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeInt64Size(System.Int64)">
            <summary>
            Computes the number of bytes that would be needed to encode an
            int64 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeInt32Size(System.Int32)">
            <summary>
            Computes the number of bytes that would be needed to encode an
            int32 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeFixed64Size(System.UInt64)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            fixed64 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeFixed32Size(System.UInt32)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            fixed32 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeBoolSize(System.Boolean)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            bool field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeStringSize(System.String)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            string field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeGroupSize(Google.Protobuf.IMessage)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            group field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeMessageSize(Google.Protobuf.IMessage)">
            <summary>
            Computes the number of bytes that would be needed to encode an
            embedded message field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeBytesSize(Google.Protobuf.ByteString)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            bytes field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeUInt32Size(System.UInt32)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            uint32 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeEnumSize(System.Int32)">
            <summary>
            Computes the number of bytes that would be needed to encode a
            enum field, including the tag. The caller is responsible for
            converting the enum value to its numeric value.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeSFixed32Size(System.Int32)">
            <summary>
            Computes the number of bytes that would be needed to encode an
            sfixed32 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeSFixed64Size(System.Int64)">
            <summary>
            Computes the number of bytes that would be needed to encode an
            sfixed64 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeSInt32Size(System.Int32)">
            <summary>
            Computes the number of bytes that would be needed to encode an
            sint32 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeSInt64Size(System.Int64)">
            <summary>
            Computes the number of bytes that would be needed to encode an
            sint64 field, including the tag.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeLengthSize(System.Int32)">
            <summary>
            Computes the number of bytes that would be needed to encode a length,
            as written by <see cref="M:Google.Protobuf.CodedOutputStream.WriteLength(System.Int32)"/>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeRawVarint32Size(System.UInt32)">
            <summary>
            Computes the number of bytes that would be needed to encode a varint.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeRawVarint64Size(System.UInt64)">
            <summary>
            Computes the number of bytes that would be needed to encode a varint.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.ComputeTagSize(System.Int32)">
            <summary>
            Computes the number of bytes that would be needed to encode a tag.
            </summary>
        </member>
        <member name="F:Google.Protobuf.CodedOutputStream.DefaultBufferSize">
            <summary>
            The buffer size used by CreateInstance(Stream).
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.#ctor(System.Byte[])">
            <summary>
            Creates a new CodedOutputStream that writes directly to the given
            byte array. If more bytes are written than fit in the array,
            OutOfSpaceException will be thrown.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.#ctor(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Creates a new CodedOutputStream that writes directly to the given
            byte array slice. If more bytes are written than fit in the array,
            OutOfSpaceException will be thrown.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.#ctor(System.IO.Stream)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.CodedOutputStream" /> which write to the given stream, and disposes of that
            stream when the returned <c>CodedOutputStream</c> is disposed.
            </summary>
            <param name="output">The stream to write to. It will be disposed when the returned <c>CodedOutputStream is disposed.</c></param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Creates a new CodedOutputStream which write to the given stream and uses
            the specified buffer size.
            </summary>
            <param name="output">The stream to write to. It will be disposed when the returned <c>CodedOutputStream is disposed.</c></param>
            <param name="bufferSize">The size of buffer to use internally.</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Creates a new CodedOutputStream which write to the given stream.
            </summary>
            <param name="output">The stream to write to.</param>
            <param name="leaveOpen">If <c>true</c>, <paramref name="output"/> is left open when the returned <c>CodedOutputStream</c> is disposed;
            if <c>false</c>, the provided stream is disposed as well.</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.#ctor(System.IO.Stream,System.Int32,System.Boolean)">
            <summary>
            Creates a new CodedOutputStream which write to the given stream and uses
            the specified buffer size.
            </summary>
            <param name="output">The stream to write to.</param>
            <param name="bufferSize">The size of buffer to use internally.</param>
            <param name="leaveOpen">If <c>true</c>, <paramref name="output"/> is left open when the returned <c>CodedOutputStream</c> is disposed;
            if <c>false</c>, the provided stream is disposed as well.</param>
        </member>
        <member name="P:Google.Protobuf.CodedOutputStream.Position">
            <summary>
            Returns the current position in the stream, or the position in the output buffer
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteDouble(System.Double)">
            <summary>
            Writes a double field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteFloat(System.Single)">
            <summary>
            Writes a float field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteUInt64(System.UInt64)">
            <summary>
            Writes a uint64 field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteInt64(System.Int64)">
            <summary>
            Writes an int64 field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteInt32(System.Int32)">
            <summary>
            Writes an int32 field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteFixed64(System.UInt64)">
            <summary>
            Writes a fixed64 field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteFixed32(System.UInt32)">
            <summary>
            Writes a fixed32 field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteBool(System.Boolean)">
            <summary>
            Writes a bool field value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteString(System.String)">
            <summary>
            Writes a string field value, without a tag, to the stream.
            The data is length-prefixed.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteMessage(Google.Protobuf.IMessage)">
            <summary>
            Writes a message, without a tag, to the stream.
            The data is length-prefixed.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteGroup(Google.Protobuf.IMessage)">
            <summary>
            Writes a group, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteBytes(Google.Protobuf.ByteString)">
            <summary>
            Write a byte string, without a tag, to the stream.
            The data is length-prefixed.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteUInt32(System.UInt32)">
            <summary>
            Writes a uint32 value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteEnum(System.Int32)">
            <summary>
            Writes an enum value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteSFixed32(System.Int32)">
            <summary>
            Writes an sfixed32 value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteSFixed64(System.Int64)">
            <summary>
            Writes an sfixed64 value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteSInt32(System.Int32)">
            <summary>
            Writes an sint32 value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteSInt64(System.Int64)">
            <summary>
            Writes an sint64 value, without a tag, to the stream.
            </summary>
            <param name="value">The value to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteLength(System.Int32)">
            <summary>
            Writes a length (in bytes) for length-delimited data.
            </summary>
            <remarks>
            This method simply writes a rawint, but exists for clarity in calling code.
            </remarks>
            <param name="length">Length value, in bytes.</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteTag(System.Int32,Google.Protobuf.WireFormat.WireType)">
            <summary>
            Encodes and writes a tag.
            </summary>
            <param name="fieldNumber">The number of the field to write the tag for</param>
            <param name="type">The wire format type of the tag to write</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteTag(System.UInt32)">
            <summary>
            Writes an already-encoded tag.
            </summary>
            <param name="tag">The encoded tag</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawTag(System.Byte)">
            <summary>
            Writes the given single-byte tag directly to the stream.
            </summary>
            <param name="b1">The encoded tag</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawTag(System.Byte,System.Byte)">
            <summary>
            Writes the given two-byte tag directly to the stream.
            </summary>
            <param name="b1">The first byte of the encoded tag</param>
            <param name="b2">The second byte of the encoded tag</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawTag(System.Byte,System.Byte,System.Byte)">
            <summary>
            Writes the given three-byte tag directly to the stream.
            </summary>
            <param name="b1">The first byte of the encoded tag</param>
            <param name="b2">The second byte of the encoded tag</param>
            <param name="b3">The third byte of the encoded tag</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawTag(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Writes the given four-byte tag directly to the stream.
            </summary>
            <param name="b1">The first byte of the encoded tag</param>
            <param name="b2">The second byte of the encoded tag</param>
            <param name="b3">The third byte of the encoded tag</param>
            <param name="b4">The fourth byte of the encoded tag</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawTag(System.Byte,System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Writes the given five-byte tag directly to the stream.
            </summary>
            <param name="b1">The first byte of the encoded tag</param>
            <param name="b2">The second byte of the encoded tag</param>
            <param name="b3">The third byte of the encoded tag</param>
            <param name="b4">The fourth byte of the encoded tag</param>
            <param name="b5">The fifth byte of the encoded tag</param>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawVarint32(System.UInt32)">
            <summary>
            Writes a 32 bit value as a varint. The fast route is taken when
            there's enough buffer space left to whizz through without checking
            for each byte; otherwise, we resort to calling WriteRawByte each time.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawBytes(System.Byte[])">
            <summary>
            Writes out an array of bytes.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.WriteRawBytes(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes out part of an array of bytes.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.EncodeZigZag32(System.Int32)">
            <summary>
            Encode a 32-bit value with ZigZag encoding.
            </summary>
            <remarks>
            ZigZag encodes signed integers into values that can be efficiently
            encoded with varint.  (Otherwise, negative values must be 
            sign-extended to 64 bits to be varint encoded, thus always taking
            10 bytes on the wire.)
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.EncodeZigZag64(System.Int64)">
            <summary>
            Encode a 64-bit value with ZigZag encoding.
            </summary>
            <remarks>
            ZigZag encodes signed integers into values that can be efficiently
            encoded with varint.  (Otherwise, negative values must be 
            sign-extended to 64 bits to be varint encoded, thus always taking
            10 bytes on the wire.)
            </remarks>
        </member>
        <member name="T:Google.Protobuf.CodedOutputStream.OutOfSpaceException">
            <summary>
            Indicates that a CodedOutputStream wrapping a flat byte array
            ran out of space.
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.Dispose">
            <summary>
            Flushes any buffered data and optionally closes the underlying stream, if any.
            </summary>
            <remarks>
            <para>
            By default, any underlying stream is closed by this method. To configure this behaviour,
            use a constructor overload with a <c>leaveOpen</c> parameter. If this instance does not
            have an underlying stream, this method does nothing.
            </para>
            <para>
            For the sake of efficiency, calling this method does not prevent future write calls - but
            if a later write ends up writing to a stream which has been disposed, that is likely to
            fail. It is recommend that you not call any other methods after this.
            </para>
            </remarks>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.Flush">
            <summary>
            Flushes any buffered data to the underlying stream (if there is one).
            </summary>
        </member>
        <member name="M:Google.Protobuf.CodedOutputStream.CheckNoSpaceLeft">
            <summary>
            Verifies that SpaceLeft returns zero. It's common to create a byte array
            that is exactly big enough to hold a message, then write to it with
            a CodedOutputStream. Calling CheckNoSpaceLeft after writing verifies that
            the message was actually as big as expected, which can help bugs.
            </summary>
        </member>
        <member name="P:Google.Protobuf.CodedOutputStream.SpaceLeft">
            <summary>
            If writing to a flat array, returns the space left in the array. Otherwise,
            throws an InvalidOperationException.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Collections.Lists">
            <summary>
            Utility to compare if two Lists are the same, and the hash code
            of a List.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.Lists.Equals``1(System.Collections.Generic.List{``0},System.Collections.Generic.List{``0})">
            <summary>
            Checks if two lists are equal.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.Lists.GetHashCode``1(System.Collections.Generic.List{``0})">
            <summary>
            Gets the list's hash code.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Collections.MapField`2">
            <summary>
            Representation of a map field in a Protocol Buffer message.
            </summary>
            <typeparam name="TKey">Key type in the map. Must be a type supported by Protocol Buffer map keys.</typeparam>
            <typeparam name="TValue">Value type in the map. Must be a type supported by Protocol Buffers.</typeparam>
            <remarks>
            <para>
            For string keys, the equality comparison is provided by <see cref="P:System.StringComparer.Ordinal" />.
            </para>
            <para>
            Null values are not permitted in the map, either for wrapper types or regular messages.
            If a map is deserialized from a data stream and the value is missing from an entry, a default value
            is created instead. For primitive types, that is the regular default value (0, the empty string and so
            on); for message types, an empty instance of the message is created, as if the map entry contained a 0-length
            encoded value for the field.
            </para>
            <para>
            This implementation does not generally prohibit the use of key/value types which are not
            supported by Protocol Buffers (e.g. using a key type of <code>byte</code>) but nor does it guarantee
            that all operations will work in such cases.
            </para>
            <para>
            The order in which entries are returned when iterating over this object is undefined, and may change
            in future versions.
            </para>
            </remarks>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Clone">
            <summary>
            Creates a deep clone of this object.
            </summary>
            <returns>
            A deep clone of this object.
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Add(`0,`1)">
            <summary>
            Adds the specified key/value pair to the map.
            </summary>
            <remarks>
            This operation fails if the key already exists in the map. To replace an existing entry, use the indexer.
            </remarks>
            <param name="key">The key to add</param>
            <param name="value">The value to add.</param>
            <exception cref="T:System.ArgumentException">The given key already exists in map.</exception>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.ContainsKey(`0)">
            <summary>
            Determines whether the specified key is present in the map.
            </summary>
            <param name="key">The key to check.</param>
            <returns><c>true</c> if the map contains the given key; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Remove(`0)">
            <summary>
            Removes the entry identified by the given key from the map.
            </summary>
            <param name="key">The key indicating the entry to remove from the map.</param>
            <returns><c>true</c> if the map contained the given key before the entry was removed; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.TryGetValue(`0,`1@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found;
            otherwise, the default value for the type of the <paramref name="value"/> parameter.
            This parameter is passed uninitialized.</param>
            <returns><c>true</c> if the map contains an element with the specified key; otherwise, <c>false</c>.</returns>
        </member>
        <member name="P:Google.Protobuf.Collections.MapField`2.Item(`0)">
            <summary>
            Gets or sets the value associated with the specified key.
            </summary>
            <param name="key">The key of the value to get or set.</param>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">The property is retrieved and key does not exist in the collection.</exception>
            <returns>The value associated with the specified key. If the specified key is not found,
            a get operation throws a <see cref="T:System.Collections.Generic.KeyNotFoundException"/>, and a set operation creates a new element with the specified key.</returns>
        </member>
        <member name="P:Google.Protobuf.Collections.MapField`2.Keys">
            <summary>
            Gets a collection containing the keys in the map.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Collections.MapField`2.Values">
            <summary>
            Gets a collection containing the values in the map.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Add(System.Collections.Generic.IDictionary{`0,`1})">
            <summary>
            Adds the specified entries to the map. The keys and values are not automatically cloned.
            </summary>
            <param name="entries">The entries to add to the map.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            An enumerator that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,TValue}}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Adds the specified item to the map.
            </summary>
            <param name="item">The item to add to the map.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Clear">
            <summary>
            Removes all items from the map.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,TValue}}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Determines whether map contains an entry equivalent to the given key/value pair.
            </summary>
            <param name="item">The key/value pair to find.</param>
            <returns></returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,TValue}}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
            <summary>
            Copies the key/value pairs in this map to an array.
            </summary>
            <param name="array">The array to copy the entries into.</param>
            <param name="arrayIndex">The index of the array at which to start copying values.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,TValue}}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Removes the specified key/value pair from the map.
            </summary>
            <remarks>Both the key and the value must be found for the entry to be removed.</remarks>
            <param name="item">The key/value pair to remove.</param>
            <returns><c>true</c> if the key/value pair was found and removed; <c>false</c> otherwise.</returns>
        </member>
        <member name="P:Google.Protobuf.Collections.MapField`2.Count">
            <summary>
            Gets the number of elements contained in the map.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Collections.MapField`2.IsReadOnly">
            <summary>
            Gets a value indicating whether the map is read-only.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:System.Object" /> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Equals(Google.Protobuf.Collections.MapField{`0,`1})">
            <summary>
            Compares this map with another for equality.
            </summary>
            <remarks>
            The order of the key/value pairs in the maps is not deemed significant in this comparison.
            </remarks>
            <param name="other">The map to compare this with.</param>
            <returns><c>true</c> if <paramref name="other"/> refers to an equal map; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.AddEntriesFrom(Google.Protobuf.CodedInputStream,Google.Protobuf.Collections.MapField{`0,`1}.Codec)">
            <summary>
            Adds entries to the map from the given stream.
            </summary>
            <remarks>
            It is assumed that the stream is initially positioned after the tag specified by the codec.
            This method will continue reading entries from the stream until the end is reached, or
            a different tag is encountered.
            </remarks>
            <param name="input">Stream to read from</param>
            <param name="codec">Codec describing how the key/value pairs are encoded</param>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.WriteTo(Google.Protobuf.CodedOutputStream,Google.Protobuf.Collections.MapField{`0,`1}.Codec)">
            <summary>
            Writes the contents of this map to the given coded output stream, using the specified codec
            to encode each entry.
            </summary>
            <param name="output">The output stream to write to.</param>
            <param name="codec">The codec to use for each entry.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.CalculateSize(Google.Protobuf.Collections.MapField{`0,`1}.Codec)">
            <summary>
            Calculates the size of this map based on the given entry codec.
            </summary>
            <param name="codec">The codec to use to encode each entry.</param>
            <returns></returns>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.ToString">
            <summary>
            Returns a string representation of this repeated field, in the same
            way as it would be represented by the default JSON formatter.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Collections.MapField`2.Codec">
            <summary>
            A codec for a specific map field. This contains all the information required to encode and
            decode the nested messages.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.MapField`2.Codec.#ctor(Google.Protobuf.FieldCodec{`0},Google.Protobuf.FieldCodec{`1},System.UInt32)">
            <summary>
            Creates a new entry codec based on a separate key codec and value codec,
            and the tag to use for each map entry.
            </summary>
            <param name="keyCodec">The key codec.</param>
            <param name="valueCodec">The value codec.</param>
            <param name="mapTag">The map tag to use to introduce each map entry.</param>
        </member>
        <member name="P:Google.Protobuf.Collections.MapField`2.Codec.MapTag">
            <summary>
            The tag used in the enclosing message to indicate map entries.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Collections.MapField`2.Codec.MessageAdapter">
            <summary>
            A mutable message class, used for parsing and serializing. This
            delegates the work to a codec, but implements the <see cref="T:Google.Protobuf.IMessage"/> interface
            for interop with <see cref="T:Google.Protobuf.CodedInputStream"/> and <see cref="T:Google.Protobuf.CodedOutputStream"/>.
            This is nested inside Codec as it's tightly coupled to the associated codec,
            and it's simpler if it has direct access to all its fields.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Collections.ProtobufEqualityComparers">
            <summary>
            Provides a central place to implement equality comparisons, primarily for bitwise float/double equality.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.ProtobufEqualityComparers.GetEqualityComparer``1">
            <summary>
            Returns an equality comparer for <typeparamref name="T"/> suitable for Protobuf equality comparisons.
            This is usually just the default equality comparer for the type, but floating point numbers are compared
            bitwise.
            </summary>
            <typeparam name="T">The type of equality comparer to return.</typeparam>
            <returns>The equality comparer.</returns>
        </member>
        <member name="P:Google.Protobuf.Collections.ProtobufEqualityComparers.BitwiseDoubleEqualityComparer">
            <summary>
            Returns an equality comparer suitable for comparing 64-bit floating point values, by bitwise comparison.
            (NaN values are considered equal, but only when they have the same representation.)
            </summary>
        </member>
        <member name="P:Google.Protobuf.Collections.ProtobufEqualityComparers.BitwiseSingleEqualityComparer">
            <summary>
            Returns an equality comparer suitable for comparing 32-bit floating point values, by bitwise comparison.
            (NaN values are considered equal, but only when they have the same representation.)
            </summary>
        </member>
        <member name="P:Google.Protobuf.Collections.ProtobufEqualityComparers.BitwiseNullableDoubleEqualityComparer">
            <summary>
            Returns an equality comparer suitable for comparing nullable 64-bit floating point values, by bitwise comparison.
            (NaN values are considered equal, but only when they have the same representation.)
            </summary>
        </member>
        <member name="P:Google.Protobuf.Collections.ProtobufEqualityComparers.BitwiseNullableSingleEqualityComparer">
            <summary>
            Returns an equality comparer suitable for comparing nullable 32-bit floating point values, by bitwise comparison.
            (NaN values are considered equal, but only when they have the same representation.)
            </summary>
        </member>
        <member name="T:Google.Protobuf.Collections.ReadOnlyDictionary`2">
            <summary>
            Read-only wrapper around another dictionary.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Collections.RepeatedField`1">
            <summary>
            The contents of a repeated field: essentially, a collection with some extra
            restrictions (no null values) and capabilities (deep cloning).
            </summary>
            <remarks>
            This implementation does not generally prohibit the use of types which are not
            supported by Protocol Buffers but nor does it guarantee that all operations will work in such cases.
            </remarks>
            <typeparam name="T">The element type of the repeated field.</typeparam>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Clone">
            <summary>
            Creates a deep clone of this repeated field.
            </summary>
            <remarks>
            If the field type is
            a message type, each element is also cloned; otherwise, it is
            assumed that the field type is primitive (including string and
            bytes, both of which are immutable) and so a simple copy is
            equivalent to a deep clone.
            </remarks>
            <returns>A deep clone of this repeated field.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.AddEntriesFrom(Google.Protobuf.CodedInputStream,Google.Protobuf.FieldCodec{`0})">
            <summary>
            Adds the entries from the given input stream, decoding them with the specified codec.
            </summary>
            <param name="input">The input stream to read from.</param>
            <param name="codec">The codec to use in order to read each entry.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.CalculateSize(Google.Protobuf.FieldCodec{`0})">
            <summary>
            Calculates the size of this collection based on the given codec.
            </summary>
            <param name="codec">The codec to use when encoding each field.</param>
            <returns>The number of bytes that would be written to a <see cref="T:Google.Protobuf.CodedOutputStream"/> by <see cref="M:Google.Protobuf.Collections.RepeatedField`1.WriteTo(Google.Protobuf.CodedOutputStream,Google.Protobuf.FieldCodec{`0})"/>,
            using the same codec.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.WriteTo(Google.Protobuf.CodedOutputStream,Google.Protobuf.FieldCodec{`0})">
            <summary>
            Writes the contents of this collection to the given <see cref="T:Google.Protobuf.CodedOutputStream"/>,
            encoding each value using the specified codec.
            </summary>
            <param name="output">The output stream to write to.</param>
            <param name="codec">The codec to use when encoding each value.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Add(`0)">
            <summary>
            Adds the specified item to the collection.
            </summary>
            <param name="item">The item to add.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Contains(`0)">
            <summary>
            Determines whether this collection contains the given item.
            </summary>
            <param name="item">The item to find.</param>
            <returns><c>true</c> if this collection contains the given item; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.CopyTo(`0[],System.Int32)">
            <summary>
            Copies this collection to the given array.
            </summary>
            <param name="array">The array to copy to.</param>
            <param name="arrayIndex">The first index of the array to copy to.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Remove(`0)">
            <summary>
            Removes the specified item from the collection
            </summary>
            <param name="item">The item to remove.</param>
            <returns><c>true</c> if the item was found and removed; <c>false</c> otherwise.</returns>
        </member>
        <member name="P:Google.Protobuf.Collections.RepeatedField`1.Count">
            <summary>
            Gets the number of elements contained in the collection.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Collections.RepeatedField`1.IsReadOnly">
            <summary>
            Gets a value indicating whether the collection is read-only.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Adds all of the specified values into this collection.
            </summary>
            <param name="values">The values to add to this collection.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Add(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Adds all of the specified values into this collection. This method is present to
            allow repeated fields to be constructed from queries within collection initializers.
            Within non-collection-initializer code, consider using the equivalent <see cref="M:Google.Protobuf.Collections.RepeatedField`1.AddRange(System.Collections.Generic.IEnumerable{`0})"/>
            method instead for clarity.
            </summary>
            <param name="values">The values to add to this collection.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            An enumerator that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Equals(Google.Protobuf.Collections.RepeatedField{`0})">
            <summary>
            Compares this repeated field with another for equality.
            </summary>
            <param name="other">The repeated field to compare this with.</param>
            <returns><c>true</c> if <paramref name="other"/> refers to an equal repeated field; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.IndexOf(`0)">
            <summary>
            Returns the index of the given item within the collection, or -1 if the item is not
            present.
            </summary>
            <param name="item">The item to find in the collection.</param>
            <returns>The zero-based index of the item, or -1 if it is not found.</returns>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.Insert(System.Int32,`0)">
            <summary>
            Inserts the given item at the specified index.
            </summary>
            <param name="index">The index at which to insert the item.</param>
            <param name="item">The item to insert.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.RemoveAt(System.Int32)">
            <summary>
            Removes the item at the given index.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
        </member>
        <member name="M:Google.Protobuf.Collections.RepeatedField`1.ToString">
            <summary>
            Returns a string representation of this repeated field, in the same
            way as it would be represented by the default JSON formatter.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Collections.RepeatedField`1.Item(System.Int32)">
            <summary>
            Gets or sets the item at the specified index.
            </summary>
            <value>
            The element at the specified index.
            </value>
            <param name="index">The zero-based index of the element to get or set.</param>
            <returns>The item at the specified index.</returns>
        </member>
        <member name="T:Google.Protobuf.Compatibility.PropertyInfoExtensions">
            <summary>
            Extension methods for <see cref="T:System.Reflection.PropertyInfo"/>, effectively providing
            the familiar members from previous desktop framework versions while
            targeting the newer releases, .NET Core etc.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Compatibility.PropertyInfoExtensions.GetGetMethod(System.Reflection.PropertyInfo)">
            <summary>
            Returns the public getter of a property, or null if there is no such getter
            (either because it's read-only, or the getter isn't public).
            </summary>
        </member>
        <member name="M:Google.Protobuf.Compatibility.PropertyInfoExtensions.GetSetMethod(System.Reflection.PropertyInfo)">
            <summary>
            Returns the public setter of a property, or null if there is no such setter
            (either because it's write-only, or the setter isn't public).
            </summary>
        </member>
        <member name="T:Google.Protobuf.Compatibility.TypeExtensions">
            <summary>
            Provides extension methods on Type that just proxy to TypeInfo.
            These are used to support the new type system from .NET 4.5, without
            having calls to GetTypeInfo all over the place. While the methods here are meant to be
            broadly compatible with the desktop framework, there are some subtle differences in behaviour - but
            they're not expected to affect our use cases. While the class is internal, that should be fine: we can
            evaluate each new use appropriately.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Compatibility.TypeExtensions.IsAssignableFrom(System.Type,System.Type)">
            <summary>
            See https://msdn.microsoft.com/en-us/library/system.type.isassignablefrom
            </summary>
        </member>
        <member name="M:Google.Protobuf.Compatibility.TypeExtensions.GetProperty(System.Type,System.String)">
            <summary>
            Returns a representation of the public property associated with the given name in the given type,
            including inherited properties or null if there is no such public property.
            Here, "public property" means a property where either the getter, or the setter, or both, is public.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Compatibility.TypeExtensions.GetMethod(System.Type,System.String)">
            <summary>
            Returns a representation of the public method associated with the given name in the given type,
            including inherited methods.
            </summary>
            <remarks>
            This has a few differences compared with Type.GetMethod in the desktop framework. It will throw
            if there is an ambiguous match even between a private method and a public one, but it *won't* throw
            if there are two overloads at different levels in the type hierarchy (e.g. class Base declares public void Foo(int) and
            class Child : Base declares public void Foo(long)).
            </remarks>
            <exception cref="T:System.Reflection.AmbiguousMatchException">One type in the hierarchy declared more than one method with the same name</exception>
        </member>
        <member name="T:Google.Protobuf.FieldCodec">
            <summary>
            Factory methods for <see cref="T:Google.Protobuf.FieldCodec`1"/>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForString(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a string field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForBytes(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a bytes field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForBool(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a bool field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForInt32(System.UInt32)">
            <summary>
            Retrieves a codec suitable for an int32 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForSInt32(System.UInt32)">
            <summary>
            Retrieves a codec suitable for an sint32 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForFixed32(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a fixed32 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForSFixed32(System.UInt32)">
            <summary>
            Retrieves a codec suitable for an sfixed32 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForUInt32(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a uint32 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForInt64(System.UInt32)">
            <summary>
            Retrieves a codec suitable for an int64 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForSInt64(System.UInt32)">
            <summary>
            Retrieves a codec suitable for an sint64 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForFixed64(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a fixed64 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForSFixed64(System.UInt32)">
            <summary>
            Retrieves a codec suitable for an sfixed64 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForUInt64(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a uint64 field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForFloat(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a float field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForDouble(System.UInt32)">
            <summary>
            Retrieves a codec suitable for a double field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForEnum``1(System.UInt32,System.Func{``0,System.Int32},System.Func{System.Int32,``0})">
            <summary>
            Retrieves a codec suitable for an enum field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <param name="toInt32">A conversion function from <see cref="T:System.Int32"/> to the enum type.</param>
            <param name="fromInt32">A conversion function from the enum type to <see cref="T:System.Int32"/>.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForMessage``1(System.UInt32,Google.Protobuf.MessageParser{``0})">
            <summary>
            Retrieves a codec suitable for a message field with the given tag.
            </summary>
            <param name="tag">The tag.</param>
            <param name="parser">A parser to use for the message type.</param>
            <returns>A codec for the given tag.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForGroup``1(System.UInt32,System.UInt32,Google.Protobuf.MessageParser{``0})">
            <summary>
            Retrieves a codec suitable for a group field with the given tag.
            </summary>
            <param name="startTag">The start group tag.</param>
            <param name="endTag">The end group tag.</param>
            <param name="parser">A parser to use for the group message type.</param>
            <returns>A codec for given tag</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForClassWrapper``1(System.UInt32)">
            <summary>
            Creates a codec for a wrapper type of a class - which must be string or ByteString.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.ForStructWrapper``1(System.UInt32)">
            <summary>
            Creates a codec for a wrapper type of a struct - which must be Int32, Int64, UInt32, UInt64,
            Bool, Single or Double.
            </summary>
        </member>
        <member name="T:Google.Protobuf.FieldCodec.WrapperCodecs">
            <summary>
            Helper code to create codecs for wrapper types.
            </summary>
            <remarks>
            Somewhat ugly with all the static methods, but the conversions involved to/from nullable types make it
            slightly tricky to improve. So long as we keep the public API (ForClassWrapper, ForStructWrapper) in place,
            we can refactor later if we come up with something cleaner.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.FieldCodec.WrapperCodecs.GetCodec``1">
            <summary>
            Returns a field codec which effectively wraps a value of type T in a message.
            
            </summary>
        </member>
        <member name="T:Google.Protobuf.FieldCodec`1">
            <summary>
            <para>
            An encode/decode pair for a single field. This effectively encapsulates
            all the information needed to read or write the field value from/to a coded
            stream.
            </para>
            <para>
            This class is public and has to be as it is used by generated code, but its public
            API is very limited - just what the generated code needs to call directly.
            </para>
            </summary>
            <remarks>
            This never writes default values to the stream, and does not address "packedness"
            in repeated fields itself, other than to know whether or not the field *should* be packed.
            </remarks>
        </member>
        <member name="P:Google.Protobuf.FieldCodec`1.ValueWriter">
            <summary>
            Returns a delegate to write a value (unconditionally) to a coded output stream.
            </summary>
        </member>
        <member name="P:Google.Protobuf.FieldCodec`1.ValueSizeCalculator">
            <summary>
            Returns the size calculator for just a value.
            </summary>
        </member>
        <member name="P:Google.Protobuf.FieldCodec`1.ValueReader">
            <summary>
            Returns a delegate to read a value from a coded input stream. It is assumed that
            the stream is already positioned on the appropriate tag.
            </summary>
        </member>
        <member name="P:Google.Protobuf.FieldCodec`1.FixedSize">
            <summary>
            Returns the fixed size for an entry, or 0 if sizes vary.
            </summary>
        </member>
        <member name="P:Google.Protobuf.FieldCodec`1.Tag">
            <summary>
            Gets the tag of the codec.
            </summary>
            <value>
            The tag of the codec.
            </value>
        </member>
        <member name="P:Google.Protobuf.FieldCodec`1.EndTag">
            <summary>
            Gets the end tag of the codec or 0 if there is no end tag
            </summary>
            <value>
            The end tag of the codec.
            </value>
        </member>
        <member name="P:Google.Protobuf.FieldCodec`1.DefaultValue">
            <summary>
            Default value for this codec. Usually the same for every instance of the same type, but
            for string/ByteString wrapper fields the codec's default value is null, whereas for
            other string/ByteString fields it's "" or ByteString.Empty.
            </summary>
            <value>
            The default value of the codec's type.
            </value>
        </member>
        <member name="M:Google.Protobuf.FieldCodec`1.WriteTagAndValue(Google.Protobuf.CodedOutputStream,`0)">
            <summary>
            Write a tag and the given value, *if* the value is not the default.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldCodec`1.Read(Google.Protobuf.CodedInputStream)">
            <summary>
            Reads a value of the codec type from the given <see cref="T:Google.Protobuf.CodedInputStream"/>.
            </summary>
            <param name="input">The input stream to read from.</param>
            <returns>The value read from the stream.</returns>
        </member>
        <member name="M:Google.Protobuf.FieldCodec`1.CalculateSizeWithTag(`0)">
            <summary>
            Calculates the size required to write the given value, with a tag,
            if the value is not the default.
            </summary>
        </member>
        <member name="T:Google.Protobuf.FieldMaskTree">
            <summary>
            <para>A tree representation of a FieldMask. Each leaf node in this tree represent
            a field path in the FieldMask.</para>
            
            <para>For example, FieldMask "foo.bar,foo.baz,bar.baz" as a tree will be:</para>
            <code>
              [root] -+- foo -+- bar
                      |       |
                      |       +- baz
                      |
                      +- bar --- baz
            </code>
            
            <para>By representing FieldMasks with this tree structure we can easily convert
            a FieldMask to a canonical form, merge two FieldMasks, calculate the
            intersection to two FieldMasks and traverse all fields specified by the
            FieldMask in a message tree.</para>
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.#ctor">
            <summary>
            Creates an empty FieldMaskTree.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.#ctor(Google.Protobuf.WellKnownTypes.FieldMask)">
            <summary>
            Creates a FieldMaskTree for a given FieldMask.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.AddFieldPath(System.String)">
            <summary>
            Adds a field path to the tree. In a FieldMask, every field path matches the
            specified field as well as all its sub-fields. For example, a field path
            "foo.bar" matches field "foo.bar" and also "foo.bar.baz", etc. When adding
            a field path to the tree, redundant sub-paths will be removed. That is,
            after adding "foo.bar" to the tree, "foo.bar.baz" will be removed if it
            exists, which will turn the tree node for "foo.bar" to a leaf node.
            Likewise, if the field path to add is a sub-path of an existing leaf node,
            nothing will be changed in the tree.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.MergeFromFieldMask(Google.Protobuf.WellKnownTypes.FieldMask)">
            <summary>
            Merges all field paths in a FieldMask into this tree.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.ToFieldMask">
            <summary>
            Converts this tree to a FieldMask.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.GetFieldPaths(Google.Protobuf.FieldMaskTree.Node,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Gathers all field paths in a sub-tree.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.IntersectFieldPath(System.String,Google.Protobuf.FieldMaskTree)">
            <summary>
            Adds the intersection of this tree with the given <paramref name="path"/> to <paramref name="output"/>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.Merge(Google.Protobuf.IMessage,Google.Protobuf.IMessage,Google.Protobuf.WellKnownTypes.FieldMask.MergeOptions)">
            <summary>
            Merges all fields specified by this FieldMaskTree from <paramref name="source"/> to <paramref name="destination"/>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.FieldMaskTree.Merge(Google.Protobuf.FieldMaskTree.Node,System.String,Google.Protobuf.IMessage,Google.Protobuf.IMessage,Google.Protobuf.WellKnownTypes.FieldMask.MergeOptions)">
            <summary>
            Merges all fields specified by a sub-tree from <paramref name="source"/> to <paramref name="destination"/>.
            </summary>
        </member>
        <member name="T:Google.Protobuf.FrameworkPortability">
            <summary>
            Class containing helpful workarounds for various platform compatibility
            </summary>
        </member>
        <member name="T:Google.Protobuf.ICustomDiagnosticMessage">
            <summary>
            A message type that has a custom string format for diagnostic purposes.
            </summary>
            <remarks>
            <para>
            Calling <see cref="M:System.Object.ToString"/> on a generated message type normally
            returns the JSON representation. If a message type implements this interface,
            then the <see cref="M:Google.Protobuf.ICustomDiagnosticMessage.ToDiagnosticString"/> method will be called instead of the regular
            JSON formatting code, but only when <c>ToString()</c> is called either on the message itself
            or on another message which contains it. This does not affect the normal JSON formatting of
            the message.
            </para>
            <para>
            For example, if you create a proto message representing a GUID, the internal
            representation may be a <c>bytes</c> field or four <c>fixed32</c> fields. However, when debugging
            it may be more convenient to see a result in the same format as <see cref="T:System.Guid"/> provides.
            </para>
            <para>This interface extends <see cref="T:Google.Protobuf.IMessage"/> to avoid it accidentally being implemented
            on types other than messages, where it would not be used by anything in the framework.</para>
            </remarks>
        </member>
        <member name="M:Google.Protobuf.ICustomDiagnosticMessage.ToDiagnosticString">
            <summary>
            Returns a string representation of this object, for diagnostic purposes.
            </summary>
            <remarks>
            This method is called when a message is formatted as part of a <see cref="M:System.Object.ToString"/>
            call. It does not affect the JSON representation used by <see cref="T:Google.Protobuf.JsonFormatter"/> other than
            in calls to <see cref="M:Google.Protobuf.JsonFormatter.ToDiagnosticString(Google.Protobuf.IMessage)"/>. While it is recommended
            that the result is valid JSON, this is never assumed by the Protobuf library.
            </remarks>
            <returns>A string representation of this object, for diagnostic purposes.</returns>
        </member>
        <member name="T:Google.Protobuf.IDeepCloneable`1">
            <summary>
            Generic interface for a deeply cloneable type.
            </summary>
            <remarks>
            <para>
            All generated messages implement this interface, but so do some non-message types.
            Additionally, due to the type constraint on <c>T</c> in <see cref="T:Google.Protobuf.IMessage`1"/>,
            it is simpler to keep this as a separate interface.
            </para>
            </remarks>
            <typeparam name="T">The type itself, returned by the <see cref="M:Google.Protobuf.IDeepCloneable`1.Clone"/> method.</typeparam>
        </member>
        <member name="M:Google.Protobuf.IDeepCloneable`1.Clone">
            <summary>
            Creates a deep clone of this object.
            </summary>
            <returns>A deep clone of this object.</returns>
        </member>
        <member name="T:Google.Protobuf.IMessage">
            <summary>
            Interface for a Protocol Buffers message, supporting
            basic operations required for serialization.
            </summary>
        </member>
        <member name="M:Google.Protobuf.IMessage.MergeFrom(Google.Protobuf.CodedInputStream)">
            <summary>
            Merges the data from the specified coded input stream with the current message.
            </summary>
            <remarks>See the user guide for precise merge semantics.</remarks>
            <param name="input"></param>
        </member>
        <member name="M:Google.Protobuf.IMessage.WriteTo(Google.Protobuf.CodedOutputStream)">
            <summary>
            Writes the data to the given coded output stream.
            </summary>
            <param name="output">Coded output stream to write the data to. Must not be null.</param>
        </member>
        <member name="M:Google.Protobuf.IMessage.CalculateSize">
            <summary>
            Calculates the size of this message in Protocol Buffer wire format, in bytes.
            </summary>
            <returns>The number of bytes required to write this message
            to a coded output stream.</returns>
        </member>
        <member name="P:Google.Protobuf.IMessage.Descriptor">
            <summary>
            Descriptor for this message. All instances are expected to return the same descriptor,
            and for generated types this will be an explicitly-implemented member, returning the
            same value as the static property declared on the type.
            </summary>
        </member>
        <member name="T:Google.Protobuf.IMessage`1">
            <summary>
            Generic interface for a Protocol Buffers message,
            where the type parameter is expected to be the same type as
            the implementation class.
            </summary>
            <typeparam name="T">The message type.</typeparam>
        </member>
        <member name="M:Google.Protobuf.IMessage`1.MergeFrom(`0)">
            <summary>
            Merges the given message into this one.
            </summary>
            <remarks>See the user guide for precise merge semantics.</remarks>
            <param name="message">The message to merge with this one. Must not be null.</param>
        </member>
        <member name="T:Google.Protobuf.InvalidJsonException">
            <summary>
            Thrown when an attempt is made to parse invalid JSON, e.g. using
            a non-string property key, or including a redundant comma. Parsing a protocol buffer
            message represented in JSON using <see cref="T:Google.Protobuf.JsonParser"/> can throw both this
            exception and <see cref="T:Google.Protobuf.InvalidProtocolBufferException"/> depending on the situation. This
            exception is only thrown for "pure JSON" errors, whereas <c>InvalidProtocolBufferException</c>
            is thrown when the JSON may be valid in and of itself, but cannot be parsed as a protocol buffer
            message.
            </summary>
        </member>
        <member name="T:Google.Protobuf.InvalidProtocolBufferException">
            <summary>
            Thrown when a protocol message being parsed is invalid in some way,
            e.g. it contains a malformed varint or a negative byte length.
            </summary>
        </member>
        <member name="M:Google.Protobuf.InvalidProtocolBufferException.InvalidTag">
            <summary>
            Creates an exception for an error condition of an invalid tag being encountered.
            </summary>
        </member>
        <member name="T:Google.Protobuf.JsonFormatter">
            <summary>
            Reflection-based converter from messages to JSON.
            </summary>
            <remarks>
            <para>
            Instances of this class are thread-safe, with no mutable state.
            </para>
            <para>
            This is a simple start to get JSON formatting working. As it's reflection-based,
            it's not as quick as baking calls into generated messages - but is a simpler implementation.
            (This code is generally not heavily optimized.)
            </para>
            </remarks>
        </member>
        <member name="P:Google.Protobuf.JsonFormatter.Default">
            <summary>
            Returns a formatter using the default settings.
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonFormatter.CommonRepresentations">
            <summary>
            The JSON representation of the first 160 characters of Unicode.
            Empty strings are replaced by the static constructor.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.#ctor(Google.Protobuf.JsonFormatter.Settings)">
            <summary>
            Creates a new formatted with the given settings.
            </summary>
            <param name="settings">The settings.</param>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Format(Google.Protobuf.IMessage)">
            <summary>
            Formats the specified message as JSON.
            </summary>
            <param name="message">The message to format.</param>
            <returns>The formatted message.</returns>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Format(Google.Protobuf.IMessage,System.IO.TextWriter)">
            <summary>
            Formats the specified message as JSON.
            </summary>
            <param name="message">The message to format.</param>
            <param name="writer">The TextWriter to write the formatted message to.</param>
            <returns>The formatted message.</returns>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.ToDiagnosticString(Google.Protobuf.IMessage)">
            <summary>
            Converts a message to JSON for diagnostic purposes with no extra context.
            </summary>
            <remarks>
            <para>
            This differs from calling <see cref="M:Google.Protobuf.JsonFormatter.Format(Google.Protobuf.IMessage)"/> on the default JSON
            formatter in its handling of <see cref="T:Google.Protobuf.WellKnownTypes.Any"/>. As no type registry is available
            in <see cref="M:System.Object.ToString"/> calls, the normal way of resolving the type of
            an <c>Any</c> message cannot be applied. Instead, a JSON property named <c>@value</c>
            is included with the base64 data from the <see cref="P:Google.Protobuf.WellKnownTypes.Any.Value"/> property of the message.
            </para>
            <para>The value returned by this method is only designed to be used for diagnostic
            purposes. It may not be parsable by <see cref="T:Google.Protobuf.JsonParser"/>, and may not be parsable
            by other Protocol Buffer implementations.</para>
            </remarks>
            <param name="message">The message to format for diagnostic purposes.</param>
            <returns>The diagnostic-only JSON representation of the message</returns>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.WriteValue(System.IO.TextWriter,System.Object)">
            <summary>
            Writes a single value to the given writer as JSON. Only types understood by
            Protocol Buffers can be written in this way. This method is only exposed for
            advanced use cases; most users should be using <see cref="M:Google.Protobuf.JsonFormatter.Format(Google.Protobuf.IMessage)"/>
            or <see cref="M:Google.Protobuf.JsonFormatter.Format(Google.Protobuf.IMessage,System.IO.TextWriter)"/>.
            </summary>
            <param name="writer">The writer to write the value to. Must not be null.</param>
            <param name="value">The value to write. May be null.</param>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.WriteWellKnownTypeValue(System.IO.TextWriter,Google.Protobuf.Reflection.MessageDescriptor,System.Object)">
            <summary>
            Central interception point for well-known type formatting. Any well-known types which
            don't need special handling can fall back to WriteMessage. We avoid assuming that the
            values are using the embedded well-known types, in order to allow for dynamic messages
            in the future.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.WriteString(System.IO.TextWriter,System.String)">
            <summary>
            Writes a string (including leading and trailing double quotes) to a builder, escaping as required.
            </summary>
            <remarks>
            Other than surrogate pair handling, this code is mostly taken from src/google/protobuf/util/internal/json_escaping.cc.
            </remarks>
        </member>
        <member name="T:Google.Protobuf.JsonFormatter.Settings">
            <summary>
            Settings controlling JSON formatting.
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonFormatter.Settings.Default">
            <summary>
            Default settings, as used by <see cref="P:Google.Protobuf.JsonFormatter.Default"/>
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonFormatter.Settings.FormatDefaultValues">
            <summary>
            Whether fields whose values are the default for the field type (e.g. 0 for integers)
            should be formatted (true) or omitted (false).
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonFormatter.Settings.TypeRegistry">
            <summary>
            The type registry used to format <see cref="T:Google.Protobuf.WellKnownTypes.Any"/> messages.
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonFormatter.Settings.FormatEnumsAsIntegers">
            <summary>
            Whether to format enums as ints. Defaults to false.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Settings.#ctor(System.Boolean)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonFormatter.Settings"/> object with the specified formatting of default values
            and an empty type registry.
            </summary>
            <param name="formatDefaultValues"><c>true</c> if default values (0, empty strings etc) should be formatted; <c>false</c> otherwise.</param>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Settings.#ctor(System.Boolean,Google.Protobuf.Reflection.TypeRegistry)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonFormatter.Settings"/> object with the specified formatting of default values
            and type registry.
            </summary>
            <param name="formatDefaultValues"><c>true</c> if default values (0, empty strings etc) should be formatted; <c>false</c> otherwise.</param>
            <param name="typeRegistry">The <see cref="P:Google.Protobuf.JsonFormatter.Settings.TypeRegistry"/> to use when formatting <see cref="T:Google.Protobuf.WellKnownTypes.Any"/> messages.</param>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Settings.#ctor(System.Boolean,Google.Protobuf.Reflection.TypeRegistry,System.Boolean)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonFormatter.Settings"/> object with the specified parameters.
            </summary>
            <param name="formatDefaultValues"><c>true</c> if default values (0, empty strings etc) should be formatted; <c>false</c> otherwise.</param>
            <param name="typeRegistry">The <see cref="P:Google.Protobuf.JsonFormatter.Settings.TypeRegistry"/> to use when formatting <see cref="T:Google.Protobuf.WellKnownTypes.Any"/> messages. TypeRegistry.Empty will be used if it is null.</param>
            <param name="formatEnumsAsIntegers"><c>true</c> to format the enums as integers; <c>false</c> to format enums as enum names.</param>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Settings.WithFormatDefaultValues(System.Boolean)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonFormatter.Settings"/> object with the specified formatting of default values and the current settings.
            </summary>
            <param name="formatDefaultValues"><c>true</c> if default values (0, empty strings etc) should be formatted; <c>false</c> otherwise.</param>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Settings.WithTypeRegistry(Google.Protobuf.Reflection.TypeRegistry)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonFormatter.Settings"/> object with the specified type registry and the current settings.
            </summary>
            <param name="typeRegistry">The <see cref="P:Google.Protobuf.JsonFormatter.Settings.TypeRegistry"/> to use when formatting <see cref="T:Google.Protobuf.WellKnownTypes.Any"/> messages.</param>
        </member>
        <member name="M:Google.Protobuf.JsonFormatter.Settings.WithFormatEnumsAsIntegers(System.Boolean)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonFormatter.Settings"/> object with the specified enums formatting option and the current settings.
            </summary>
            <param name="formatEnumsAsIntegers"><c>true</c> to format the enums as integers; <c>false</c> to format enums as enum names.</param>
        </member>
        <member name="T:Google.Protobuf.JsonParser">
            <summary>
            Reflection-based converter from JSON to messages.
            </summary>
            <remarks>
            <para>
            Instances of this class are thread-safe, with no mutable state.
            </para>
            <para>
            This is a simple start to get JSON parsing working. As it's reflection-based,
            it's not as quick as baking calls into generated messages - but is a simpler implementation.
            (This code is generally not heavily optimized.)
            </para>
            </remarks>
        </member>
        <member name="P:Google.Protobuf.JsonParser.Default">
            <summary>
            Returns a formatter using the default settings.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonParser.#ctor(Google.Protobuf.JsonParser.Settings)">
            <summary>
            Creates a new formatted with the given settings.
            </summary>
            <param name="settings">The settings.</param>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Merge(Google.Protobuf.IMessage,System.String)">
            <summary>
            Parses <paramref name="json"/> and merges the information into the given message.
            </summary>
            <param name="message">The message to merge the JSON information into.</param>
            <param name="json">The JSON to parse.</param>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Merge(Google.Protobuf.IMessage,System.IO.TextReader)">
            <summary>
            Parses JSON read from <paramref name="jsonReader"/> and merges the information into the given message.
            </summary>
            <param name="message">The message to merge the JSON information into.</param>
            <param name="jsonReader">Reader providing the JSON to parse.</param>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Merge(Google.Protobuf.IMessage,Google.Protobuf.JsonTokenizer)">
            <summary>
            Merges the given message using data from the given tokenizer. In most cases, the next
            token should be a "start object" token, but wrapper types and nullity can invalidate
            that assumption. This is implemented as an LL(1) recursive descent parser over the stream
            of tokens provided by the tokenizer. This token stream is assumed to be valid JSON, with the
            tokenizer performing that validation - but not every token stream is valid "protobuf JSON".
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Parse``1(System.String)">
            <summary>
            Parses <paramref name="json"/> into a new message.
            </summary>
            <typeparam name="T">The type of message to create.</typeparam>
            <param name="json">The JSON to parse.</param>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The JSON does not comply with RFC 7159</exception>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The JSON does not represent a Protocol Buffers message correctly</exception>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Parse``1(System.IO.TextReader)">
            <summary>
            Parses JSON read from <paramref name="jsonReader"/> into a new message.
            </summary>
            <typeparam name="T">The type of message to create.</typeparam>
            <param name="jsonReader">Reader providing the JSON to parse.</param>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The JSON does not comply with RFC 7159</exception>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The JSON does not represent a Protocol Buffers message correctly</exception>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Parse(System.String,Google.Protobuf.Reflection.MessageDescriptor)">
            <summary>
            Parses <paramref name="json"/> into a new message.
            </summary>
            <param name="json">The JSON to parse.</param>
            <param name="descriptor">Descriptor of message type to parse.</param>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The JSON does not comply with RFC 7159</exception>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The JSON does not represent a Protocol Buffers message correctly</exception>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Parse(System.IO.TextReader,Google.Protobuf.Reflection.MessageDescriptor)">
            <summary>
            Parses JSON read from <paramref name="jsonReader"/> into a new message.
            </summary>
            <param name="jsonReader">Reader providing the JSON to parse.</param>
            <param name="descriptor">Descriptor of message type to parse.</param>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The JSON does not comply with RFC 7159</exception>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The JSON does not represent a Protocol Buffers message correctly</exception>
        </member>
        <member name="M:Google.Protobuf.JsonParser.NewMessageForField(Google.Protobuf.Reflection.FieldDescriptor)">
            <summary>
            Creates a new instance of the message type for the given field.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonParser.ValidateInfinityAndNan(System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Checks that any infinite/NaN values originated from the correct text.
            This corrects the lenient whitespace handling of double.Parse/float.Parse, as well as the
            way that Mono parses out-of-range values as infinity.
            </summary>
        </member>
        <member name="T:Google.Protobuf.JsonParser.Settings">
            <summary>
            Settings controlling JSON parsing.
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonParser.Settings.Default">
            <summary>
            Default settings, as used by <see cref="P:Google.Protobuf.JsonParser.Default"/>. This has the same default
            recursion limit as <see cref="T:Google.Protobuf.CodedInputStream"/>, and an empty type registry.
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonParser.Settings.RecursionLimit">
            <summary>
            The maximum depth of messages to parse. Note that this limit only applies to parsing
            messages, not collections - so a message within a collection within a message only counts as
            depth 2, not 3.
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonParser.Settings.TypeRegistry">
            <summary>
            The type registry used to parse <see cref="T:Google.Protobuf.WellKnownTypes.Any"/> messages.
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonParser.Settings.IgnoreUnknownFields">
            <summary>
            Whether the parser should ignore unknown fields (<c>true</c>) or throw an exception when
            they are encountered (<c>false</c>).
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Settings.#ctor(System.Int32)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonParser.Settings"/> object with the specified recursion limit.
            </summary>
            <param name="recursionLimit">The maximum depth of messages to parse</param>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Settings.#ctor(System.Int32,Google.Protobuf.Reflection.TypeRegistry)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonParser.Settings"/> object with the specified recursion limit and type registry.
            </summary>
            <param name="recursionLimit">The maximum depth of messages to parse</param>
            <param name="typeRegistry">The type registry used to parse <see cref="T:Google.Protobuf.WellKnownTypes.Any"/> messages</param>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Settings.WithIgnoreUnknownFields(System.Boolean)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonParser.Settings"/> object set to either ignore unknown fields, or throw an exception
            when unknown fields are encountered.
            </summary>
            <param name="ignoreUnknownFields"><c>true</c> if unknown fields should be ignored when parsing; <c>false</c> to throw an exception.</param>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Settings.WithRecursionLimit(System.Int32)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonParser.Settings"/> object based on this one, but with the specified recursion limit.
            </summary>
            <param name="recursionLimit">The new recursion limit.</param>
        </member>
        <member name="M:Google.Protobuf.JsonParser.Settings.WithTypeRegistry(Google.Protobuf.Reflection.TypeRegistry)">
            <summary>
            Creates a new <see cref="T:Google.Protobuf.JsonParser.Settings"/> object based on this one, but with the specified type registry.
            </summary>
            <param name="typeRegistry">The new type registry. Must not be null.</param>
        </member>
        <member name="T:Google.Protobuf.JsonTokenizer">
            <summary>
            Simple but strict JSON tokenizer, rigidly following RFC 7159.
            </summary>
            <remarks>
            <para>
            This tokenizer is stateful, and only returns "useful" tokens - names, values etc.
            It does not create tokens for the separator between names and values, or for the comma
            between values. It validates the token stream as it goes - so callers can assume that the
            tokens it produces are appropriate. For example, it would never produce "start object, end array."
            </para>
            <para>Implementation details: the base class handles single token push-back and </para>
            <para>Not thread-safe.</para>
            </remarks>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.FromTextReader(System.IO.TextReader)">
            <summary>
             Creates a tokenizer that reads from the given text reader.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.FromReplayedTokens(System.Collections.Generic.IList{Google.Protobuf.JsonToken},Google.Protobuf.JsonTokenizer)">
            <summary>
            Creates a tokenizer that first replays the given list of tokens, then continues reading
            from another tokenizer. Note that if the returned tokenizer is "pushed back", that does not push back
            on the continuation tokenizer, or vice versa. Care should be taken when using this method - it was
            created for the sake of Any parsing.
            </summary>
        </member>
        <member name="P:Google.Protobuf.JsonTokenizer.ObjectDepth">
            <summary>
            Returns the depth of the stack, purely in objects (not collections).
            Informally, this is the number of remaining unclosed '{' characters we have.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.Next">
            <summary>
            Returns the next JSON token in the stream. An EndDocument token is returned to indicate the end of the stream,
            after which point <c>Next()</c> should not be called again.
            </summary>
            <remarks>This implementation provides single-token buffering, and calls <see cref="M:Google.Protobuf.JsonTokenizer.NextImpl"/> if there is no buffered token.</remarks>
            <returns>The next token in the stream. This is never null.</returns>
            <exception cref="T:System.InvalidOperationException">This method is called after an EndDocument token has been returned</exception>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The input text does not comply with RFC 7159</exception>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.NextImpl">
            <summary>
            Returns the next JSON token in the stream, when requested by the base class. (The <see cref="M:Google.Protobuf.JsonTokenizer.Next"/> method delegates
            to this if it doesn't have a buffered token.)
            </summary>
            <exception cref="T:System.InvalidOperationException">This method is called after an EndDocument token has been returned</exception>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The input text does not comply with RFC 7159</exception>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.SkipValue">
            <summary>
            Skips the value we're about to read. This must only be called immediately after reading a property name.
            If the value is an object or an array, the complete object/array is skipped.
            </summary>
        </member>
        <member name="T:Google.Protobuf.JsonTokenizer.JsonReplayTokenizer">
            <summary>
            Tokenizer which first exhausts a list of tokens, then consults another tokenizer.
            </summary>
        </member>
        <member name="T:Google.Protobuf.JsonTokenizer.JsonTextTokenizer">
            <summary>
            Tokenizer which does all the *real* work of parsing JSON.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.NextImpl">
            <remarks>
            This method essentially just loops through characters skipping whitespace, validating and
            changing state (e.g. from ObjectBeforeColon to ObjectAfterColon)
            until it reaches something which will be a genuine token (e.g. a start object, or a value) at which point
            it returns the token. Although the method is large, it would be relatively hard to break down further... most
            of it is the large switch statement, which sometimes returns and sometimes doesn't.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.ReadString">
            <summary>
            Reads a string token. It is assumed that the opening " has already been read.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.ReadEscapedCharacter">
            <summary>
            Reads an escaped character. It is assumed that the leading backslash has already been read.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.ReadUnicodeEscape">
            <summary>
            Reads an escaped Unicode 4-nybble hex sequence. It is assumed that the leading \u has already been read.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.ConsumeLiteral(System.String)">
            <summary>
            Consumes a text-only literal, throwing an exception if the read text doesn't match it.
            It is assumed that the first letter of the literal has already been read.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.ValidateAndModifyStateForValue(System.String)">
            <summary>
            Validates that we're in a valid state to read a value (using the given error prefix if necessary)
            and changes the state to the appropriate one, e.g. ObjectAfterColon to ObjectAfterProperty.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.PopContainer">
            <summary>
            Pops the top-most container, and sets the state to the appropriate one for the end of a value
            in the parent container.
            </summary>
        </member>
        <member name="T:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State">
            <summary>
            Possible states of the tokenizer.
            </summary>
            <remarks>
            <para>This is a flags enum purely so we can simply and efficiently represent a set of valid states
            for checking.</para>
            <para>
            Each is documented with an example,
            where ^ represents the current position within the text stream. The examples all use string values,
            but could be any value, including nested objects/arrays.
            The complete state of the tokenizer also includes a stack to indicate the contexts (arrays/objects).
            Any additional notional state of "AfterValue" indicates that a value has been completed, at which 
            point there's an immediate transition to ExpectedEndOfDocument,  ObjectAfterProperty or ArrayAfterValue.
            </para>
            <para>
            These states were derived manually by reading RFC 7159 carefully.
            </para>
            </remarks>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.StartOfDocument">
            <summary>
            ^ { "foo": "bar" }
            Before the value in a document. Next states: ObjectStart, ArrayStart, "AfterValue"
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ExpectedEndOfDocument">
            <summary>
            { "foo": "bar" } ^
            After the value in a document. Next states: ReaderExhausted
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ReaderExhausted">
            <summary>
            { "foo": "bar" } ^ (and already read to the end of the reader)
            Terminal state.
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ObjectStart">
            <summary>
            { ^ "foo": "bar" }
            Before the *first* property in an object.
            Next states:
            "AfterValue" (empty object)
            ObjectBeforeColon (read a name)
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ObjectBeforeColon">
            <summary>
            { "foo" ^ : "bar", "x": "y" }
            Next state: ObjectAfterColon
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ObjectAfterColon">
            <summary>
            { "foo" : ^ "bar", "x": "y" }
            Before any property other than the first in an object.
            (Equivalently: after any property in an object) 
            Next states:
            "AfterValue" (value is simple)
            ObjectStart (value is object)
            ArrayStart (value is array)
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ObjectAfterProperty">
            <summary>
            { "foo" : "bar" ^ , "x" : "y" }
            At the end of a property, so expecting either a comma or end-of-object
            Next states: ObjectAfterComma or "AfterValue"
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ObjectAfterComma">
            <summary>
            { "foo":"bar", ^ "x":"y" }
            Read the comma after the previous property, so expecting another property.
            This is like ObjectStart, but closing brace isn't valid here
            Next state: ObjectBeforeColon.
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ArrayStart">
            <summary>
            [ ^ "foo", "bar" ]
            Before the *first* value in an array.
            Next states:
            "AfterValue" (read a value)
            "AfterValue" (end of array; will pop stack)
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ArrayAfterValue">
            <summary>
            [ "foo" ^ , "bar" ]
            After any value in an array, so expecting either a comma or end-of-array
            Next states: ArrayAfterComma or "AfterValue"
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.State.ArrayAfterComma">
            <summary>
            [ "foo", ^ "bar" ]
            After a comma in an array, so there *must* be another value (simple or complex).
            Next states: "AfterValue" (simple value), StartObject, StartArray
            </summary>
        </member>
        <member name="T:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.PushBackReader">
            <summary>
            Wrapper around a text reader allowing small amounts of buffering and location handling.
            </summary>
        </member>
        <member name="F:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.PushBackReader.nextChar">
            <summary>
            The buffered next character, if we have one.
            </summary>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.PushBackReader.Read">
            <summary>
            Returns the next character in the stream, or null if we have reached the end.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Google.Protobuf.JsonTokenizer.JsonTextTokenizer.PushBackReader.CreateException(System.String)">
            <summary>
            Creates a new exception appropriate for the current state of the reader.
            </summary>
        </member>
        <member name="T:Google.Protobuf.LimitedInputStream">
            <summary>
            Stream implementation which proxies another stream, only allowing a certain amount
            of data to be read. Note that this is only used to read delimited streams, so it
            doesn't attempt to implement everything.
            </summary>
        </member>
        <member name="T:Google.Protobuf.MessageExtensions">
            <summary>
            Extension methods on <see cref="T:Google.Protobuf.IMessage"/> and <see cref="T:Google.Protobuf.IMessage`1"/>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.MergeFrom(Google.Protobuf.IMessage,System.Byte[])">
            <summary>
            Merges data from the given byte array into an existing message.
            </summary>
            <param name="message">The message to merge the data into.</param>
            <param name="data">The data to merge, which must be protobuf-encoded binary data.</param>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.MergeFrom(Google.Protobuf.IMessage,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Merges data from the given byte array slice into an existing message.
            </summary>
            <param name="message">The message to merge the data into.</param>
            <param name="data">The data containing the slice to merge, which must be protobuf-encoded binary data.</param>
            <param name="offset">The offset of the slice to merge.</param>
            <param name="length">The length of the slice to merge.</param>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.MergeFrom(Google.Protobuf.IMessage,Google.Protobuf.ByteString)">
            <summary>
            Merges data from the given byte string into an existing message.
            </summary>
            <param name="message">The message to merge the data into.</param>
            <param name="data">The data to merge, which must be protobuf-encoded binary data.</param>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.MergeFrom(Google.Protobuf.IMessage,System.IO.Stream)">
            <summary>
            Merges data from the given stream into an existing message.
            </summary>
            <param name="message">The message to merge the data into.</param>
            <param name="input">Stream containing the data to merge, which must be protobuf-encoded binary data.</param>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.MergeDelimitedFrom(Google.Protobuf.IMessage,System.IO.Stream)">
            <summary>
            Merges length-delimited data from the given stream into an existing message.
            </summary>
            <remarks>
            The stream is expected to contain a length and then the data. Only the amount of data
            specified by the length will be consumed.
            </remarks>
            <param name="message">The message to merge the data into.</param>
            <param name="input">Stream containing the data to merge, which must be protobuf-encoded binary data.</param>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.ToByteArray(Google.Protobuf.IMessage)">
            <summary>
            Converts the given message into a byte array in protobuf encoding.
            </summary>
            <param name="message">The message to convert.</param>
            <returns>The message data as a byte array.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.WriteTo(Google.Protobuf.IMessage,System.IO.Stream)">
            <summary>
            Writes the given message data to the given stream in protobuf encoding.
            </summary>
            <param name="message">The message to write to the stream.</param>
            <param name="output">The stream to write to.</param>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.WriteDelimitedTo(Google.Protobuf.IMessage,System.IO.Stream)">
            <summary>
            Writes the length and then data of the given message to a stream.
            </summary>
            <param name="message">The message to write.</param>
            <param name="output">The output stream to write to.</param>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.ToByteString(Google.Protobuf.IMessage)">
            <summary>
            Converts the given message into a byte string in protobuf encoding.
            </summary>
            <param name="message">The message to convert.</param>
            <returns>The message data as a byte string.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageExtensions.IsInitialized(Google.Protobuf.IMessage)">
            <summary>
            Checks if all required fields in a message have values set. For proto3 messages, this returns true
            </summary>
        </member>
        <member name="T:Google.Protobuf.MessageParser">
            <summary>
            A general message parser, typically used by reflection-based code as all the methods
            return simple <see cref="T:Google.Protobuf.IMessage"/>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.MessageParser.CreateTemplate">
            <summary>
            Creates a template instance ready for population.
            </summary>
            <returns>An empty message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser.ParseFrom(System.Byte[])">
            <summary>
            Parses a message from a byte array.
            </summary>
            <param name="data">The byte array containing the message. Must not be null.</param>
            <returns>The newly parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser.ParseFrom(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Parses a message from a byte array slice.
            </summary>
            <param name="data">The byte array containing the message. Must not be null.</param>
            <param name="offset">The offset of the slice to parse.</param>
            <param name="length">The length of the slice to parse.</param>
            <returns>The newly parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser.ParseFrom(Google.Protobuf.ByteString)">
            <summary>
            Parses a message from the given byte string.
            </summary>
            <param name="data">The data to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser.ParseFrom(System.IO.Stream)">
            <summary>
            Parses a message from the given stream.
            </summary>
            <param name="input">The stream to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser.ParseDelimitedFrom(System.IO.Stream)">
            <summary>
            Parses a length-delimited message from the given stream.
            </summary>
            <remarks>
            The stream is expected to contain a length and then the data. Only the amount of data
            specified by the length will be consumed.
            </remarks>
            <param name="input">The stream to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser.ParseFrom(Google.Protobuf.CodedInputStream)">
            <summary>
            Parses a message from the given coded input stream.
            </summary>
            <param name="input">The stream to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser.ParseJson(System.String)">
            <summary>
            Parses a message from the given JSON.
            </summary>
            <param name="json">The JSON to parse.</param>
            <returns>The parsed message.</returns>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The JSON does not comply with RFC 7159</exception>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The JSON does not represent a Protocol Buffers message correctly</exception>
        </member>
        <member name="M:Google.Protobuf.MessageParser.WithDiscardUnknownFields(System.Boolean)">
            <summary>
            Creates a new message parser which optionally discards unknown fields when parsing.
            </summary>
            <param name="discardUnknownFields">Whether or not to discard unknown fields when parsing.</param>
            <returns>A newly configured message parser.</returns>
        </member>
        <member name="T:Google.Protobuf.MessageParser`1">
            <summary>
            A parser for a specific message type.
            </summary>
            <remarks>
            <p>
            This delegates most behavior to the
            <see cref="M:Google.Protobuf.IMessage.MergeFrom(Google.Protobuf.CodedInputStream)"/> implementation within the original type, but
            provides convenient overloads to parse from a variety of sources.
            </p>
            <p>
            Most applications will never need to create their own instances of this type;
            instead, use the static <c>Parser</c> property of a generated message type to obtain a
            parser for that type.
            </p>
            </remarks>
            <typeparam name="T">The type of message to be parsed.</typeparam>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.#ctor(System.Func{`0})">
            <summary>
            Creates a new parser.
            </summary>
            <remarks>
            The factory method is effectively an optimization over using a generic constraint
            to require a parameterless constructor: delegates are significantly faster to execute.
            </remarks>
            <param name="factory">Function to invoke when a new, empty message is required.</param>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.CreateTemplate">
            <summary>
            Creates a template instance ready for population.
            </summary>
            <returns>An empty message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.ParseFrom(System.Byte[])">
            <summary>
            Parses a message from a byte array.
            </summary>
            <param name="data">The byte array containing the message. Must not be null.</param>
            <returns>The newly parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.ParseFrom(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Parses a message from a byte array slice.
            </summary>
            <param name="data">The byte array containing the message. Must not be null.</param>
            <param name="offset">The offset of the slice to parse.</param>
            <param name="length">The length of the slice to parse.</param>
            <returns>The newly parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.ParseFrom(Google.Protobuf.ByteString)">
            <summary>
            Parses a message from the given byte string.
            </summary>
            <param name="data">The data to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.ParseFrom(System.IO.Stream)">
            <summary>
            Parses a message from the given stream.
            </summary>
            <param name="input">The stream to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.ParseDelimitedFrom(System.IO.Stream)">
            <summary>
            Parses a length-delimited message from the given stream.
            </summary>
            <remarks>
            The stream is expected to contain a length and then the data. Only the amount of data
            specified by the length will be consumed.
            </remarks>
            <param name="input">The stream to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.ParseFrom(Google.Protobuf.CodedInputStream)">
            <summary>
            Parses a message from the given coded input stream.
            </summary>
            <param name="input">The stream to parse.</param>
            <returns>The parsed message.</returns>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.ParseJson(System.String)">
            <summary>
            Parses a message from the given JSON.
            </summary>
            <param name="json">The JSON to parse.</param>
            <returns>The parsed message.</returns>
            <exception cref="T:Google.Protobuf.InvalidJsonException">The JSON does not comply with RFC 7159</exception>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The JSON does not represent a Protocol Buffers message correctly</exception>
        </member>
        <member name="M:Google.Protobuf.MessageParser`1.WithDiscardUnknownFields(System.Boolean)">
            <summary>
            Creates a new message parser which optionally discards unknown fields when parsing.
            </summary>
            <param name="discardUnknownFields">Whether or not to discard unknown fields when parsing.</param>
            <returns>A newly configured message parser.</returns>
        </member>
        <member name="T:Google.Protobuf.ProtoPreconditions">
            <summary>
            Helper methods for throwing exceptions when preconditions are not met.
            </summary>
            <remarks>
            This class is used internally and by generated code; it is not particularly
            expected to be used from application code, although nothing prevents it
            from being used that way.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.ProtoPreconditions.CheckNotNull``1(``0,System.String)">
            <summary>
            Throws an ArgumentNullException if the given value is null, otherwise
            return the value to the caller.
            </summary>
        </member>
        <member name="M:Google.Protobuf.ProtoPreconditions.CheckNotNullUnconstrained``1(``0,System.String)">
            <summary>
            Throws an ArgumentNullException if the given value is null, otherwise
            return the value to the caller.
            </summary>
            <remarks>
            This is equivalent to <see cref="M:Google.Protobuf.ProtoPreconditions.CheckNotNull``1(``0,System.String)"/> but without the type parameter
            constraint. In most cases, the constraint is useful to prevent you from calling CheckNotNull
            with a value type - but it gets in the way if either you want to use it with a nullable
            value type, or you want to use it with an unconstrained type parameter.
            </remarks>
        </member>
        <member name="T:Google.Protobuf.Reflection.CustomOptions">
            <summary>
            Container for a set of custom options specified within a message, field etc.
            </summary>
            <remarks>
            <para>
            This type is publicly immutable, but internally mutable. It is only populated
            by the descriptor parsing code - by the time any user code is able to see an instance,
            it will be fully initialized.
            </para>
            <para>
            If an option is requested using the incorrect method, an answer may still be returned: all
            of the numeric types are represented internally using 64-bit integers, for example. It is up to
            the caller to ensure that they make the appropriate method call for the option they're interested in.
            Note that enum options are simply stored as integers, so the value should be fetched using
            <see cref="M:Google.Protobuf.Reflection.CustomOptions.TryGetInt32(System.Int32,System.Int32@)"/> and then cast appropriately.
            </para>
            <para>
            Repeated options are currently not supported. Asking for a single value of an option
            which was actually repeated will return the last value, except for message types where
            all the set values are merged together.
            </para>
            </remarks>
        </member>
        <member name="F:Google.Protobuf.Reflection.CustomOptions.Empty">
            <summary>
            Singleton for all descriptors with an empty set of options.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.CustomOptions.valuesByField">
            <summary>
            A sequence of values per field. This needs to be per field rather than per tag to allow correct deserialization
            of repeated fields which could be "int, ByteString, int" - unlikely as that is. The fact that values are boxed
            is unfortunate; we might be able to use a struct instead, and we could combine uint and ulong values.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetBool(System.Int32,System.Boolean@)">
            <summary>
            Retrieves a Boolean value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetInt32(System.Int32,System.Int32@)">
            <summary>
            Retrieves a signed 32-bit integer value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetInt64(System.Int32,System.Int64@)">
            <summary>
            Retrieves a signed 64-bit integer value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetFixed32(System.Int32,System.UInt32@)">
            <summary>
            Retrieves an unsigned 32-bit integer value for the specified option field,
            assuming a fixed-length representation.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetFixed64(System.Int32,System.UInt64@)">
            <summary>
            Retrieves an unsigned 64-bit integer value for the specified option field,
            assuming a fixed-length representation.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetSFixed32(System.Int32,System.Int32@)">
            <summary>
            Retrieves a signed 32-bit integer value for the specified option field,
            assuming a fixed-length representation.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetSFixed64(System.Int32,System.Int64@)">
            <summary>
            Retrieves a signed 64-bit integer value for the specified option field,
            assuming a fixed-length representation.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetSInt32(System.Int32,System.Int32@)">
            <summary>
            Retrieves a signed 32-bit integer value for the specified option field,
            assuming a zigzag encoding.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetSInt64(System.Int32,System.Int64@)">
            <summary>
            Retrieves a signed 64-bit integer value for the specified option field,
            assuming a zigzag encoding.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetUInt32(System.Int32,System.UInt32@)">
            <summary>
            Retrieves an unsigned 32-bit integer value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetUInt64(System.Int32,System.UInt64@)">
            <summary>
            Retrieves an unsigned 64-bit integer value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetFloat(System.Int32,System.Single@)">
            <summary>
            Retrieves a 32-bit floating point value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetDouble(System.Int32,System.Double@)">
            <summary>
            Retrieves a 64-bit floating point value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetString(System.Int32,System.String@)">
            <summary>
            Retrieves a string value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetBytes(System.Int32,Google.Protobuf.ByteString@)">
            <summary>
            Retrieves a bytes value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.TryGetMessage``1(System.Int32,``0@)">
            <summary>
            Retrieves a message value for the specified option field.
            </summary>
            <param name="field">The field to fetch the value for.</param>
            <param name="value">The output variable to populate.</param>
            <returns><c>true</c> if a suitable value for the field was found; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.CustomOptions.ReadOrSkipUnknownField(Google.Protobuf.CodedInputStream)">
            <summary>
            Reads an unknown field, either parsing it and storing it or skipping it.
            </summary>
            <remarks>
            If the current set of options is empty and we manage to read a field, a new set of options
            will be created and returned. Otherwise, the return value is <c>this</c>. This allows
            us to start with a singleton empty set of options and just create new ones where necessary.
            </remarks>
            <param name="input">Input stream to read from. </param>
            <returns>The resulting set of custom options, either <c>this</c> or a new set.</returns>
        </member>
        <member name="T:Google.Protobuf.Reflection.CustomOptions.FieldValue">
            <summary>
            All field values can be stored as a byte string or a 64-bit integer.
            This struct avoids unnecessary boxing.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorReflection">
            <summary>Holder for reflection information generated from google/protobuf/descriptor.proto</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorReflection.Descriptor">
            <summary>File descriptor for google/protobuf/descriptor.proto</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FileDescriptorSet">
            <summary>
            The protocol compiler can output a FileDescriptorSet containing the .proto
            files it parses.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorSet.FileFieldNumber">
            <summary>Field number for the "file" field.</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FileDescriptorProto">
            <summary>
            Describes a complete .proto file.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.Name">
            <summary>
            file name, relative to root of source tree
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.PackageFieldNumber">
            <summary>Field number for the "package" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.Package">
            <summary>
            e.g. "foo", "foo.bar", etc.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.HasPackage">
            <summary>Gets whether the "package" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptorProto.ClearPackage">
            <summary>Clears the value of the "package" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.DependencyFieldNumber">
            <summary>Field number for the "dependency" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.Dependency">
            <summary>
            Names of files imported by this file.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.PublicDependencyFieldNumber">
            <summary>Field number for the "public_dependency" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.PublicDependency">
            <summary>
            Indexes of the public imported files in the dependency list above.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.WeakDependencyFieldNumber">
            <summary>Field number for the "weak_dependency" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.WeakDependency">
            <summary>
            Indexes of the weak imported files in the dependency list.
            For Google-internal migration only. Do not use.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.MessageTypeFieldNumber">
            <summary>Field number for the "message_type" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.MessageType">
            <summary>
            All top-level definitions in this file.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.EnumTypeFieldNumber">
            <summary>Field number for the "enum_type" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.ServiceFieldNumber">
            <summary>Field number for the "service" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.ExtensionFieldNumber">
            <summary>Field number for the "extension" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.SourceCodeInfoFieldNumber">
            <summary>Field number for the "source_code_info" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.SourceCodeInfo">
            <summary>
            This field contains optional information about the original source code.
            You may safely remove this entire field without harming runtime
            functionality of the descriptors -- the information is needed only by
            development tools.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.HasSourceCodeInfo">
            <summary>Gets whether the source_code_info field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptorProto.ClearSourceCodeInfo">
            <summary>Clears the value of the source_code_info field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileDescriptorProto.SyntaxFieldNumber">
            <summary>Field number for the "syntax" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.Syntax">
            <summary>
            The syntax of the proto file.
            The supported values are "proto2" and "proto3".
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptorProto.HasSyntax">
            <summary>Gets whether the "syntax" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptorProto.ClearSyntax">
            <summary>Clears the value of the "syntax" field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorProto">
            <summary>
            Describes a message type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.FieldFieldNumber">
            <summary>Field number for the "field" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.ExtensionFieldNumber">
            <summary>Field number for the "extension" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.NestedTypeFieldNumber">
            <summary>Field number for the "nested_type" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.EnumTypeFieldNumber">
            <summary>Field number for the "enum_type" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.ExtensionRangeFieldNumber">
            <summary>Field number for the "extension_range" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.OneofDeclFieldNumber">
            <summary>Field number for the "oneof_decl" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.ReservedRangeFieldNumber">
            <summary>Field number for the "reserved_range" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.ReservedNameFieldNumber">
            <summary>Field number for the "reserved_name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.ReservedName">
            <summary>
            Reserved field names, which may not be used by fields in the same message.
            A given name may only be reserved once.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorProto.Types">
            <summary>Container for nested types declared in the DescriptorProto message type.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.StartFieldNumber">
            <summary>Field number for the "start" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.HasStart">
            <summary>Gets whether the "start" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.ClearStart">
            <summary>Clears the value of the "start" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.EndFieldNumber">
            <summary>Field number for the "end" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.HasEnd">
            <summary>Gets whether the "end" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.ClearEnd">
            <summary>Clears the value of the "end" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorProto.Types.ExtensionRange.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange">
            <summary>
            Range of reserved tag numbers. Reserved tag numbers may not be used by
            fields or extension ranges in the same message. Reserved ranges may
            not overlap.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.StartFieldNumber">
            <summary>Field number for the "start" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.Start">
            <summary>
            Inclusive.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.HasStart">
            <summary>Gets whether the "start" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.ClearStart">
            <summary>Clears the value of the "start" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.EndFieldNumber">
            <summary>Field number for the "end" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.End">
            <summary>
            Exclusive.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.HasEnd">
            <summary>Gets whether the "end" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorProto.Types.ReservedRange.ClearEnd">
            <summary>Clears the value of the "end" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.ExtensionRangeOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ExtensionRangeOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FieldDescriptorProto">
            <summary>
            Describes a field within a message.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.NumberFieldNumber">
            <summary>Field number for the "number" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasNumber">
            <summary>Gets whether the "number" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearNumber">
            <summary>Clears the value of the "number" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.LabelFieldNumber">
            <summary>Field number for the "label" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasLabel">
            <summary>Gets whether the "label" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearLabel">
            <summary>Clears the value of the "label" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.Type">
            <summary>
            If type_name is set, this need not be set.  If both this and type_name
            are set, this must be one of TYPE_ENUM, TYPE_MESSAGE or TYPE_GROUP.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasType">
            <summary>Gets whether the "type" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearType">
            <summary>Clears the value of the "type" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.TypeNameFieldNumber">
            <summary>Field number for the "type_name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.TypeName">
            <summary>
            For message and enum types, this is the name of the type.  If the name
            starts with a '.', it is fully-qualified.  Otherwise, C++-like scoping
            rules are used to find the type (i.e. first the nested types within this
            message are searched, then within the parent, on up to the root
            namespace).
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasTypeName">
            <summary>Gets whether the "type_name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearTypeName">
            <summary>Clears the value of the "type_name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.ExtendeeFieldNumber">
            <summary>Field number for the "extendee" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.Extendee">
            <summary>
            For extensions, this is the name of the type being extended.  It is
            resolved in the same manner as type_name.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasExtendee">
            <summary>Gets whether the "extendee" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearExtendee">
            <summary>Clears the value of the "extendee" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.DefaultValueFieldNumber">
            <summary>Field number for the "default_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.DefaultValue">
            <summary>
            For numeric types, contains the original text representation of the value.
            For booleans, "true" or "false".
            For strings, contains the default text contents (not escaped in any way).
            For bytes, contains the C escaped value.  All bytes >= 128 are escaped.
            TODO(kenton):  Base-64 encode?
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasDefaultValue">
            <summary>Gets whether the "default_value" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearDefaultValue">
            <summary>Clears the value of the "default_value" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.OneofIndexFieldNumber">
            <summary>Field number for the "oneof_index" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.OneofIndex">
            <summary>
            If set, gives the index of a oneof in the containing type's oneof_decl
            list.  This field is a member of that oneof.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasOneofIndex">
            <summary>Gets whether the "oneof_index" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearOneofIndex">
            <summary>Clears the value of the "oneof_index" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.JsonNameFieldNumber">
            <summary>Field number for the "json_name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.JsonName">
            <summary>
            JSON name of this field. The value is set by protocol compiler. If the
            user has set a "json_name" option on this field, that option's value
            will be used. Otherwise, it's deduced from the field's name by converting
            it to camelCase.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasJsonName">
            <summary>Gets whether the "json_name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearJsonName">
            <summary>Clears the value of the "json_name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FieldDescriptorProto.Types">
            <summary>Container for nested types declared in the FieldDescriptorProto message type.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Double">
            <summary>
            0 is reserved for errors.
            Order is weird for historical reasons.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Int64">
            <summary>
            Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT64 if
            negative values are likely.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Int32">
            <summary>
            Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT32 if
            negative values are likely.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Group">
            <summary>
            Tag-delimited aggregate.
            Group type is deprecated and not supported in proto3. However, Proto3
            implementations should still be able to parse the group wire format and
            treat group fields as unknown fields.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Message">
            <summary>
            Length-delimited aggregate.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Bytes">
            <summary>
            New in version 2.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Sint32">
            <summary>
            Uses ZigZag encoding.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type.Sint64">
            <summary>
            Uses ZigZag encoding.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldDescriptorProto.Types.Label.Optional">
            <summary>
            0 is reserved for errors
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.OneofDescriptorProto">
            <summary>
            Describes a oneof.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.OneofDescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofDescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.OneofDescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.OneofDescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofDescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.OneofDescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.EnumDescriptorProto">
            <summary>
            Describes an enum type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumDescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumDescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumDescriptorProto.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumDescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumDescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumDescriptorProto.ReservedRangeFieldNumber">
            <summary>Field number for the "reserved_range" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.ReservedRange">
            <summary>
            Range of reserved numeric values. Reserved numeric values may not be used
            by enum values in the same enum declaration. Reserved ranges may not
            overlap.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumDescriptorProto.ReservedNameFieldNumber">
            <summary>Field number for the "reserved_name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.ReservedName">
            <summary>
            Reserved enum value names, which may not be reused. A given name may only
            be reserved once.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.EnumDescriptorProto.Types">
            <summary>Container for nested types declared in the EnumDescriptorProto message type.</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange">
             <summary>
             Range of reserved numeric values. Reserved values may not be used by
             entries in the same enum. Reserved ranges may not overlap.
            
             Note that this is distinct from DescriptorProto.ReservedRange in that it
             is inclusive such that it can appropriately represent the entire int32
             domain.
             </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.StartFieldNumber">
            <summary>Field number for the "start" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.Start">
            <summary>
            Inclusive.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.HasStart">
            <summary>Gets whether the "start" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.ClearStart">
            <summary>Clears the value of the "start" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.EndFieldNumber">
            <summary>Field number for the "end" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.End">
            <summary>
            Inclusive.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.HasEnd">
            <summary>Gets whether the "end" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumDescriptorProto.Types.EnumReservedRange.ClearEnd">
            <summary>Clears the value of the "end" field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.EnumValueDescriptorProto">
            <summary>
            Describes a value within an enum.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumValueDescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueDescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumValueDescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumValueDescriptorProto.NumberFieldNumber">
            <summary>Field number for the "number" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueDescriptorProto.HasNumber">
            <summary>Gets whether the "number" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumValueDescriptorProto.ClearNumber">
            <summary>Clears the value of the "number" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumValueDescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueDescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumValueDescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.ServiceDescriptorProto">
            <summary>
            Describes a service.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.ServiceDescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceDescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.ServiceDescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.ServiceDescriptorProto.MethodFieldNumber">
            <summary>Field number for the "method" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.ServiceDescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceDescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.ServiceDescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.MethodDescriptorProto">
            <summary>
            Describes a method of a service.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodDescriptorProto.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.HasName">
            <summary>Gets whether the "name" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodDescriptorProto.ClearName">
            <summary>Clears the value of the "name" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodDescriptorProto.InputTypeFieldNumber">
            <summary>Field number for the "input_type" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.InputType">
            <summary>
            Input and output type names.  These are resolved in the same way as
            FieldDescriptorProto.type_name, but must refer to a message type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.HasInputType">
            <summary>Gets whether the "input_type" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodDescriptorProto.ClearInputType">
            <summary>Clears the value of the "input_type" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodDescriptorProto.OutputTypeFieldNumber">
            <summary>Field number for the "output_type" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.HasOutputType">
            <summary>Gets whether the "output_type" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodDescriptorProto.ClearOutputType">
            <summary>Clears the value of the "output_type" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodDescriptorProto.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.HasOptions">
            <summary>Gets whether the options field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodDescriptorProto.ClearOptions">
            <summary>Clears the value of the options field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodDescriptorProto.ClientStreamingFieldNumber">
            <summary>Field number for the "client_streaming" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.ClientStreaming">
            <summary>
            Identifies if client streams multiple client messages
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.HasClientStreaming">
            <summary>Gets whether the "client_streaming" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodDescriptorProto.ClearClientStreaming">
            <summary>Clears the value of the "client_streaming" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodDescriptorProto.ServerStreamingFieldNumber">
            <summary>Field number for the "server_streaming" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.ServerStreaming">
            <summary>
            Identifies if server streams multiple server messages
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptorProto.HasServerStreaming">
            <summary>Gets whether the "server_streaming" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodDescriptorProto.ClearServerStreaming">
            <summary>Clears the value of the "server_streaming" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.JavaPackageFieldNumber">
            <summary>Field number for the "java_package" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.JavaPackage">
            <summary>
            Sets the Java package where classes generated from this .proto will be
            placed.  By default, the proto package is used, but this is often
            inappropriate because proto packages do not normally start with backwards
            domain names.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasJavaPackage">
            <summary>Gets whether the "java_package" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearJavaPackage">
            <summary>Clears the value of the "java_package" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.JavaOuterClassnameFieldNumber">
            <summary>Field number for the "java_outer_classname" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.JavaOuterClassname">
            <summary>
            If set, all the classes from the .proto file are wrapped in a single
            outer class with the given name.  This applies to both Proto1
            (equivalent to the old "--one_java_file" option) and Proto2 (where
            a .proto always translates to a single class, but you may want to
            explicitly choose the class name).
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasJavaOuterClassname">
            <summary>Gets whether the "java_outer_classname" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearJavaOuterClassname">
            <summary>Clears the value of the "java_outer_classname" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.JavaMultipleFilesFieldNumber">
            <summary>Field number for the "java_multiple_files" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.JavaMultipleFiles">
            <summary>
            If set true, then the Java code generator will generate a separate .java
            file for each top-level message, enum, and service defined in the .proto
            file.  Thus, these types will *not* be nested inside the outer class
            named by java_outer_classname.  However, the outer class will still be
            generated to contain the file's getDescriptor() method as well as any
            top-level extensions defined in the file.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasJavaMultipleFiles">
            <summary>Gets whether the "java_multiple_files" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearJavaMultipleFiles">
            <summary>Clears the value of the "java_multiple_files" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.JavaGenerateEqualsAndHashFieldNumber">
            <summary>Field number for the "java_generate_equals_and_hash" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.JavaGenerateEqualsAndHash">
            <summary>
            This option does nothing.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasJavaGenerateEqualsAndHash">
            <summary>Gets whether the "java_generate_equals_and_hash" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearJavaGenerateEqualsAndHash">
            <summary>Clears the value of the "java_generate_equals_and_hash" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.JavaStringCheckUtf8FieldNumber">
            <summary>Field number for the "java_string_check_utf8" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.JavaStringCheckUtf8">
            <summary>
            If set true, then the Java2 code generator will generate code that
            throws an exception whenever an attempt is made to assign a non-UTF-8
            byte sequence to a string field.
            Message reflection will do the same.
            However, an extension field still accepts non-UTF-8 byte sequences.
            This option has no effect on when used with the lite runtime.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasJavaStringCheckUtf8">
            <summary>Gets whether the "java_string_check_utf8" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearJavaStringCheckUtf8">
            <summary>Clears the value of the "java_string_check_utf8" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.OptimizeForFieldNumber">
            <summary>Field number for the "optimize_for" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasOptimizeFor">
            <summary>Gets whether the "optimize_for" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearOptimizeFor">
            <summary>Clears the value of the "optimize_for" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.GoPackageFieldNumber">
            <summary>Field number for the "go_package" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.GoPackage">
            <summary>
            Sets the Go package where structs generated from this .proto will be
            placed. If omitted, the Go package will be derived from the following:
              - The basename of the package import path, if provided.
              - Otherwise, the package statement in the .proto file, if present.
              - Otherwise, the basename of the .proto file, without extension.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasGoPackage">
            <summary>Gets whether the "go_package" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearGoPackage">
            <summary>Clears the value of the "go_package" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.CcGenericServicesFieldNumber">
            <summary>Field number for the "cc_generic_services" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.CcGenericServices">
             <summary>
             Should generic services be generated in each language?  "Generic" services
             are not specific to any particular RPC system.  They are generated by the
             main code generators in each language (without additional plugins).
             Generic services were the only kind of service generation supported by
             early versions of google.protobuf.
            
             Generic services are now considered deprecated in favor of using plugins
             that generate code specific to your particular RPC system.  Therefore,
             these default to false.  Old code which depends on generic services should
             explicitly set them to true.
             </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasCcGenericServices">
            <summary>Gets whether the "cc_generic_services" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearCcGenericServices">
            <summary>Clears the value of the "cc_generic_services" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.JavaGenericServicesFieldNumber">
            <summary>Field number for the "java_generic_services" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasJavaGenericServices">
            <summary>Gets whether the "java_generic_services" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearJavaGenericServices">
            <summary>Clears the value of the "java_generic_services" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.PyGenericServicesFieldNumber">
            <summary>Field number for the "py_generic_services" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasPyGenericServices">
            <summary>Gets whether the "py_generic_services" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearPyGenericServices">
            <summary>Clears the value of the "py_generic_services" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.PhpGenericServicesFieldNumber">
            <summary>Field number for the "php_generic_services" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasPhpGenericServices">
            <summary>Gets whether the "php_generic_services" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearPhpGenericServices">
            <summary>Clears the value of the "php_generic_services" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.DeprecatedFieldNumber">
            <summary>Field number for the "deprecated" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.Deprecated">
            <summary>
            Is this file deprecated?
            Depending on the target platform, this can emit Deprecated annotations
            for everything in the file, or it will be completely ignored; in the very
            least, this is a formalization for deprecating files.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasDeprecated">
            <summary>Gets whether the "deprecated" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearDeprecated">
            <summary>Clears the value of the "deprecated" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.CcEnableArenasFieldNumber">
            <summary>Field number for the "cc_enable_arenas" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.CcEnableArenas">
            <summary>
            Enables the use of arenas for the proto messages in this file. This applies
            only to generated classes for C++.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasCcEnableArenas">
            <summary>Gets whether the "cc_enable_arenas" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearCcEnableArenas">
            <summary>Clears the value of the "cc_enable_arenas" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.ObjcClassPrefixFieldNumber">
            <summary>Field number for the "objc_class_prefix" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.ObjcClassPrefix">
            <summary>
            Sets the objective c class prefix which is prepended to all objective c
            generated classes from this .proto. There is no default.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasObjcClassPrefix">
            <summary>Gets whether the "objc_class_prefix" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearObjcClassPrefix">
            <summary>Clears the value of the "objc_class_prefix" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.CsharpNamespaceFieldNumber">
            <summary>Field number for the "csharp_namespace" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.CsharpNamespace">
            <summary>
            Namespace for generated classes; defaults to the package.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasCsharpNamespace">
            <summary>Gets whether the "csharp_namespace" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearCsharpNamespace">
            <summary>Clears the value of the "csharp_namespace" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.SwiftPrefixFieldNumber">
            <summary>Field number for the "swift_prefix" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.SwiftPrefix">
            <summary>
            By default Swift generators will take the proto package and CamelCase it
            replacing '.' with underscore and use that to prefix the types/symbols
            defined. When this options is provided, they will use this value instead
            to prefix the types/symbols defined.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasSwiftPrefix">
            <summary>Gets whether the "swift_prefix" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearSwiftPrefix">
            <summary>Clears the value of the "swift_prefix" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.PhpClassPrefixFieldNumber">
            <summary>Field number for the "php_class_prefix" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.PhpClassPrefix">
            <summary>
            Sets the php class prefix which is prepended to all php generated classes
            from this .proto. Default is empty.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasPhpClassPrefix">
            <summary>Gets whether the "php_class_prefix" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearPhpClassPrefix">
            <summary>Clears the value of the "php_class_prefix" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.PhpNamespaceFieldNumber">
            <summary>Field number for the "php_namespace" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.PhpNamespace">
            <summary>
            Use this option to change the namespace of php generated classes. Default
            is empty. When this option is empty, the package name will be used for
            determining the namespace.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasPhpNamespace">
            <summary>Gets whether the "php_namespace" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearPhpNamespace">
            <summary>Clears the value of the "php_namespace" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.PhpMetadataNamespaceFieldNumber">
            <summary>Field number for the "php_metadata_namespace" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.PhpMetadataNamespace">
            <summary>
            Use this option to change the namespace of php generated metadata classes.
            Default is empty. When this option is empty, the proto file name will be used
            for determining the namespace.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasPhpMetadataNamespace">
            <summary>Gets whether the "php_metadata_namespace" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearPhpMetadataNamespace">
            <summary>Clears the value of the "php_metadata_namespace" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.RubyPackageFieldNumber">
            <summary>Field number for the "ruby_package" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.RubyPackage">
            <summary>
            Use this option to change the package of ruby generated classes. Default
            is empty. When this option is not set, the package name will be used for
            determining the ruby package.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.HasRubyPackage">
            <summary>Gets whether the "ruby_package" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileOptions.ClearRubyPackage">
            <summary>Clears the value of the "ruby_package" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here.
            See the documentation for the "Options" section above.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FileOptions.Types">
            <summary>Container for nested types declared in the FileOptions message type.</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FileOptions.Types.OptimizeMode">
            <summary>
            Generated classes can be optimized for speed or code size.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.Types.OptimizeMode.Speed">
            <summary>
            Generate complete code for parsing, serialization,
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.Types.OptimizeMode.CodeSize">
            <summary>
            etc.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FileOptions.Types.OptimizeMode.LiteRuntime">
            <summary>
            Generate code using MessageLite and the lite runtime.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MessageOptions.MessageSetWireFormatFieldNumber">
            <summary>Field number for the "message_set_wire_format" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.MessageSetWireFormat">
             <summary>
             Set true to use the old proto1 MessageSet wire format for extensions.
             This is provided for backwards-compatibility with the MessageSet wire
             format.  You should not use this for any other reason:  It's less
             efficient, has fewer features, and is more complicated.
            
             The message must be defined exactly as follows:
               message Foo {
                 option message_set_wire_format = true;
                 extensions 4 to max;
               }
             Note that the message cannot have any defined fields; MessageSets only
             have extensions.
            
             All extensions of your type must be singular messages; e.g. they cannot
             be int32s, enums, or repeated messages.
            
             Because this is an option, the above two restrictions are not enforced by
             the protocol compiler.
             </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.HasMessageSetWireFormat">
            <summary>Gets whether the "message_set_wire_format" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageOptions.ClearMessageSetWireFormat">
            <summary>Clears the value of the "message_set_wire_format" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MessageOptions.NoStandardDescriptorAccessorFieldNumber">
            <summary>Field number for the "no_standard_descriptor_accessor" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.NoStandardDescriptorAccessor">
            <summary>
            Disables the generation of the standard "descriptor()" accessor, which can
            conflict with a field of the same name.  This is meant to make migration
            from proto1 easier; new code should avoid fields named "descriptor".
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.HasNoStandardDescriptorAccessor">
            <summary>Gets whether the "no_standard_descriptor_accessor" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageOptions.ClearNoStandardDescriptorAccessor">
            <summary>Clears the value of the "no_standard_descriptor_accessor" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MessageOptions.DeprecatedFieldNumber">
            <summary>Field number for the "deprecated" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.Deprecated">
            <summary>
            Is this message deprecated?
            Depending on the target platform, this can emit Deprecated annotations
            for the message, or it will be completely ignored; in the very least,
            this is a formalization for deprecating messages.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.HasDeprecated">
            <summary>Gets whether the "deprecated" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageOptions.ClearDeprecated">
            <summary>Clears the value of the "deprecated" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MessageOptions.MapEntryFieldNumber">
            <summary>Field number for the "map_entry" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.MapEntry">
             <summary>
             Whether the message is an automatically generated map entry type for the
             maps field.
            
             For maps fields:
                 map&lt;KeyType, ValueType> map_field = 1;
             The parsed descriptor looks like:
                 message MapFieldEntry {
                     option map_entry = true;
                     optional KeyType key = 1;
                     optional ValueType value = 2;
                 }
                 repeated MapFieldEntry map_field = 1;
            
             Implementations may choose not to generate the map_entry=true message, but
             use a native map in the target language to hold the keys and values.
             The reflection APIs in such implementions still need to work as
             if the field is a repeated message field.
            
             NOTE: Do not set the option in .proto files. Always use the maps syntax
             instead. The option should only be implicitly set by the proto compiler
             parser.
             </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.HasMapEntry">
            <summary>Gets whether the "map_entry" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageOptions.ClearMapEntry">
            <summary>Clears the value of the "map_entry" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MessageOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.CtypeFieldNumber">
            <summary>Field number for the "ctype" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.Ctype">
            <summary>
            The ctype option instructs the C++ code generator to use a different
            representation of the field than it normally would.  See the specific
            options below.  This option is not yet implemented in the open source
            release -- sorry, we'll try to include it in a future version!
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.HasCtype">
            <summary>Gets whether the "ctype" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldOptions.ClearCtype">
            <summary>Clears the value of the "ctype" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.PackedFieldNumber">
            <summary>Field number for the "packed" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.Packed">
            <summary>
            The packed option can be enabled for repeated primitive fields to enable
            a more efficient representation on the wire. Rather than repeatedly
            writing the tag and type for each element, the entire array is encoded as
            a single length-delimited blob. In proto3, only explicit setting it to
            false will avoid using packed encoding.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.HasPacked">
            <summary>Gets whether the "packed" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldOptions.ClearPacked">
            <summary>Clears the value of the "packed" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.JstypeFieldNumber">
            <summary>Field number for the "jstype" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.Jstype">
             <summary>
             The jstype option determines the JavaScript type used for values of the
             field.  The option is permitted only for 64 bit integral and fixed types
             (int64, uint64, sint64, fixed64, sfixed64).  A field with jstype JS_STRING
             is represented as JavaScript string, which avoids loss of precision that
             can happen when a large value is converted to a floating point JavaScript.
             Specifying JS_NUMBER for the jstype causes the generated JavaScript code to
             use the JavaScript "number" type.  The behavior of the default option
             JS_NORMAL is implementation dependent.
            
             This option is an enum to permit additional types to be added, e.g.
             goog.math.Integer.
             </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.HasJstype">
            <summary>Gets whether the "jstype" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldOptions.ClearJstype">
            <summary>Clears the value of the "jstype" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.LazyFieldNumber">
            <summary>Field number for the "lazy" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.Lazy">
             <summary>
             Should this field be parsed lazily?  Lazy applies only to message-type
             fields.  It means that when the outer message is initially parsed, the
             inner message's contents will not be parsed but instead stored in encoded
             form.  The inner message will actually be parsed when it is first accessed.
            
             This is only a hint.  Implementations are free to choose whether to use
             eager or lazy parsing regardless of the value of this option.  However,
             setting this option true suggests that the protocol author believes that
             using lazy parsing on this field is worth the additional bookkeeping
             overhead typically needed to implement it.
            
             This option does not affect the public interface of any generated code;
             all method signatures remain the same.  Furthermore, thread-safety of the
             interface is not affected by this option; const methods remain safe to
             call from multiple threads concurrently, while non-const methods continue
             to require exclusive access.
            
             Note that implementations may choose not to check required fields within
             a lazy sub-message.  That is, calling IsInitialized() on the outer message
             may return true even if the inner message has missing required fields.
             This is necessary because otherwise the inner message would have to be
             parsed in order to perform the check, defeating the purpose of lazy
             parsing.  An implementation which chooses not to check required fields
             must be consistent about it.  That is, for any particular sub-message, the
             implementation must either *always* check its required fields, or *never*
             check its required fields, regardless of whether or not the message has
             been parsed.
             </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.HasLazy">
            <summary>Gets whether the "lazy" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldOptions.ClearLazy">
            <summary>Clears the value of the "lazy" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.DeprecatedFieldNumber">
            <summary>Field number for the "deprecated" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.Deprecated">
            <summary>
            Is this field deprecated?
            Depending on the target platform, this can emit Deprecated annotations
            for accessors, or it will be completely ignored; in the very least, this
            is a formalization for deprecating fields.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.HasDeprecated">
            <summary>Gets whether the "deprecated" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldOptions.ClearDeprecated">
            <summary>Clears the value of the "deprecated" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.WeakFieldNumber">
            <summary>Field number for the "weak" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.Weak">
            <summary>
            For Google-internal migration only. Do not use.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.HasWeak">
            <summary>Gets whether the "weak" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldOptions.ClearWeak">
            <summary>Clears the value of the "weak" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FieldOptions.Types">
            <summary>Container for nested types declared in the FieldOptions message type.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.Types.CType.String">
            <summary>
            Default mode.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.Types.JSType.JsNormal">
            <summary>
            Use the default type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.Types.JSType.JsString">
            <summary>
            Use JavaScript strings.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldOptions.Types.JSType.JsNumber">
            <summary>
            Use JavaScript numbers.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.OneofOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumOptions.AllowAliasFieldNumber">
            <summary>Field number for the "allow_alias" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumOptions.AllowAlias">
            <summary>
            Set this option to true to allow mapping different tag names to the same
            value.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumOptions.HasAllowAlias">
            <summary>Gets whether the "allow_alias" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumOptions.ClearAllowAlias">
            <summary>Clears the value of the "allow_alias" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumOptions.DeprecatedFieldNumber">
            <summary>Field number for the "deprecated" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumOptions.Deprecated">
            <summary>
            Is this enum deprecated?
            Depending on the target platform, this can emit Deprecated annotations
            for the enum, or it will be completely ignored; in the very least, this
            is a formalization for deprecating enums.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumOptions.HasDeprecated">
            <summary>Gets whether the "deprecated" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumOptions.ClearDeprecated">
            <summary>Clears the value of the "deprecated" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumValueOptions.DeprecatedFieldNumber">
            <summary>Field number for the "deprecated" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueOptions.Deprecated">
            <summary>
            Is this enum value deprecated?
            Depending on the target platform, this can emit Deprecated annotations
            for the enum value, or it will be completely ignored; in the very least,
            this is a formalization for deprecating enum values.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueOptions.HasDeprecated">
            <summary>Gets whether the "deprecated" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumValueOptions.ClearDeprecated">
            <summary>Clears the value of the "deprecated" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.EnumValueOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.ServiceOptions.DeprecatedFieldNumber">
            <summary>Field number for the "deprecated" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceOptions.Deprecated">
            <summary>
            Is this service deprecated?
            Depending on the target platform, this can emit Deprecated annotations
            for the service, or it will be completely ignored; in the very least,
            this is a formalization for deprecating services.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceOptions.HasDeprecated">
            <summary>Gets whether the "deprecated" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.ServiceOptions.ClearDeprecated">
            <summary>Clears the value of the "deprecated" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.ServiceOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodOptions.DeprecatedFieldNumber">
            <summary>Field number for the "deprecated" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodOptions.Deprecated">
            <summary>
            Is this method deprecated?
            Depending on the target platform, this can emit Deprecated annotations
            for the method, or it will be completely ignored; in the very least,
            this is a formalization for deprecating methods.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodOptions.HasDeprecated">
            <summary>Gets whether the "deprecated" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodOptions.ClearDeprecated">
            <summary>Clears the value of the "deprecated" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodOptions.IdempotencyLevelFieldNumber">
            <summary>Field number for the "idempotency_level" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodOptions.HasIdempotencyLevel">
            <summary>Gets whether the "idempotency_level" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MethodOptions.ClearIdempotencyLevel">
            <summary>Clears the value of the "idempotency_level" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodOptions.UninterpretedOptionFieldNumber">
            <summary>Field number for the "uninterpreted_option" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodOptions.UninterpretedOption">
            <summary>
            The parser stores options it doesn't recognize here. See above.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.MethodOptions.Types">
            <summary>Container for nested types declared in the MethodOptions message type.</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.MethodOptions.Types.IdempotencyLevel">
            <summary>
            Is this method side-effect-free (or safe in HTTP parlance), or idempotent,
            or neither? HTTP based RPC implementation may choose GET verb for safe
            methods, and PUT verb for idempotent methods instead of the default POST.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodOptions.Types.IdempotencyLevel.NoSideEffects">
            <summary>
            implies idempotent
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.MethodOptions.Types.IdempotencyLevel.Idempotent">
            <summary>
            idempotent, but may have side effects
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.UninterpretedOption">
            <summary>
            A message representing a option the parser does not recognize. This only
            appears in options protos created by the compiler::Parser class.
            DescriptorPool resolves these when building Descriptor objects. Therefore,
            options protos in descriptor objects (e.g. returned by Descriptor::options(),
            or produced by Descriptor::CopyTo()) will never have UninterpretedOptions
            in them.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.IdentifierValueFieldNumber">
            <summary>Field number for the "identifier_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.IdentifierValue">
            <summary>
            The value of the uninterpreted option, in whatever type the tokenizer
            identified it as during parsing. Exactly one of these should be set.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.HasIdentifierValue">
            <summary>Gets whether the "identifier_value" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.ClearIdentifierValue">
            <summary>Clears the value of the "identifier_value" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.PositiveIntValueFieldNumber">
            <summary>Field number for the "positive_int_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.HasPositiveIntValue">
            <summary>Gets whether the "positive_int_value" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.ClearPositiveIntValue">
            <summary>Clears the value of the "positive_int_value" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.NegativeIntValueFieldNumber">
            <summary>Field number for the "negative_int_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.HasNegativeIntValue">
            <summary>Gets whether the "negative_int_value" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.ClearNegativeIntValue">
            <summary>Clears the value of the "negative_int_value" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.DoubleValueFieldNumber">
            <summary>Field number for the "double_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.HasDoubleValue">
            <summary>Gets whether the "double_value" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.ClearDoubleValue">
            <summary>Clears the value of the "double_value" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.StringValueFieldNumber">
            <summary>Field number for the "string_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.HasStringValue">
            <summary>Gets whether the "string_value" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.ClearStringValue">
            <summary>Clears the value of the "string_value" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.AggregateValueFieldNumber">
            <summary>Field number for the "aggregate_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.HasAggregateValue">
            <summary>Gets whether the "aggregate_value" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.ClearAggregateValue">
            <summary>Clears the value of the "aggregate_value" field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.UninterpretedOption.Types">
            <summary>Container for nested types declared in the UninterpretedOption message type.</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.UninterpretedOption.Types.NamePart">
            <summary>
            The name of the uninterpreted option.  Each string represents a segment in
            a dot-separated name.  is_extension is true iff a segment represents an
            extension (denoted with parentheses in options specs in .proto files).
            E.g.,{ ["foo", false], ["bar.baz", true], ["qux", false] } represents
            "foo.(bar.baz).qux".
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.Types.NamePart.NamePart_FieldNumber">
            <summary>Field number for the "name_part" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.Types.NamePart.HasNamePart_">
            <summary>Gets whether the "name_part" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.Types.NamePart.ClearNamePart_">
            <summary>Clears the value of the "name_part" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.UninterpretedOption.Types.NamePart.IsExtensionFieldNumber">
            <summary>Field number for the "is_extension" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.UninterpretedOption.Types.NamePart.HasIsExtension">
            <summary>Gets whether the "is_extension" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.UninterpretedOption.Types.NamePart.ClearIsExtension">
            <summary>Clears the value of the "is_extension" field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.SourceCodeInfo">
            <summary>
            Encapsulates information about the original source file from which a
            FileDescriptorProto was generated.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.SourceCodeInfo.LocationFieldNumber">
            <summary>Field number for the "location" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.SourceCodeInfo.Location">
             <summary>
             A Location identifies a piece of source code in a .proto file which
             corresponds to a particular definition.  This information is intended
             to be useful to IDEs, code indexers, documentation generators, and similar
             tools.
            
             For example, say we have a file like:
               message Foo {
                 optional string foo = 1;
               }
             Let's look at just the field definition:
               optional string foo = 1;
               ^       ^^     ^^  ^  ^^^
               a       bc     de  f  ghi
             We have the following locations:
               span   path               represents
               [a,i)  [ 4, 0, 2, 0 ]     The whole field definition.
               [a,b)  [ 4, 0, 2, 0, 4 ]  The label (optional).
               [c,d)  [ 4, 0, 2, 0, 5 ]  The type (string).
               [e,f)  [ 4, 0, 2, 0, 1 ]  The name (foo).
               [g,h)  [ 4, 0, 2, 0, 3 ]  The number (1).
            
             Notes:
             - A location may refer to a repeated field itself (i.e. not to any
               particular index within it).  This is used whenever a set of elements are
               logically enclosed in a single code segment.  For example, an entire
               extend block (possibly containing multiple extension definitions) will
               have an outer location whose path refers to the "extensions" repeated
               field without an index.
             - Multiple locations may have the same path.  This happens when a single
               logical declaration is spread out across multiple places.  The most
               obvious example is the "extend" block again -- there may be multiple
               extend blocks in the same scope, each of which will have the same path.
             - A location's span is not always a subset of its parent's span.  For
               example, the "extendee" of an extension declaration appears at the
               beginning of the "extend" block and is shared by all extensions within
               the block.
             - Just because a location's span is a subset of some other location's span
               does not mean that it is a descendent.  For example, a "group" defines
               both a type and a field in a single declaration.  Thus, the locations
               corresponding to the type and field and their components will overlap.
             - Code which tries to interpret locations should probably be designed to
               ignore those that it doesn't understand, as more types of locations could
               be recorded in the future.
             </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.SourceCodeInfo.Types">
            <summary>Container for nested types declared in the SourceCodeInfo message type.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.PathFieldNumber">
            <summary>Field number for the "path" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.Path">
             <summary>
             Identifies which part of the FileDescriptorProto was defined at this
             location.
            
             Each element is a field number or an index.  They form a path from
             the root FileDescriptorProto to the place where the definition.  For
             example, this path:
               [ 4, 3, 2, 7, 1 ]
             refers to:
               file.message_type(3)  // 4, 3
                   .field(7)         // 2, 7
                   .name()           // 1
             This is because FileDescriptorProto.message_type has field number 4:
               repeated DescriptorProto message_type = 4;
             and DescriptorProto.field has field number 2:
               repeated FieldDescriptorProto field = 2;
             and FieldDescriptorProto.name has field number 1:
               optional string name = 1;
            
             Thus, the above path gives the location of a field name.  If we removed
             the last element:
               [ 4, 3, 2, 7 ]
             this path refers to the whole field declaration (from the beginning
             of the label to the terminating semicolon).
             </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.SpanFieldNumber">
            <summary>Field number for the "span" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.Span">
            <summary>
            Always has exactly three or four elements: start line, start column,
            end line (optional, otherwise assumed same as start line), end column.
            These are packed into a single field for efficiency.  Note that line
            and column numbers are zero-based -- typically you will want to add
            1 to each before displaying to a user.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.LeadingCommentsFieldNumber">
            <summary>Field number for the "leading_comments" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.LeadingComments">
             <summary>
             If this SourceCodeInfo represents a complete declaration, these are any
             comments appearing before and after the declaration which appear to be
             attached to the declaration.
            
             A series of line comments appearing on consecutive lines, with no other
             tokens appearing on those lines, will be treated as a single comment.
            
             leading_detached_comments will keep paragraphs of comments that appear
             before (but not connected to) the current element. Each paragraph,
             separated by empty lines, will be one comment element in the repeated
             field.
            
             Only the comment content is provided; comment markers (e.g. //) are
             stripped out.  For block comments, leading whitespace and an asterisk
             will be stripped from the beginning of each line other than the first.
             Newlines are included in the output.
            
             Examples:
            
               optional int32 foo = 1;  // Comment attached to foo.
               // Comment attached to bar.
               optional int32 bar = 2;
            
               optional string baz = 3;
               // Comment attached to baz.
               // Another line attached to baz.
            
               // Comment attached to qux.
               //
               // Another line attached to qux.
               optional double qux = 4;
            
               // Detached comment for corge. This is not leading or trailing comments
               // to qux or corge because there are blank lines separating it from
               // both.
            
               // Detached comment for corge paragraph 2.
            
               optional string corge = 5;
               /* Block comment attached
                * to corge.  Leading asterisks
                * will be removed. */
               /* Block comment attached to
                * grault. */
               optional int32 grault = 6;
            
               // ignored detached comments.
             </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.HasLeadingComments">
            <summary>Gets whether the "leading_comments" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.ClearLeadingComments">
            <summary>Clears the value of the "leading_comments" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.TrailingCommentsFieldNumber">
            <summary>Field number for the "trailing_comments" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.HasTrailingComments">
            <summary>Gets whether the "trailing_comments" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.ClearTrailingComments">
            <summary>Clears the value of the "trailing_comments" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.SourceCodeInfo.Types.Location.LeadingDetachedCommentsFieldNumber">
            <summary>Field number for the "leading_detached_comments" field.</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.GeneratedCodeInfo">
            <summary>
            Describes the relationship between generated code and its original source
            file. A GeneratedCodeInfo message is associated with only one generated
            source file, but may contain references to different source .proto files.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.GeneratedCodeInfo.AnnotationFieldNumber">
            <summary>Field number for the "annotation" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Annotation">
            <summary>
            An Annotation connects some span of text in generated code to an element
            of its generating .proto file.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.GeneratedCodeInfo.Types">
            <summary>Container for nested types declared in the GeneratedCodeInfo message type.</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.PathFieldNumber">
            <summary>Field number for the "path" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.Path">
            <summary>
            Identifies the element in the original source .proto file. This field
            is formatted the same as SourceCodeInfo.Location.path.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.SourceFileFieldNumber">
            <summary>Field number for the "source_file" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.SourceFile">
            <summary>
            Identifies the filesystem path to the original source .proto.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.HasSourceFile">
            <summary>Gets whether the "source_file" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.ClearSourceFile">
            <summary>Clears the value of the "source_file" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.BeginFieldNumber">
            <summary>Field number for the "begin" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.Begin">
            <summary>
            Identifies the starting offset in bytes in the generated code
            that relates to the identified object.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.HasBegin">
            <summary>Gets whether the "begin" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.ClearBegin">
            <summary>Clears the value of the "begin" field</summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.EndFieldNumber">
            <summary>Field number for the "end" field.</summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.End">
            <summary>
            Identifies the ending offset in bytes in the generated code that
            relates to the identified offset. The end offset should be one past
            the last relevant byte (so the length of the text = end - begin).
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.HasEnd">
            <summary>Gets whether the "end" field is set</summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.GeneratedCodeInfo.Types.Annotation.ClearEnd">
            <summary>Clears the value of the "end" field</summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorBase">
            <summary>
            Base class for nearly all descriptors, providing common functionality.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorBase.Index">
            <value>
            The index of this descriptor within its parent descriptor. 
            </value>
            <remarks>
            This returns the index of this descriptor within its parent, for
            this descriptor's type. (There can be duplicate values for different
            types, e.g. one enum type with index 0 and one message type with index 0.)
            </remarks>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorBase.Name">
            <summary>
            Returns the name of the entity (field, message etc) being described.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorBase.FullName">
            <summary>
            The fully qualified name of the descriptor's target.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorBase.File">
            <value>
            The file this descriptor was declared in.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorBase.Declaration">
            <summary>
            The declaration information about the descriptor, or null if no declaration information
            is available for this descriptor.
            </summary>
            <remarks>
            This information is typically only available for dynamically loaded descriptors,
            for example within a protoc plugin where the full descriptors, including source info,
            are passed to the code by protoc.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorBase.GetNestedDescriptorListForField(System.Int32)">
            <summary>
            Retrieves the list of nested descriptors corresponding to the given field number, if any.
            If the field is unknown or not a nested descriptor list, return null to terminate the search.
            The default implementation returns null.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorDeclaration">
            <summary>
            Provides additional information about the declaration of a descriptor,
            such as source location and comments.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.Descriptor">
            <summary>
            The descriptor this declaration relates to.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.StartLine">
            <summary>
            The start line of the declaration within the source file. This value is 1-based.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.StartColumn">
            <summary>
            The start column of the declaration within the source file. This value is 1-based.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.EndLine">
            <summary>
            // The end line of the declaration within the source file. This value is 1-based.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.EndColumn">
            <summary>
            The end column of the declaration within the source file. This value is 1-based, and
            exclusive. (The final character of the declaration is on the column before this value.)
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.LeadingComments">
            <summary>
            Comments appearing before the declaration. Never null, but may be empty. Multi-line comments
            are represented as a newline-separated string. Leading whitespace and the comment marker ("//")
            are removed from each line.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.TrailingComments">
            <summary>
            Comments appearing after the declaration. Never null, but may be empty. Multi-line comments
            are represented as a newline-separated string. Leading whitespace and the comment marker ("//")
            are removed from each line.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorDeclaration.LeadingDetachedComments">
            <summary>
            Comments appearing before the declaration, but separated from it by blank
            lines. Each string represents a newline-separated paragraph of comments.
            Leading whitespace and the comment marker ("//") are removed from each line.
            The list is never null, but may be empty. Likewise each element is never null, but may be empty.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorPool">
            <summary>
            Contains lookup tables containing all the descriptors defined in a particular file.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.FindSymbol``1(System.String)">
            <summary>
            Finds a symbol of the given name within the pool.
            </summary>
            <typeparam name="T">The type of symbol to look for</typeparam>
            <param name="fullName">Fully-qualified name to look up</param>
            <returns>The symbol with the given name and type,
            or null if the symbol doesn't exist or has the wrong type</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.AddPackage(System.String,Google.Protobuf.Reflection.FileDescriptor)">
            <summary>
            Adds a package to the symbol tables. If a package by the same name
            already exists, that is fine, but if some other kind of symbol
            exists under the same name, an exception is thrown. If the package
            has multiple components, this also adds the parent package(s).
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.AddSymbol(Google.Protobuf.Reflection.IDescriptor)">
            <summary>
            Adds a symbol to the symbol table.
            </summary>
            <exception cref="T:Google.Protobuf.Reflection.DescriptorValidationException">The symbol already existed
            in the symbol table.</exception>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.ValidateSymbolName(Google.Protobuf.Reflection.IDescriptor)">
            <summary>
            Verifies that the descriptor's name is valid (i.e. it contains
            only letters, digits and underscores, and does not start with a digit).
            </summary>
            <param name="descriptor"></param>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.FindFieldByNumber(Google.Protobuf.Reflection.MessageDescriptor,System.Int32)">
            <summary>
            Returns the field with the given number in the given descriptor,
            or null if it can't be found.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.AddFieldByNumber(Google.Protobuf.Reflection.FieldDescriptor)">
            <summary>
            Adds a field to the fieldsByNumber table.
            </summary>
            <exception cref="T:Google.Protobuf.Reflection.DescriptorValidationException">A field with the same
            containing type and number already exists.</exception>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.AddEnumValueByNumber(Google.Protobuf.Reflection.EnumValueDescriptor)">
            <summary>
            Adds an enum value to the enumValuesByNumber table. If an enum value
            with the same type and number already exists, this method does nothing.
            (This is allowed; the first value defined with the number takes precedence.)
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorPool.LookupSymbol(System.String,Google.Protobuf.Reflection.IDescriptor)">
            <summary>
            Looks up a descriptor by name, relative to some other descriptor.
            The name may be fully-qualified (with a leading '.'), partially-qualified,
            or unqualified. C++-like name lookup semantics are used to search for the
            matching descriptor.
            </summary>
            <remarks>
            This isn't heavily optimized, but it's only used during cross linking anyway.
            If it starts being used more widely, we should look at performance more carefully.
            </remarks>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorPool.DescriptorIntPair">
            <summary>
            Struct used to hold the keys for the fieldByNumber table.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorUtil">
            <summary>
            Internal class containing utility methods when working with descriptors.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorUtil.IndexedConverter`2">
            <summary>
            Equivalent to Func[TInput, int, TOutput] but usable in .NET 2.0. Only used to convert
            arrays.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.DescriptorUtil.ConvertAndMakeReadOnly``2(System.Collections.Generic.IList{``0},Google.Protobuf.Reflection.DescriptorUtil.IndexedConverter{``0,``1})">
            <summary>
            Converts the given array into a read-only list, applying the specified conversion to
            each input element.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.DescriptorValidationException">
            <summary>
            Thrown when building descriptors fails because the source DescriptorProtos
            are not valid.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorValidationException.ProblemSymbolName">
            <value>
            The full name of the descriptor where the error occurred.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.DescriptorValidationException.Description">
            <value>
            A human-readable description of the error. (The Message property
            is made up of the descriptor's name and this description.)
            </value>
        </member>
        <member name="T:Google.Protobuf.Reflection.EnumDescriptor">
            <summary>
            Descriptor for an enum type in a .proto file.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptor.Name">
            <summary>
            The brief name of the descriptor's target.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptor.ClrType">
            <summary>
            The CLR type for this enum. For generated code, this will be a CLR enum type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptor.ContainingType">
            <value>
            If this is a nested type, get the outer descriptor, otherwise null.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptor.Values">
            <value>
            An unmodifiable list of defined value descriptors for this enum.
            </value>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumDescriptor.FindValueByNumber(System.Int32)">
            <summary>
            Finds an enum value by number. If multiple enum values have the
            same number, this returns the first defined value with that number.
            If there is no value for the given number, this returns <c>null</c>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.EnumDescriptor.FindValueByName(System.String)">
            <summary>
            Finds an enum value by name.
            </summary>
            <param name="name">The unqualified name of the value (e.g. "FOO").</param>
            <returns>The value's descriptor, or null if not found.</returns>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this enum.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.EnumValueDescriptor">
            <summary>
            Descriptor for a single enum value within an enum in a .proto file.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueDescriptor.Name">
            <summary>
            Returns the name of the enum value described by this object.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueDescriptor.Number">
            <summary>
            Returns the number associated with this enum value.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueDescriptor.EnumDescriptor">
            <summary>
            Returns the enum descriptor that this value is part of.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.EnumValueDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this enum value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FieldAccessorBase">
            <summary>
            Base class for field accessors.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FieldDescriptor">
            <summary>
            Descriptor for a field or extension within a message in a .proto file.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.ContainingType">
            <summary>
            Get the field's containing message type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.ContainingOneof">
            <summary>
            Returns the oneof containing this field, or <c>null</c> if it is not part of a oneof.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.JsonName">
            <summary>
            The effective JSON name for this field. This is usually the lower-camel-cased form of the field name,
            but can be overridden using the <c>json_name</c> option in the .proto file.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.Name">
            <summary>
            The brief name of the descriptor's target.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.Accessor">
            <summary>
            Returns the accessor for this field.
            </summary>
            <remarks>
            <para>
            While a <see cref="T:Google.Protobuf.Reflection.FieldDescriptor"/> describes the field, it does not provide
            any way of obtaining or changing the value of the field within a specific message;
            that is the responsibility of the accessor.
            </para>
            <para>
            In descriptors for generated code, the value returned by this property will be non-null for all
            regular fields. However, if a message containing a map field is introspected, the list of nested messages will include
            an auto-generated nested key/value pair message for the field. This is not represented in any
            generated type, and the value of the map field itself is represented by a dictionary in the
            reflection API. There are never instances of those "hidden" messages, so no accessor is provided
            and this property will return null.
            </para>
            <para>
            In dynamically loaded descriptors, the value returned by this property will current be null;
            if and when dynamic messages are supported, it will return a suitable accessor to work with
            them.
            </para>
            </remarks>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptor.GetFieldTypeFromProtoType(Google.Protobuf.Reflection.FieldDescriptorProto.Types.Type)">
            <summary>
            Maps a field type as included in the .proto file to a FieldType.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.IsRepeated">
            <summary>
            Returns <c>true</c> if this field is a repeated field; <c>false</c> otherwise.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.IsRequired">
            <summary>
            Returns <c>true</c> if this field is a required field; <c>false</c> otherwise.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.IsMap">
            <summary>
            Returns <c>true</c> if this field is a map field; <c>false</c> otherwise.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.IsPacked">
            <summary>
            Returns <c>true</c> if this field is a packed, repeated field; <c>false</c> otherwise.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.FieldType">
            <summary>
            Returns the type of the field.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.FieldNumber">
            <summary>
            Returns the field number declared in the proto file.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptor.CompareTo(Google.Protobuf.Reflection.FieldDescriptor)">
            <summary>
            Compares this descriptor with another one, ordering in "canonical" order
            which simply means ascending order by field number. <paramref name="other"/>
            must be a field of the same type, i.e. the <see cref="P:Google.Protobuf.Reflection.FieldDescriptor.ContainingType"/> of
            both fields must be the same.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.EnumType">
            <summary>
            For enum fields, returns the field's type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.MessageType">
            <summary>
            For embedded message and group fields, returns the field's type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FieldDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this field.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FieldDescriptor.CrossLink">
            <summary>
            Look up and cross-link all field types etc.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FieldType">
            <summary>
            Enumeration of all the possible field types.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Double">
            <summary>
            The <c>double</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Float">
            <summary>
            The <c>float</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Int64">
            <summary>
            The <c>int64</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.UInt64">
            <summary>
            The <c>uint64</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Int32">
            <summary>
            The <c>int32</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Fixed64">
            <summary>
            The <c>fixed64</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Fixed32">
            <summary>
            The <c>fixed32</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Bool">
            <summary>
            The <c>bool</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.String">
            <summary>
            The <c>string</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Group">
            <summary>
            The field type used for groups.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Message">
            <summary>
            The field type used for message fields.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Bytes">
            <summary>
            The <c>bytes</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.UInt32">
            <summary>
            The <c>uint32</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.SFixed32">
            <summary>
            The <c>sfixed32</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.SFixed64">
            <summary>
            The <c>sfixed64</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.SInt32">
            <summary>
            The <c>sint32</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.SInt64">
            <summary>
            The <c>sint64</c> field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.FieldType.Enum">
            <summary>
            The field type used for enum fields.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.FileDescriptor">
            <summary>
            Describes a .proto file, including everything defined within.
            IDescriptor is implemented such that the File property returns this descriptor,
            and the FullName is the same as the Name.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.ComputeFullName(Google.Protobuf.Reflection.MessageDescriptor,System.String)">
            <summary>
            Computes the full name of a descriptor within this file, with an optional parent message.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.DeterminePublicDependencies(Google.Protobuf.Reflection.FileDescriptor,Google.Protobuf.Reflection.FileDescriptorProto,System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.FileDescriptor},System.Boolean)">
            <summary>
            Extracts public dependencies from direct dependencies. This is a static method despite its
            first parameter, as the value we're in the middle of constructing is only used for exceptions.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.Proto">
            <value>
            The descriptor in its protocol message representation.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.Name">
            <value>
            The file name.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.Package">
            <summary>
            The package as declared in the .proto file. This may or may not
            be equivalent to the .NET namespace of the generated classes.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.MessageTypes">
            <value>
            Unmodifiable list of top-level message types declared in this file.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.EnumTypes">
            <value>
            Unmodifiable list of top-level enum types declared in this file.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.Services">
            <value>
            Unmodifiable list of top-level services declared in this file.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.Dependencies">
            <value>
            Unmodifiable list of this file's dependencies (imports).
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.PublicDependencies">
            <value>
            Unmodifiable list of this file's public dependencies (public imports).
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.SerializedData">
            <value>
            The original serialized binary form of this descriptor.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.Google#Protobuf#Reflection#IDescriptor#FullName">
            <value>
            Implementation of IDescriptor.FullName - just returns the same as Name.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.Google#Protobuf#Reflection#IDescriptor#File">
            <value>
            Implementation of IDescriptor.File - just returns this descriptor.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.DescriptorPool">
            <value>
            Pool containing symbol descriptors.
            </value>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.FindTypeByName``1(System.String)">
            <summary>
            Finds a type (message, enum, service or extension) in the file by name. Does not find nested types.
            </summary>
            <param name="name">The unqualified type name to look for.</param>
            <typeparam name="T">The type of descriptor to look for</typeparam>
            <returns>The type's descriptor, or null if not found.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.BuildFrom(Google.Protobuf.ByteString,Google.Protobuf.Reflection.FileDescriptorProto,Google.Protobuf.Reflection.FileDescriptor[],System.Boolean,Google.Protobuf.Reflection.GeneratedClrTypeInfo)">
            <summary>
            Builds a FileDescriptor from its protocol buffer representation.
            </summary>
            <param name="descriptorData">The original serialized descriptor data.
            We have only limited proto2 support, so serializing FileDescriptorProto
            would not necessarily give us this.</param>
            <param name="proto">The protocol message form of the FileDescriptor.</param>
            <param name="dependencies">FileDescriptors corresponding to all of the
            file's dependencies, in the exact order listed in the .proto file. May be null,
            in which case it is treated as an empty array.</param>
            <param name="allowUnknownDependencies">Whether unknown dependencies are ignored (true) or cause an exception to be thrown (false).</param>
            <param name="generatedCodeInfo">Details about generated code, for the purposes of reflection.</param>
            <exception cref="T:Google.Protobuf.Reflection.DescriptorValidationException">If <paramref name="proto"/> is not
            a valid descriptor. This can occur for a number of reasons, such as a field
            having an undefined type or because two messages were defined with the same name.</exception>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.FromGeneratedCode(System.Byte[],Google.Protobuf.Reflection.FileDescriptor[],Google.Protobuf.Reflection.GeneratedClrTypeInfo)">
            <summary>
            Creates a descriptor for generated code.
            </summary>
            <remarks>
            This method is only designed to be used by the results of generating code with protoc,
            which creates the appropriate dependencies etc. It has to be public because the generated
            code is "external", but should not be called directly by end users.
            </remarks>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.BuildFromByteStrings(System.Collections.Generic.IEnumerable{Google.Protobuf.ByteString})">
            <summary>
            Converts the given descriptor binary data into FileDescriptor objects.
            Note: reflection using the returned FileDescriptors is not currently supported.
            </summary>
            <param name="descriptorData">The binary file descriptor proto data. Must not be null, and any
            dependencies must come before the descriptor which depends on them. (If A depends on B, and B
            depends on C, then the descriptors must be presented in the order C, B, A.) This is compatible
            with the order in which protoc provides descriptors to plugins.</param>
            <returns>The file descriptors corresponding to <paramref name="descriptorData"/>.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.DescriptorProtoFileDescriptor">
            <summary>
            Returns the file descriptor for descriptor.proto.
            </summary>
            <remarks>
            This is used for protos which take a direct dependency on <c>descriptor.proto</c>, typically for
            annotations. While <c>descriptor.proto</c> is a proto2 file, it is built into the Google.Protobuf
            runtime for reflection purposes. The messages are internal to the runtime as they would require
            proto2 semantics for full support, but the file descriptor is available via this property. The
            C# codegen in protoc automatically uses this property when it detects a dependency on <c>descriptor.proto</c>.
            </remarks>
            <value>
            The file descriptor for <c>descriptor.proto</c>.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.FileDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this file.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.FileDescriptor.ForceReflectionInitialization``1">
            <summary>
            Performs initialization for the given generic type argument.
            </summary>
            <remarks>
            This method is present for the sake of AOT compilers. It allows code (whether handwritten or generated)
            to make calls into the reflection machinery of this library to express an intention to use that type
            reflectively (e.g. for JSON parsing and formatting). The call itself does almost nothing, but AOT compilers
            attempting to determine which generic type arguments need to be handled will spot the code path and act
            accordingly.
            </remarks>
            <typeparam name="T">The type to force initialization for.</typeparam>
        </member>
        <member name="T:Google.Protobuf.Reflection.GeneratedClrTypeInfo">
            <summary>
            Extra information provided by generated code when initializing a message or file descriptor.
            These are constructed as required, and are not long-lived. Hand-written code should
            never need to use this type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedClrTypeInfo.ClrType">
            <summary>
            Irrelevant for file descriptors; the CLR type for the message for message descriptors.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedClrTypeInfo.Parser">
            <summary>
            Irrelevant for file descriptors; the parser for message descriptors.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedClrTypeInfo.PropertyNames">
            <summary>
            Irrelevant for file descriptors; the CLR property names (in message descriptor field order)
            for fields in the message for message descriptors.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedClrTypeInfo.OneofNames">
            <summary>
            Irrelevant for file descriptors; the CLR property "base" names (in message descriptor oneof order)
            for oneofs in the message for message descriptors. It is expected that for a oneof name of "Foo",
            there will be a "FooCase" property and a "ClearFoo" method.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedClrTypeInfo.NestedTypes">
            <summary>
            The reflection information for types within this file/message descriptor. Elements may be null
            if there is no corresponding generated type, e.g. for map entry types.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.GeneratedClrTypeInfo.NestedEnums">
            <summary>
            The CLR types for enums within this file/message descriptor.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.GeneratedClrTypeInfo.#ctor(System.Type,Google.Protobuf.MessageParser,System.String[],System.String[],System.Type[],Google.Protobuf.Reflection.GeneratedClrTypeInfo[])">
            <summary>
            Creates a GeneratedClrTypeInfo for a message descriptor, with nested types, nested enums, the CLR type, property names and oneof names.
            Each array parameter may be null, to indicate a lack of values.
            The parameter order is designed to make it feasible to format the generated code readably.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.GeneratedClrTypeInfo.#ctor(System.Type[],Google.Protobuf.Reflection.GeneratedClrTypeInfo[])">
            <summary>
            Creates a GeneratedClrTypeInfo for a file descriptor, with only types and enums.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.IDescriptor">
            <summary>
            Interface implemented by all descriptor types.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.IDescriptor.Name">
            <summary>
            Returns the name of the entity (message, field etc) being described.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.IDescriptor.FullName">
            <summary>
            Returns the fully-qualified name of the entity being described.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.IDescriptor.File">
            <summary>
            Returns the descriptor for the .proto file that this entity is part of.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.IFieldAccessor">
            <summary>
            Allows fields to be reflectively accessed.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.IFieldAccessor.Descriptor">
            <summary>
            Returns the descriptor associated with this field.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.IFieldAccessor.Clear(Google.Protobuf.IMessage)">
            <summary>
            Clears the field in the specified message. (For repeated fields,
            this clears the list.)
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.IFieldAccessor.HasValue(Google.Protobuf.IMessage)">
            <summary>
            Indicates whether the field in the specified message is set. For proto3 fields, this throws an <see cref="T:System.InvalidOperationException"/>
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.IFieldAccessor.GetValue(Google.Protobuf.IMessage)">
            <summary>
            Fetches the field value. For repeated values, this will be an
            <see cref="T:System.Collections.IList"/> implementation. For map values, this will be an
            <see cref="T:System.Collections.IDictionary"/> implementation.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.IFieldAccessor.SetValue(Google.Protobuf.IMessage,System.Object)">
            <summary>
            Mutator for single "simple" fields only.
            </summary>
            <remarks>
            Repeated fields are mutated by fetching the value and manipulating it as a list.
            Map fields are mutated by fetching the value and manipulating it as a dictionary.
            </remarks>
            <exception cref="T:System.InvalidOperationException">The field is not a "simple" field.</exception>
        </member>
        <member name="T:Google.Protobuf.Reflection.MapFieldAccessor">
            <summary>
            Accessor for map fields.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.MessageDescriptor">
            <summary>
            Describes a message type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.Name">
            <summary>
            The brief name of the descriptor's target.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.ClrType">
            <summary>
            The CLR type used to represent message instances from this descriptor.
            </summary>
            <remarks>
            <para>
            The value returned by this property will be non-null for all regular fields. However,
            if a message containing a map field is introspected, the list of nested messages will include
            an auto-generated nested key/value pair message for the field. This is not represented in any
            generated type, so this property will return null in such cases.
            </para>
            <para>
            For wrapper types (<see cref="T:Google.Protobuf.WellKnownTypes.StringValue"/> and the like), the type returned here
            will be the generated message type, not the native type used by reflection for fields of those types. Code
            using reflection should call <see cref="P:Google.Protobuf.Reflection.MessageDescriptor.IsWrapperType"/> to determine whether a message descriptor represents
            a wrapper type, and handle the result appropriately.
            </para>
            </remarks>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.Parser">
            <summary>
            A parser for this message type.
            </summary>
            <remarks>
            <para>
            As <see cref="T:Google.Protobuf.Reflection.MessageDescriptor"/> is not generic, this cannot be statically
            typed to the relevant type, but it should produce objects of a type compatible with <see cref="P:Google.Protobuf.Reflection.MessageDescriptor.ClrType"/>.
            </para>
            <para>
            The value returned by this property will be non-null for all regular fields. However,
            if a message containing a map field is introspected, the list of nested messages will include
            an auto-generated nested key/value pair message for the field. No message parser object is created for
            such messages, so this property will return null in such cases.
            </para>
            <para>
            For wrapper types (<see cref="T:Google.Protobuf.WellKnownTypes.StringValue"/> and the like), the parser returned here
            will be the generated message type, not the native type used by reflection for fields of those types. Code
            using reflection should call <see cref="P:Google.Protobuf.Reflection.MessageDescriptor.IsWrapperType"/> to determine whether a message descriptor represents
            a wrapper type, and handle the result appropriately.
            </para>
            </remarks>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.IsWellKnownType">
            <summary>
            Returns whether this message is one of the "well known types" which may have runtime/protoc support.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.IsWrapperType">
            <summary>
            Returns whether this message is one of the "wrapper types" used for fields which represent primitive values
            with the addition of presence.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.ContainingType">
            <value>
            If this is a nested type, get the outer descriptor, otherwise null.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.Fields">
            <value>
            A collection of fields, which can be retrieved by name or field number.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.NestedTypes">
            <value>
            An unmodifiable list of this message type's nested types.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.EnumTypes">
            <value>
            An unmodifiable list of this message type's enum types.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.Oneofs">
            <value>
            An unmodifiable list of the "oneof" field collections in this message type.
            </value>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageDescriptor.FindFieldByName(System.String)">
            <summary>
            Finds a field by field name.
            </summary>
            <param name="name">The unqualified name of the field (e.g. "foo").</param>
            <returns>The field's descriptor, or null if not found.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageDescriptor.FindFieldByNumber(System.Int32)">
            <summary>
            Finds a field by field number.
            </summary>
            <param name="number">The field number within this message type.</param>
            <returns>The field's descriptor, or null if not found.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageDescriptor.FindDescriptor``1(System.String)">
            <summary>
            Finds a nested descriptor by name. The is valid for fields, nested
            message types, oneofs and enums.
            </summary>
            <param name="name">The unqualified name of the descriptor, e.g. "Foo"</param>
            <returns>The descriptor, or null if not found.</returns>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this message.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageDescriptor.CrossLink">
            <summary>
            Looks up and cross-links all fields and nested types.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.MessageDescriptor.FieldCollection">
            <summary>
            A collection to simplify retrieving the field accessor for a particular field.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageDescriptor.FieldCollection.InDeclarationOrder">
            <value>
            Returns the fields in the message as an immutable list, in the order in which they
            are declared in the source .proto file.
            </value>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageDescriptor.FieldCollection.InFieldNumberOrder">
            <value>
            Returns the fields in the message as an immutable list, in ascending field number
            order. Field numbers need not be contiguous, so there is no direct mapping from the
            index in the list to the field number; to retrieve a field by field number, it is better
            to use the <see cref="T:Google.Protobuf.Reflection.MessageDescriptor.FieldCollection"/> indexer.
            </value>
        </member>
        <member name="M:Google.Protobuf.Reflection.MessageDescriptor.FieldCollection.ByJsonName">
            <value>
            Returns a read-only dictionary mapping the field names in this message as they're available
            in the JSON representation to the field descriptors. For example, a field <c>foo_bar</c>
            in the message would result two entries, one with a key <c>fooBar</c> and one with a key
            <c>foo_bar</c>, both referring to the same field.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.FieldCollection.Item(System.Int32)">
            <summary>
            Retrieves the descriptor for the field with the given number.
            </summary>
            <param name="number">Number of the field to retrieve the descriptor for</param>
            <returns>The accessor for the given field</returns>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">The message descriptor does not contain a field
            with the given number</exception>
        </member>
        <member name="P:Google.Protobuf.Reflection.MessageDescriptor.FieldCollection.Item(System.String)">
            <summary>
            Retrieves the descriptor for the field with the given name.
            </summary>
            <param name="name">Name of the field to retrieve the descriptor for</param>
            <returns>The descriptor for the given field</returns>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">The message descriptor does not contain a field
            with the given name</exception>
        </member>
        <member name="T:Google.Protobuf.Reflection.MethodDescriptor">
            <summary>
            Describes a single method in a service.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptor.Service">
            <value>
            The service this method belongs to.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptor.InputType">
            <value>
            The method's input type.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptor.OutputType">
            <value>
            The method's input type.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptor.IsClientStreaming">
            <value>
            Indicates if client streams multiple requests.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptor.IsServerStreaming">
            <value>
            Indicates if server streams multiple responses.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this method.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.MethodDescriptor.Name">
            <summary>
            The brief name of the descriptor's target.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.OneofAccessor">
            <summary>
            Reflection access for a oneof, allowing clear and "get case" actions.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofAccessor.Descriptor">
            <summary>
            Gets the descriptor for this oneof.
            </summary>
            <value>
            The descriptor of the oneof.
            </value>
        </member>
        <member name="M:Google.Protobuf.Reflection.OneofAccessor.Clear(Google.Protobuf.IMessage)">
            <summary>
            Clears the oneof in the specified message.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.OneofAccessor.GetCaseFieldDescriptor(Google.Protobuf.IMessage)">
            <summary>
            Indicates which field in the oneof is set for specified message
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.OneofDescriptor">
            <summary>
            Describes a "oneof" field collection in a message type: a set of
            fields of which at most one can be set in any particular message.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofDescriptor.Name">
            <summary>
            The brief name of the descriptor's target.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofDescriptor.ContainingType">
            <summary>
            Gets the message type containing this oneof.
            </summary>
            <value>
            The message type containing this oneof.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofDescriptor.Fields">
            <summary>
            Gets the fields within this oneof, in declaration order.
            </summary>
            <value>
            The fields within this oneof, in declaration order.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofDescriptor.Accessor">
            <summary>
            Gets an accessor for reflective access to the values associated with the oneof
            in a particular message.
            </summary>
            <remarks>
            <para>
            In descriptors for generated code, the value returned by this property will always be non-null.
            </para>
            <para>
            In dynamically loaded descriptors, the value returned by this property will current be null;
            if and when dynamic messages are supported, it will return a suitable accessor to work with
            them.
            </para>
            </remarks>
            <value>
            The accessor used for reflective access.
            </value>
        </member>
        <member name="P:Google.Protobuf.Reflection.OneofDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this oneof.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.OriginalNameAttribute">
            <summary>
            Specifies the original name (in the .proto file) of a named element,
            such as an enum value.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OriginalNameAttribute.Name">
            <summary>
            The name of the element in the .proto file.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.OriginalNameAttribute.PreferredAlias">
            <summary>
            If the name is preferred in the .proto file.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.OriginalNameAttribute.#ctor(System.String)">
            <summary>
            Constructs a new attribute instance for the given name.
            </summary>
            <param name="name">The name of the element in the .proto file.</param>
        </member>
        <member name="T:Google.Protobuf.Reflection.PackageDescriptor">
            <summary>
            Represents a package in the symbol table.  We use PackageDescriptors
            just as placeholders so that someone cannot define, say, a message type
            that has the same name as an existing package.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.ReflectionUtil">
            <summary>
            The methods in this class are somewhat evil, and should not be tampered with lightly.
            Basically they allow the creation of relatively weakly typed delegates from MethodInfos
            which are more strongly typed. They do this by creating an appropriate strongly typed
            delegate from the MethodInfo, and then calling that within an anonymous method.
            Mind-bending stuff (at least to your humble narrator) but the resulting delegates are
            very fast compared with calling Invoke later on.
            </summary>
        </member>
        <member name="F:Google.Protobuf.Reflection.ReflectionUtil.EmptyTypes">
            <summary>
            Empty Type[] used when calling GetProperty to force property instead of indexer fetching.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.ReflectionUtil.CreateFuncIMessageObject(System.Reflection.MethodInfo)">
            <summary>
            Creates a delegate which will cast the argument to the type that declares the method,
            call the method on it, then convert the result to object.
            </summary>
            <param name="method">The method to create a delegate for, which must be declared in an IMessage
            implementation.</param>
        </member>
        <member name="M:Google.Protobuf.Reflection.ReflectionUtil.CreateFuncIMessageInt32(System.Reflection.MethodInfo)">
            <summary>
            Creates a delegate which will cast the argument to the type that declares the method,
            call the method on it, then convert the result to the specified type. The method is expected
            to actually return an enum (because of where we're calling it - for oneof cases). Sometimes that
            means we need some extra work to perform conversions.
            </summary>
            <param name="method">The method to create a delegate for, which must be declared in an IMessage
            implementation.</param>
        </member>
        <member name="M:Google.Protobuf.Reflection.ReflectionUtil.CreateActionIMessageObject(System.Reflection.MethodInfo)">
            <summary>
            Creates a delegate which will execute the given method after casting the first argument to
            the type that declares the method, and the second argument to the first parameter type of the method.
            </summary>
            <param name="method">The method to create a delegate for, which must be declared in an IMessage
            implementation.</param>
        </member>
        <member name="M:Google.Protobuf.Reflection.ReflectionUtil.CreateActionIMessage(System.Reflection.MethodInfo)">
            <summary>
            Creates a delegate which will execute the given method after casting the first argument to
            type that declares the method.
            </summary>
            <param name="method">The method to create a delegate for, which must be declared in an IMessage
            implementation.</param>
        </member>
        <member name="M:Google.Protobuf.Reflection.ReflectionUtil.GetReflectionHelper(System.Type,System.Type)">
            <summary>
            Creates a reflection helper for the given type arguments. Currently these are created on demand
            rather than cached; this will be "busy" when initially loading a message's descriptor, but after that
            they can be garbage collected. We could cache them by type if that proves to be important, but creating
            an object is pretty cheap.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.RepeatedFieldAccessor">
            <summary>
            Accessor for repeated fields.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.ServiceDescriptor">
            <summary>
            Describes a service type.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceDescriptor.Name">
            <summary>
            The brief name of the descriptor's target.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceDescriptor.Methods">
            <value>
            An unmodifiable list of methods in this service.
            </value>
        </member>
        <member name="M:Google.Protobuf.Reflection.ServiceDescriptor.FindMethodByName(System.String)">
            <summary>
            Finds a method by name.
            </summary>
            <param name="name">The unqualified name of the method (e.g. "Foo").</param>
            <returns>The method's decsriptor, or null if not found.</returns>
        </member>
        <member name="P:Google.Protobuf.Reflection.ServiceDescriptor.CustomOptions">
            <summary>
            The (possibly empty) set of custom options for this service.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.SingleFieldAccessor">
            <summary>
            Accessor for single fields.
            </summary>
        </member>
        <member name="T:Google.Protobuf.Reflection.TypeRegistry">
            <summary>
            An immutable registry of types which can be looked up by their full name.
            </summary>
        </member>
        <member name="P:Google.Protobuf.Reflection.TypeRegistry.Empty">
            <summary>
            An empty type registry, containing no types.
            </summary>
        </member>
        <member name="M:Google.Protobuf.Reflection.TypeRegistry.Find(System.String)">
            <summary>
            Attempts to find a message descriptor by its full name.
            </summary>
            <param name="fullName">The full name of the message, which is the dot-separated
            combination of package, containing messages and message name</param>
            <returns>The message descriptor corresponding to <paramref name="fullName"/> or null
            if there is no such message descriptor.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.TypeRegistry.FromFiles(Google.Protobuf.Reflection.FileDescriptor[])">
            <summary>
            Creates a type registry from the specified set of file descriptors.
            </summary>
            <remarks>
            This is a convenience overload for <see cref="M:Google.Protobuf.Reflection.TypeRegistry.FromFiles(System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.FileDescriptor})"/>
            to allow calls such as <c>TypeRegistry.FromFiles(descriptor1, descriptor2)</c>.
            </remarks>
            <param name="fileDescriptors">The set of files to include in the registry. Must not contain null values.</param>
            <returns>A type registry for the given files.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.TypeRegistry.FromFiles(System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.FileDescriptor})">
            <summary>
            Creates a type registry from the specified set of file descriptors.
            </summary>
            <remarks>
            All message types within all the specified files are added to the registry, and
            the dependencies of the specified files are also added, recursively.
            </remarks>
            <param name="fileDescriptors">The set of files to include in the registry. Must not contain null values.</param>
            <returns>A type registry for the given files.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.TypeRegistry.FromMessages(Google.Protobuf.Reflection.MessageDescriptor[])">
            <summary>
            Creates a type registry from the file descriptor parents of the specified set of message descriptors.
            </summary>
            <remarks>
            This is a convenience overload for <see cref="M:Google.Protobuf.Reflection.TypeRegistry.FromMessages(System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.MessageDescriptor})"/>
            to allow calls such as <c>TypeRegistry.FromFiles(descriptor1, descriptor2)</c>.
            </remarks>
            <param name="messageDescriptors">The set of message descriptors to use to identify file descriptors to include in the registry.
            Must not contain null values.</param>
            <returns>A type registry for the given files.</returns>
        </member>
        <member name="M:Google.Protobuf.Reflection.TypeRegistry.FromMessages(System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.MessageDescriptor})">
            <summary>
            Creates a type registry from the file descriptor parents of the specified set of message descriptors.
            </summary>
            <remarks>
            The specified message descriptors are only used to identify their file descriptors; the returned registry
            contains all the types within the file descriptors which contain the specified message descriptors (and
            the dependencies of those files), not just the specified messages.
            </remarks>
            <param name="messageDescriptors">The set of message descriptors to use to identify file descriptors to include in the registry.
            Must not contain null values.</param>
            <returns>A type registry for the given files.</returns>
        </member>
        <member name="T:Google.Protobuf.Reflection.TypeRegistry.Builder">
            <summary>
            Builder class which isn't exposed, but acts as a convenient alternative to passing round two dictionaries in recursive calls.
            </summary>
        </member>
        <member name="T:Google.Protobuf.UnknownField">
             <summary>
             Represents a single field in an UnknownFieldSet.
            
             An UnknownField consists of four lists of values. The lists correspond
             to the four "wire types" used in the protocol buffer binary format.
             Normally, only one of the four lists will contain any values, since it
             is impossible to define a valid message type that declares two different
             types for the same field number. However, the code is designed to allow
             for the case where the same unknown field number is encountered using
             multiple different wire types.
            
             </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.#ctor">
            <summary>
            Creates a new UnknownField.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.Equals(System.Object)">
            <summary>
            Checks if two unknown field are equal.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.GetHashCode">
            <summary>
            Get the hash code of the unknown field.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.WriteTo(System.Int32,Google.Protobuf.CodedOutputStream)">
            <summary>
            Serializes the field, including the field number, and writes it to
            <paramref name="output"/>
            </summary>
            <param name="fieldNumber">The unknown field number.</param>
            <param name="output">The CodedOutputStream to write to.</param>
        </member>
        <member name="M:Google.Protobuf.UnknownField.GetSerializedSize(System.Int32)">
            <summary>
            Computes the number of bytes required to encode this field, including field
            number.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.MergeFrom(Google.Protobuf.UnknownField)">
            <summary>
            Merge the values in <paramref name="other" /> into this field.  For each list
            of values, <paramref name="other"/>'s values are append to the ones in this
            field.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.AddAll``1(System.Collections.Generic.List{``0},System.Collections.Generic.IList{``0})">
            <summary>
            Returns a new list containing all of the given specified values from
            both the <paramref name="current"/> and <paramref name="extras"/> lists.
            If <paramref name="current" /> is null and <paramref name="extras"/> is empty,
            null is returned. Otherwise, either a new list is created (if <paramref name="current" />
            is null) or the elements of <paramref name="extras"/> are added to <paramref name="current" />.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.AddVarint(System.UInt64)">
            <summary>
            Adds a varint value.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.AddFixed32(System.UInt32)">
            <summary>
            Adds a fixed32 value.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.AddFixed64(System.UInt64)">
            <summary>
            Adds a fixed64 value.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.AddLengthDelimited(Google.Protobuf.ByteString)">
            <summary>
            Adds a length-delimited value.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownField.Add``1(System.Collections.Generic.List{``0},``0)">
            <summary>
            Adds <paramref name="value"/> to the <paramref name="list"/>, creating
            a new list if <paramref name="list"/> is null. The list is returned - either
            the original reference or the new list.
            </summary>
        </member>
        <member name="T:Google.Protobuf.UnknownFieldSet">
             <summary>
             Used to keep track of fields which were seen when parsing a protocol message
             but whose field numbers or types are unrecognized. This most frequently
             occurs when new fields are added to a message type and then messages containing
             those fields are read by old software that was built before the new types were
             added.
            
             Most users will never need to use this class directly.
             </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.#ctor">
            <summary>
            Creates a new UnknownFieldSet.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.HasField(System.Int32)">
            <summary>
            Checks whether or not the given field number is present in the set.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.WriteTo(Google.Protobuf.CodedOutputStream)">
            <summary>
            Serializes the set and writes it to <paramref name="output"/>.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.CalculateSize">
            <summary>
            Gets the number of bytes required to encode this set.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.Equals(System.Object)">
            <summary>
            Checks if two unknown field sets are equal.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.GetHashCode">
            <summary>
            Gets the unknown field set's hash code.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.AddOrReplaceField(System.Int32,Google.Protobuf.UnknownField)">
            <summary>
            Adds a field to the set. If a field with the same number already exists, it
            is replaced.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.MergeFieldFrom(Google.Protobuf.CodedInputStream)">
            <summary>
            Parse a single field from <paramref name="input"/> and merge it
            into this set.
            </summary>
            <param name="input">The coded input stream containing the field</param>
            <returns>false if the tag is an "end group" tag, true otherwise</returns>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.MergeFieldFrom(Google.Protobuf.UnknownFieldSet,Google.Protobuf.CodedInputStream)">
            <summary>
            Create a new UnknownFieldSet if unknownFields is null.
            Parse a single field from <paramref name="input"/> and merge it
            into unknownFields. If <paramref name="input"/> is configured to discard unknown fields,
            <paramref name="unknownFields"/> will be returned as-is and the field will be skipped.
            </summary>
            <param name="unknownFields">The UnknownFieldSet which need to be merged</param>
            <param name="input">The coded input stream containing the field</param>
            <returns>The merged UnknownFieldSet</returns>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.MergeFrom(Google.Protobuf.UnknownFieldSet)">
            <summary>
            Merges the fields from <paramref name="other"/> into this set.
            If a field number exists in both sets, the values in <paramref name="other"/>
            will be appended to the values in this set.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.MergeFrom(Google.Protobuf.UnknownFieldSet,Google.Protobuf.UnknownFieldSet)">
            <summary>
            Created a new UnknownFieldSet to <paramref name="unknownFields"/> if
            needed and merges the fields from <paramref name="other"/> into the first set.
            If a field number exists in both sets, the values in <paramref name="other"/>
            will be appended to the values in this set.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.MergeField(System.Int32,Google.Protobuf.UnknownField)">
            <summary>
            Adds a field to the unknown field set. If a field with the same
            number already exists, the two are merged.
            </summary>
        </member>
        <member name="M:Google.Protobuf.UnknownFieldSet.Clone(Google.Protobuf.UnknownFieldSet)">
            <summary>
            Clone an unknown field set from <paramref name="other"/>.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.AnyReflection">
            <summary>Holder for reflection information generated from google/protobuf/any.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.AnyReflection.Descriptor">
            <summary>File descriptor for google/protobuf/any.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Any">
             <summary>
             `Any` contains an arbitrary serialized protocol buffer message along with a
             URL that describes the type of the serialized message.
            
             Protobuf library provides support to pack/unpack Any values in the form
             of utility functions or additional generated methods of the Any type.
            
             Example 1: Pack and unpack a message in C++.
            
                 Foo foo = ...;
                 Any any;
                 any.PackFrom(foo);
                 ...
                 if (any.UnpackTo(&amp;foo)) {
                   ...
                 }
            
             Example 2: Pack and unpack a message in Java.
            
                 Foo foo = ...;
                 Any any = Any.pack(foo);
                 ...
                 if (any.is(Foo.class)) {
                   foo = any.unpack(Foo.class);
                 }
            
              Example 3: Pack and unpack a message in Python.
            
                 foo = Foo(...)
                 any = Any()
                 any.Pack(foo)
                 ...
                 if any.Is(Foo.DESCRIPTOR):
                   any.Unpack(foo)
                   ...
            
              Example 4: Pack and unpack a message in Go
            
                  foo := &amp;pb.Foo{...}
                  any, err := ptypes.MarshalAny(foo)
                  ...
                  foo := &amp;pb.Foo{}
                  if err := ptypes.UnmarshalAny(any, foo); err != nil {
                    ...
                  }
            
             The pack methods provided by protobuf library will by default use
             'type.googleapis.com/full.type.name' as the type URL and the unpack
             methods only use the fully qualified type name after the last '/'
             in the type URL, for example "foo.bar.com/x/y.z" will yield type
             name "y.z".
            
             JSON
             ====
             The JSON representation of an `Any` value uses the regular
             representation of the deserialized, embedded message, with an
             additional field `@type` which contains the type URL. Example:
            
                 package google.profile;
                 message Person {
                   string first_name = 1;
                   string last_name = 2;
                 }
            
                 {
                   "@type": "type.googleapis.com/google.profile.Person",
                   "firstName": &lt;string>,
                   "lastName": &lt;string>
                 }
            
             If the embedded message type is well-known and has a custom JSON
             representation, that representation will be embedded adding a field
             `value` which holds the custom JSON in addition to the `@type`
             field. Example (for message [google.protobuf.Duration][]):
            
                 {
                   "@type": "type.googleapis.com/google.protobuf.Duration",
                   "value": "1.212s"
                 }
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Any.TypeUrlFieldNumber">
            <summary>Field number for the "type_url" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Any.TypeUrl">
             <summary>
             A URL/resource name that uniquely identifies the type of the serialized
             protocol buffer message. This string must contain at least
             one "/" character. The last segment of the URL's path must represent
             the fully qualified name of the type (as in
             `path/google.protobuf.Duration`). The name should be in a canonical form
             (e.g., leading "." is not accepted).
            
             In practice, teams usually precompile into the binary all types that they
             expect it to use in the context of Any. However, for URLs which use the
             scheme `http`, `https`, or no scheme, one can optionally set up a type
             server that maps type URLs to message definitions as follows:
            
             * If no scheme is provided, `https` is assumed.
             * An HTTP GET on the URL must yield a [google.protobuf.Type][]
               value in binary format, or produce an error.
             * Applications are allowed to cache lookup results based on the
               URL, or have them precompiled into a binary to avoid any
               lookup. Therefore, binary compatibility needs to be preserved
               on changes to types. (Use versioned type names to manage
               breaking changes.)
            
             Note: this functionality is not currently available in the official
             protobuf release, and it is not used for type URLs beginning with
             type.googleapis.com.
            
             Schemes other than `http`, `https` (or the empty scheme) might be
             used with implementation specific semantics.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Any.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Any.Value">
            <summary>
            Must be a valid serialized protocol buffer of the above specified type.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Any.GetTypeName(System.String)">
            <summary>
            Retrieves the type name for a type URL, matching the <see cref="P:Google.Protobuf.Reflection.DescriptorBase.FullName"/>
            of the packed message type.
            </summary>
            <remarks>
            <para>
            This is always just the last part of the URL, after the final slash. No validation of 
            anything before the trailing slash is performed. If the type URL does not include a slash,
            an empty string is returned rather than an exception being thrown; this won't match any types,
            and the calling code is probably in a better position to give a meaningful error.
            </para>
            <para>
            There is no handling of fragments or queries  at the moment.
            </para>
            </remarks>
            <param name="typeUrl">The URL to extract the type name from</param>
            <returns>The type name</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Any.Is(Google.Protobuf.Reflection.MessageDescriptor)">
            <summary>
            Returns a bool indictating whether this Any message is of the target message type
            </summary>
            <param name="descriptor">The descriptor of the message type</param>
            <returns><c>true</c> if the type name matches the descriptor's full name or <c>false</c> otherwise</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Any.Unpack``1">
            <summary>
            Unpacks the content of this Any message into the target message type,
            which must match the type URL within this Any message.
            </summary>
            <typeparam name="T">The type of message to unpack the content into.</typeparam>
            <returns>The unpacked message.</returns>
            <exception cref="T:Google.Protobuf.InvalidProtocolBufferException">The target message type doesn't match the type URL in this message</exception>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Any.TryUnpack``1(``0@)">
            <summary>
            Attempts to unpack the content of this Any message into the target message type,
            if it matches the type URL within this Any message.
            </summary>
            <typeparam name="T">The type of message to attempt to unpack the content into.</typeparam>
            <returns><c>true</c> if the message was successfully unpacked; <c>false</c> if the type name didn't match</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Any.Pack(Google.Protobuf.IMessage)">
            <summary>
            Packs the specified message into an Any message using a type URL prefix of "type.googleapis.com".
            </summary>
            <param name="message">The message to pack.</param>
            <returns>An Any message with the content and type URL of <paramref name="message"/>.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Any.Pack(Google.Protobuf.IMessage,System.String)">
            <summary>
            Packs the specified message into an Any message using the specified type URL prefix.
            </summary>
            <param name="message">The message to pack.</param>
            <param name="typeUrlPrefix">The prefix for the type URL.</param>
            <returns>An Any message with the content and type URL of <paramref name="message"/>.</returns>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.ApiReflection">
            <summary>Holder for reflection information generated from google/protobuf/api.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.ApiReflection.Descriptor">
            <summary>File descriptor for google/protobuf/api.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Api">
             <summary>
             Api is a light-weight descriptor for an API Interface.
            
             Interfaces are also described as "protocol buffer services" in some contexts,
             such as by the "service" keyword in a .proto file, but they are different
             from API Services, which represent a concrete implementation of an interface
             as opposed to simply a description of methods and bindings. They are also
             sometimes simply referred to as "APIs" in other contexts, such as the name of
             this message itself. See https://cloud.google.com/apis/design/glossary for
             detailed terminology.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Api.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Api.Name">
            <summary>
            The fully qualified name of this interface, including package name
            followed by the interface's simple name.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Api.MethodsFieldNumber">
            <summary>Field number for the "methods" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Api.Methods">
            <summary>
            The methods of this interface, in unspecified order.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Api.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Api.Options">
            <summary>
            Any metadata attached to the interface.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Api.VersionFieldNumber">
            <summary>Field number for the "version" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Api.Version">
             <summary>
             A version string for this interface. If specified, must have the form
             `major-version.minor-version`, as in `1.10`. If the minor version is
             omitted, it defaults to zero. If the entire version field is empty, the
             major version is derived from the package name, as outlined below. If the
             field is not empty, the version in the package name will be verified to be
             consistent with what is provided here.
            
             The versioning schema uses [semantic
             versioning](http://semver.org) where the major version number
             indicates a breaking change and the minor version an additive,
             non-breaking change. Both version numbers are signals to users
             what to expect from different versions, and should be carefully
             chosen based on the product plan.
            
             The major version is also reflected in the package name of the
             interface, which must end in `v&lt;major-version>`, as in
             `google.feature.v1`. For major versions 0 and 1, the suffix can
             be omitted. Zero major versions must only be used for
             experimental, non-GA interfaces.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Api.SourceContextFieldNumber">
            <summary>Field number for the "source_context" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Api.SourceContext">
            <summary>
            Source context for the protocol buffer service represented by this
            message.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Api.MixinsFieldNumber">
            <summary>Field number for the "mixins" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Api.Mixins">
            <summary>
            Included interfaces. See [Mixin][].
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Api.SyntaxFieldNumber">
            <summary>Field number for the "syntax" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Api.Syntax">
            <summary>
            The source syntax of the service.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Method">
            <summary>
            Method represents a method of an API interface.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Method.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Method.Name">
            <summary>
            The simple name of this method.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Method.RequestTypeUrlFieldNumber">
            <summary>Field number for the "request_type_url" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Method.RequestTypeUrl">
            <summary>
            A URL of the input message type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Method.RequestStreamingFieldNumber">
            <summary>Field number for the "request_streaming" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Method.RequestStreaming">
            <summary>
            If true, the request is streamed.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Method.ResponseTypeUrlFieldNumber">
            <summary>Field number for the "response_type_url" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Method.ResponseTypeUrl">
            <summary>
            The URL of the output message type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Method.ResponseStreamingFieldNumber">
            <summary>Field number for the "response_streaming" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Method.ResponseStreaming">
            <summary>
            If true, the response is streamed.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Method.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Method.Options">
            <summary>
            Any metadata attached to the method.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Method.SyntaxFieldNumber">
            <summary>Field number for the "syntax" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Method.Syntax">
            <summary>
            The source syntax of this method.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Mixin">
             <summary>
             Declares an API Interface to be included in this interface. The including
             interface must redeclare all the methods from the included interface, but
             documentation and options are inherited as follows:
            
             - If after comment and whitespace stripping, the documentation
               string of the redeclared method is empty, it will be inherited
               from the original method.
            
             - Each annotation belonging to the service config (http,
               visibility) which is not set in the redeclared method will be
               inherited.
            
             - If an http annotation is inherited, the path pattern will be
               modified as follows. Any version prefix will be replaced by the
               version of the including interface plus the [root][] path if
               specified.
            
             Example of a simple mixin:
            
                 package google.acl.v1;
                 service AccessControl {
                   // Get the underlying ACL object.
                   rpc GetAcl(GetAclRequest) returns (Acl) {
                     option (google.api.http).get = "/v1/{resource=**}:getAcl";
                   }
                 }
            
                 package google.storage.v2;
                 service Storage {
                   rpc GetAcl(GetAclRequest) returns (Acl);
            
                   // Get a data record.
                   rpc GetData(GetDataRequest) returns (Data) {
                     option (google.api.http).get = "/v2/{resource=**}";
                   }
                 }
            
             Example of a mixin configuration:
            
                 apis:
                 - name: google.storage.v2.Storage
                   mixins:
                   - name: google.acl.v1.AccessControl
            
             The mixin construct implies that all methods in `AccessControl` are
             also declared with same name and request/response types in
             `Storage`. A documentation generator or annotation processor will
             see the effective `Storage.GetAcl` method after inherting
             documentation and annotations as follows:
            
                 service Storage {
                   // Get the underlying ACL object.
                   rpc GetAcl(GetAclRequest) returns (Acl) {
                     option (google.api.http).get = "/v2/{resource=**}:getAcl";
                   }
                   ...
                 }
            
             Note how the version in the path pattern changed from `v1` to `v2`.
            
             If the `root` field in the mixin is specified, it should be a
             relative path under which inherited HTTP paths are placed. Example:
            
                 apis:
                 - name: google.storage.v2.Storage
                   mixins:
                   - name: google.acl.v1.AccessControl
                     root: acls
            
             This implies the following inherited HTTP annotation:
            
                 service Storage {
                   // Get the underlying ACL object.
                   rpc GetAcl(GetAclRequest) returns (Acl) {
                     option (google.api.http).get = "/v2/acls/{resource=**}:getAcl";
                   }
                   ...
                 }
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Mixin.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Mixin.Name">
            <summary>
            The fully qualified name of the interface which is included.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Mixin.RootFieldNumber">
            <summary>Field number for the "root" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Mixin.Root">
            <summary>
            If non-empty specifies a path under which inherited HTTP paths
            are rooted.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.DurationReflection">
            <summary>Holder for reflection information generated from google/protobuf/duration.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.DurationReflection.Descriptor">
            <summary>File descriptor for google/protobuf/duration.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Duration">
             <summary>
             A Duration represents a signed, fixed-length span of time represented
             as a count of seconds and fractions of seconds at nanosecond
             resolution. It is independent of any calendar and concepts like "day"
             or "month". It is related to Timestamp in that the difference between
             two Timestamp values is a Duration and it can be added or subtracted
             from a Timestamp. Range is approximately +-10,000 years.
            
             # Examples
            
             Example 1: Compute Duration from two Timestamps in pseudo code.
            
                 Timestamp start = ...;
                 Timestamp end = ...;
                 Duration duration = ...;
            
                 duration.seconds = end.seconds - start.seconds;
                 duration.nanos = end.nanos - start.nanos;
            
                 if (duration.seconds &lt; 0 &amp;&amp; duration.nanos > 0) {
                   duration.seconds += 1;
                   duration.nanos -= 1000000000;
                 } else if (durations.seconds > 0 &amp;&amp; duration.nanos &lt; 0) {
                   duration.seconds -= 1;
                   duration.nanos += 1000000000;
                 }
            
             Example 2: Compute Timestamp from Timestamp + Duration in pseudo code.
            
                 Timestamp start = ...;
                 Duration duration = ...;
                 Timestamp end = ...;
            
                 end.seconds = start.seconds + duration.seconds;
                 end.nanos = start.nanos + duration.nanos;
            
                 if (end.nanos &lt; 0) {
                   end.seconds -= 1;
                   end.nanos += 1000000000;
                 } else if (end.nanos >= 1000000000) {
                   end.seconds += 1;
                   end.nanos -= 1000000000;
                 }
            
             Example 3: Compute Duration from datetime.timedelta in Python.
            
                 td = datetime.timedelta(days=3, minutes=10)
                 duration = Duration()
                 duration.FromTimedelta(td)
            
             # JSON Mapping
            
             In JSON format, the Duration type is encoded as a string rather than an
             object, where the string ends in the suffix "s" (indicating seconds) and
             is preceded by the number of seconds, with nanoseconds expressed as
             fractional seconds. For example, 3 seconds with 0 nanoseconds should be
             encoded in JSON format as "3s", while 3 seconds and 1 nanosecond should
             be expressed in JSON format as "3.000000001s", and 3 seconds and 1
             microsecond should be expressed in JSON format as "3.000001s".
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Duration.SecondsFieldNumber">
            <summary>Field number for the "seconds" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Duration.Seconds">
            <summary>
            Signed seconds of the span of time. Must be from -315,576,000,000
            to +315,576,000,000 inclusive. Note: these bounds are computed from:
            60 sec/min * 60 min/hr * 24 hr/day * 365.25 days/year * 10000 years
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Duration.NanosFieldNumber">
            <summary>Field number for the "nanos" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Duration.Nanos">
            <summary>
            Signed fractions of a second at nanosecond resolution of the span
            of time. Durations less than one second are represented with a 0
            `seconds` field and a positive or negative `nanos` field. For durations
            of one second or more, a non-zero value for the `nanos` field must be
            of the same sign as the `seconds` field. Must be from -999,999,999
            to +999,999,999 inclusive.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Duration.NanosecondsPerSecond">
            <summary>
            The number of nanoseconds in a second.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Duration.NanosecondsPerTick">
            <summary>
            The number of nanoseconds in a BCL tick (as used by <see cref="T:System.TimeSpan"/> and <see cref="T:System.DateTime"/>).
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Duration.MaxSeconds">
            <summary>
            The maximum permitted number of seconds.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Duration.MinSeconds">
            <summary>
            The minimum permitted number of seconds.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.ToTimeSpan">
            <summary>
            Converts this <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/> to a <see cref="T:System.TimeSpan"/>.
            </summary>
            <remarks>If the duration is not a precise number of ticks, it is truncated towards 0.</remarks>
            <returns>The value of this duration, as a <c>TimeSpan</c>.</returns>
            <exception cref="T:System.InvalidOperationException">This value isn't a valid normalized duration, as
            described in the documentation.</exception>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.FromTimeSpan(System.TimeSpan)">
            <summary>
            Converts the given <see cref="T:System.TimeSpan"/> to a <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/>.
            </summary>
            <param name="timeSpan">The <c>TimeSpan</c> to convert.</param>
            <returns>The value of the given <c>TimeSpan</c>, as a <c>Duration</c>.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.op_UnaryNegation(Google.Protobuf.WellKnownTypes.Duration)">
            <summary>
            Returns the result of negating the duration. For example, the negation of 5 minutes is -5 minutes.
            </summary>
            <param name="value">The duration to negate. Must not be null.</param>
            <returns>The negated value of this duration.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.op_Addition(Google.Protobuf.WellKnownTypes.Duration,Google.Protobuf.WellKnownTypes.Duration)">
            <summary>
            Adds the two specified <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/> values together.
            </summary>
            <param name="lhs">The first value to add. Must not be null.</param>
            <param name="rhs">The second value to add. Must not be null.</param>
            <returns></returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.op_Subtraction(Google.Protobuf.WellKnownTypes.Duration,Google.Protobuf.WellKnownTypes.Duration)">
            <summary>
            Subtracts one <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/> from another.
            </summary>
            <param name="lhs">The duration to subtract from. Must not be null.</param>
            <param name="rhs">The duration to subtract. Must not be null.</param>
            <returns>The difference between the two specified durations.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.Normalize(System.Int64,System.Int32)">
            <summary>
            Creates a duration with the normalized values from the given number of seconds and
            nanoseconds, conforming with the description in the proto file.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.ToJson(System.Int64,System.Int32,System.Boolean)">
            <summary>
            Converts a duration specified in seconds/nanoseconds to a string.
            </summary>
            <remarks>
            If the value is a normalized duration in the range described in <c>duration.proto</c>,
            <paramref name="diagnosticOnly"/> is ignored. Otherwise, if the parameter is <c>true</c>,
            a JSON object with a warning is returned; if it is <c>false</c>, an <see cref="T:System.InvalidOperationException"/> is thrown.
            </remarks>
            <param name="seconds">Seconds portion of the duration.</param>
            <param name="nanoseconds">Nanoseconds portion of the duration.</param>
            <param name="diagnosticOnly">Determines the handling of non-normalized values</param>
            <exception cref="T:System.InvalidOperationException">The represented duration is invalid, and <paramref name="diagnosticOnly"/> is <c>false</c>.</exception>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.ToDiagnosticString">
            <summary>
            Returns a string representation of this <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/> for diagnostic purposes.
            </summary>
            <remarks>
            Normally the returned value will be a JSON string value (including leading and trailing quotes) but
            when the value is non-normalized or out of range, a JSON object representation will be returned
            instead, including a warning. This is to avoid exceptions being thrown when trying to
            diagnose problems - the regular JSON formatter will still throw an exception for non-normalized
            values.
            </remarks>
            <returns>A string representation of this value.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Duration.AppendNanoseconds(System.Text.StringBuilder,System.Int32)">
            <summary>
            Appends a number of nanoseconds to a StringBuilder. Either 0 digits are added (in which
            case no "." is appended), or 3 6 or 9 digits. This is internal for use in Timestamp as well
            as Duration.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.EmptyReflection">
            <summary>Holder for reflection information generated from google/protobuf/empty.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.EmptyReflection.Descriptor">
            <summary>File descriptor for google/protobuf/empty.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Empty">
             <summary>
             A generic empty message that you can re-use to avoid defining duplicated
             empty messages in your APIs. A typical example is to use it as the request
             or the response type of an API method. For instance:
            
                 service Foo {
                   rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty);
                 }
            
             The JSON representation for `Empty` is empty JSON object `{}`.
             </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.FieldMaskReflection">
            <summary>Holder for reflection information generated from google/protobuf/field_mask.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.FieldMaskReflection.Descriptor">
            <summary>File descriptor for google/protobuf/field_mask.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.FieldMask">
             <summary>
             `FieldMask` represents a set of symbolic field paths, for example:
            
                 paths: "f.a"
                 paths: "f.b.d"
            
             Here `f` represents a field in some root message, `a` and `b`
             fields in the message found in `f`, and `d` a field found in the
             message in `f.b`.
            
             Field masks are used to specify a subset of fields that should be
             returned by a get operation or modified by an update operation.
             Field masks also have a custom JSON encoding (see below).
            
             # Field Masks in Projections
            
             When used in the context of a projection, a response message or
             sub-message is filtered by the API to only contain those fields as
             specified in the mask. For example, if the mask in the previous
             example is applied to a response message as follows:
            
                 f {
                   a : 22
                   b {
                     d : 1
                     x : 2
                   }
                   y : 13
                 }
                 z: 8
            
             The result will not contain specific values for fields x,y and z
             (their value will be set to the default, and omitted in proto text
             output):
            
                 f {
                   a : 22
                   b {
                     d : 1
                   }
                 }
            
             A repeated field is not allowed except at the last position of a
             paths string.
            
             If a FieldMask object is not present in a get operation, the
             operation applies to all fields (as if a FieldMask of all fields
             had been specified).
            
             Note that a field mask does not necessarily apply to the
             top-level response message. In case of a REST get operation, the
             field mask applies directly to the response, but in case of a REST
             list operation, the mask instead applies to each individual message
             in the returned resource list. In case of a REST custom method,
             other definitions may be used. Where the mask applies will be
             clearly documented together with its declaration in the API.  In
             any case, the effect on the returned resource/resources is required
             behavior for APIs.
            
             # Field Masks in Update Operations
            
             A field mask in update operations specifies which fields of the
             targeted resource are going to be updated. The API is required
             to only change the values of the fields as specified in the mask
             and leave the others untouched. If a resource is passed in to
             describe the updated values, the API ignores the values of all
             fields not covered by the mask.
            
             If a repeated field is specified for an update operation, new values will
             be appended to the existing repeated field in the target resource. Note that
             a repeated field is only allowed in the last position of a `paths` string.
            
             If a sub-message is specified in the last position of the field mask for an
             update operation, then new value will be merged into the existing sub-message
             in the target resource.
            
             For example, given the target message:
            
                 f {
                   b {
                     d: 1
                     x: 2
                   }
                   c: [1]
                 }
            
             And an update message:
            
                 f {
                   b {
                     d: 10
                   }
                   c: [2]
                 }
            
             then if the field mask is:
            
              paths: ["f.b", "f.c"]
            
             then the result will be:
            
                 f {
                   b {
                     d: 10
                     x: 2
                   }
                   c: [1, 2]
                 }
            
             An implementation may provide options to override this default behavior for
             repeated and message fields.
            
             In order to reset a field's value to the default, the field must
             be in the mask and set to the default value in the provided resource.
             Hence, in order to reset all fields of a resource, provide a default
             instance of the resource and set all fields in the mask, or do
             not provide a mask as described below.
            
             If a field mask is not present on update, the operation applies to
             all fields (as if a field mask of all fields has been specified).
             Note that in the presence of schema evolution, this may mean that
             fields the client does not know and has therefore not filled into
             the request will be reset to their default. If this is unwanted
             behavior, a specific service may require a client to always specify
             a field mask, producing an error if not.
            
             As with get operations, the location of the resource which
             describes the updated values in the request message depends on the
             operation kind. In any case, the effect of the field mask is
             required to be honored by the API.
            
             ## Considerations for HTTP REST
            
             The HTTP kind of an update operation which uses a field mask must
             be set to PATCH instead of PUT in order to satisfy HTTP semantics
             (PUT must only be used for full updates).
            
             # JSON Encoding of Field Masks
            
             In JSON, a field mask is encoded as a single string where paths are
             separated by a comma. Fields name in each path are converted
             to/from lower-camel naming conventions.
            
             As an example, consider the following message declarations:
            
                 message Profile {
                   User user = 1;
                   Photo photo = 2;
                 }
                 message User {
                   string display_name = 1;
                   string address = 2;
                 }
            
             In proto a field mask for `Profile` may look as such:
            
                 mask {
                   paths: "user.display_name"
                   paths: "photo"
                 }
            
             In JSON, the same mask is represented as below:
            
                 {
                   mask: "user.displayName,photo"
                 }
            
             # Field Masks and Oneof Fields
            
             Field masks treat fields in oneofs just as regular fields. Consider the
             following message:
            
                 message SampleMessage {
                   oneof test_oneof {
                     string name = 4;
                     SubMessage sub_message = 9;
                   }
                 }
            
             The field mask can be:
            
                 mask {
                   paths: "name"
                 }
            
             Or:
            
                 mask {
                   paths: "sub_message"
                 }
            
             Note that oneof type names ("test_oneof" in this case) cannot be used in
             paths.
            
             ## Field Mask Verification
            
             The implementation of any API method which has a FieldMask type field in the
             request should verify the included field paths, and return an
             `INVALID_ARGUMENT` error if any path is duplicated or unmappable.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.FieldMask.PathsFieldNumber">
            <summary>Field number for the "paths" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.FieldMask.Paths">
            <summary>
            The set of field mask paths.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.ToJson(System.Collections.Generic.IList{System.String},System.Boolean)">
            <summary>
            Converts a field mask specified by paths to a string.
            </summary>
            <remarks>
            If the value is a normalized duration in the range described in <c>field_mask.proto</c>,
            <paramref name="diagnosticOnly"/> is ignored. Otherwise, if the parameter is <c>true</c>,
            a JSON object with a warning is returned; if it is <c>false</c>, an <see cref="T:System.InvalidOperationException"/> is thrown.
            </remarks>
            <param name="paths">Paths in the field mask</param>
            <param name="diagnosticOnly">Determines the handling of non-normalized values</param>
            <exception cref="T:System.InvalidOperationException">The represented field mask is invalid, and <paramref name="diagnosticOnly"/> is <c>false</c>.</exception>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.ToDiagnosticString">
            <summary>
            Returns a string representation of this <see cref="T:Google.Protobuf.WellKnownTypes.FieldMask"/> for diagnostic purposes.
            </summary>
            <remarks>
            Normally the returned value will be a JSON string value (including leading and trailing quotes) but
            when the value is non-normalized or out of range, a JSON object representation will be returned
            instead, including a warning. This is to avoid exceptions being thrown when trying to
            diagnose problems - the regular JSON formatter will still throw an exception for non-normalized
            values.
            </remarks>
            <returns>A string representation of this value.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.FromString(System.String)">
            <summary>
            Parses from a string to a FieldMask.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.FromString``1(System.String)">
            <summary>
            Parses from a string to a FieldMask and validates all field paths.
            </summary>
            <typeparam name="T">The type to validate the field paths against.</typeparam>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.FromStringEnumerable``1(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Constructs a FieldMask for a list of field paths in a certain type.
            </summary>
            <typeparam name="T">The type to validate the field paths against.</typeparam>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.FromFieldNumbers``1(System.Int32[])">
            <summary>
            Constructs a FieldMask from the passed field numbers.
            </summary>
            <typeparam name="T">The type to validate the field paths against.</typeparam>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.FromFieldNumbers``1(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Constructs a FieldMask from the passed field numbers.
            </summary>
            <typeparam name="T">The type to validate the field paths against.</typeparam>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.IsPathValid(System.String)">
            <summary>
            Checks whether the given path is valid for a field mask.
            </summary>
            <returns>true if the path is valid; false otherwise</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.IsValid``1(Google.Protobuf.WellKnownTypes.FieldMask)">
            <summary>
            Checks whether paths in a given fields mask are valid.
            </summary>
            <typeparam name="T">The type to validate the field paths against.</typeparam>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.IsValid(Google.Protobuf.Reflection.MessageDescriptor,Google.Protobuf.WellKnownTypes.FieldMask)">
            <summary>
            Checks whether paths in a given fields mask are valid.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.IsValid``1(System.String)">
            <summary>
            Checks whether a given field path is valid.
            </summary>
            <typeparam name="T">The type to validate the field paths against.</typeparam>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.IsValid(Google.Protobuf.Reflection.MessageDescriptor,System.String)">
            <summary>
            Checks whether paths in a given fields mask are valid.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.Normalize">
            <summary>
            Converts this FieldMask to its canonical form. In the canonical form of a
            FieldMask, all field paths are sorted alphabetically and redundant field
            paths are removed.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.Union(Google.Protobuf.WellKnownTypes.FieldMask[])">
            <summary>
            Creates a union of two or more FieldMasks.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.Intersection(Google.Protobuf.WellKnownTypes.FieldMask)">
            <summary>
            Calculates the intersection of two FieldMasks.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.Merge(Google.Protobuf.IMessage,Google.Protobuf.IMessage,Google.Protobuf.WellKnownTypes.FieldMask.MergeOptions)">
            <summary>
            Merges fields specified by this FieldMask from one message to another with the
            specified merge options.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.FieldMask.Merge(Google.Protobuf.IMessage,Google.Protobuf.IMessage)">
            <summary>
            Merges fields specified by this FieldMask from one message to another.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.FieldMask.MergeOptions">
            <summary>
            Options to customize merging behavior.
            </summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.FieldMask.MergeOptions.ReplaceMessageFields">
            <summary>
            Whether to replace message fields(i.e., discard existing content in
            destination message fields) when merging.
            Default behavior is to merge the source message field into the
            destination message field.
            </summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.FieldMask.MergeOptions.ReplaceRepeatedFields">
            <summary>
            Whether to replace repeated fields (i.e., discard existing content in
            destination repeated fields) when merging.
            Default behavior is to append elements from source repeated field to the
            destination repeated field.
            </summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.FieldMask.MergeOptions.ReplacePrimitiveFields">
            <summary>
            Whether to replace primitive (non-repeated and non-message) fields in
            destination message fields with the source primitive fields (i.e., if the
            field is set in the source, the value is copied to the
            destination; if the field is unset in the source, the field is cleared
            from the destination) when merging.
            
            Default behavior is to always set the value of the source primitive
            field to the destination primitive field, and if the source field is
            unset, the default value of the source field is copied to the
            destination.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.SourceContextReflection">
            <summary>Holder for reflection information generated from google/protobuf/source_context.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.SourceContextReflection.Descriptor">
            <summary>File descriptor for google/protobuf/source_context.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.SourceContext">
            <summary>
            `SourceContext` represents information about the source of a
            protobuf element, like the file in which it is defined.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.SourceContext.FileNameFieldNumber">
            <summary>Field number for the "file_name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.SourceContext.FileName">
            <summary>
            The path-qualified name of the .proto file that contained the associated
            protobuf element.  For example: `"google/protobuf/source_context.proto"`.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.StructReflection">
            <summary>Holder for reflection information generated from google/protobuf/struct.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.StructReflection.Descriptor">
            <summary>File descriptor for google/protobuf/struct.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.NullValue">
             <summary>
             `NullValue` is a singleton enumeration to represent the null value for the
             `Value` type union.
            
              The JSON representation for `NullValue` is JSON `null`.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.NullValue.NullValue">
            <summary>
            Null value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Struct">
             <summary>
             `Struct` represents a structured data value, consisting of fields
             which map to dynamically typed values. In some languages, `Struct`
             might be supported by a native representation. For example, in
             scripting languages like JS a struct is represented as an
             object. The details of that representation are described together
             with the proto support for the language.
            
             The JSON representation for `Struct` is JSON object.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Struct.FieldsFieldNumber">
            <summary>Field number for the "fields" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Struct.Fields">
            <summary>
            Unordered map of dynamically typed values.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Value">
             <summary>
             `Value` represents a dynamically typed value which can be either
             null, a number, a string, a boolean, a recursive struct value, or a
             list of values. A producer of value is expected to set one of that
             variants, absence of any variant indicates an error.
            
             The JSON representation for `Value` is JSON value.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Value.NullValueFieldNumber">
            <summary>Field number for the "null_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Value.NullValue">
            <summary>
            Represents a null value.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Value.NumberValueFieldNumber">
            <summary>Field number for the "number_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Value.NumberValue">
            <summary>
            Represents a double value.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Value.StringValueFieldNumber">
            <summary>Field number for the "string_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Value.StringValue">
            <summary>
            Represents a string value.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Value.BoolValueFieldNumber">
            <summary>Field number for the "bool_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Value.BoolValue">
            <summary>
            Represents a boolean value.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Value.StructValueFieldNumber">
            <summary>Field number for the "struct_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Value.StructValue">
            <summary>
            Represents a structured value.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Value.ListValueFieldNumber">
            <summary>Field number for the "list_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Value.ListValue">
            <summary>
            Represents a repeated `Value`.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Value.KindOneofCase">
            <summary>Enum of possible cases for the "kind" oneof.</summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Value.ForString(System.String)">
            <summary>
            Convenience method to create a Value message with a string value.
            </summary>
            <param name="value">Value to set for the StringValue property.</param>
            <returns>A newly-created Value message with the given value.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Value.ForNumber(System.Double)">
            <summary>
            Convenience method to create a Value message with a number value.
            </summary>
            <param name="value">Value to set for the NumberValue property.</param>
            <returns>A newly-created Value message with the given value.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Value.ForBool(System.Boolean)">
            <summary>
            Convenience method to create a Value message with a Boolean value.
            </summary>
            <param name="value">Value to set for the BoolValue property.</param>
            <returns>A newly-created Value message with the given value.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Value.ForNull">
            <summary>
            Convenience method to create a Value message with a null initial value.
            </summary>
            <returns>A newly-created Value message a null initial value.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Value.ForList(Google.Protobuf.WellKnownTypes.Value[])">
            <summary>
            Convenience method to create a Value message with an initial list of values.
            </summary>
            <remarks>The values provided are not cloned; the references are copied directly.</remarks>
            <returns>A newly-created Value message an initial list value.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Value.ForStruct(Google.Protobuf.WellKnownTypes.Struct)">
            <summary>
            Convenience method to create a Value message with an initial struct value
            </summary>
            <remarks>The value provided is not cloned; the reference is copied directly.</remarks>
            <returns>A newly-created Value message an initial struct value.</returns>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.ListValue">
             <summary>
             `ListValue` is a wrapper around a repeated field of values.
            
             The JSON representation for `ListValue` is JSON array.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.ListValue.ValuesFieldNumber">
            <summary>Field number for the "values" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.ListValue.Values">
            <summary>
            Repeated field of dynamically typed values.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.TimeExtensions">
            <summary>
            Extension methods on BCL time-related types, converting to protobuf types.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.TimeExtensions.ToTimestamp(System.DateTime)">
            <summary>
            Converts the given <see cref="T:System.DateTime"/> to a <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/>.
            </summary>
            <param name="dateTime">The date and time to convert to a timestamp.</param>
            <exception cref="T:System.ArgumentException">The <paramref name="dateTime"/> value has a <see cref="P:System.DateTime.Kind"/>other than <c>Utc</c>.</exception>
            <returns>The converted timestamp.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.TimeExtensions.ToTimestamp(System.DateTimeOffset)">
            <summary>
            Converts the given <see cref="T:System.DateTimeOffset"/> to a <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/>
            </summary>
            <remarks>The offset is taken into consideration when converting the value (so the same instant in time
            is represented) but is not a separate part of the resulting value. In other words, there is no
            roundtrip operation to retrieve the original <c>DateTimeOffset</c>.</remarks>
            <param name="dateTimeOffset">The date and time (with UTC offset) to convert to a timestamp.</param>
            <returns>The converted timestamp.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.TimeExtensions.ToDuration(System.TimeSpan)">
            <summary>
            Converts the given <see cref="T:System.TimeSpan"/> to a <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/>.
            </summary>
            <param name="timeSpan">The time span to convert.</param>
            <returns>The converted duration.</returns>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.TimestampReflection">
            <summary>Holder for reflection information generated from google/protobuf/timestamp.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.TimestampReflection.Descriptor">
            <summary>File descriptor for google/protobuf/timestamp.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Timestamp">
             <summary>
             A Timestamp represents a point in time independent of any time zone or local
             calendar, encoded as a count of seconds and fractions of seconds at
             nanosecond resolution. The count is relative to an epoch at UTC midnight on
             January 1, 1970, in the proleptic Gregorian calendar which extends the
             Gregorian calendar backwards to year one.
            
             All minutes are 60 seconds long. Leap seconds are "smeared" so that no leap
             second table is needed for interpretation, using a [24-hour linear
             smear](https://developers.google.com/time/smear).
            
             The range is from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59.999999999Z. By
             restricting to that range, we ensure that we can convert to and from [RFC
             3339](https://www.ietf.org/rfc/rfc3339.txt) date strings.
            
             # Examples
            
             Example 1: Compute Timestamp from POSIX `time()`.
            
                 Timestamp timestamp;
                 timestamp.set_seconds(time(NULL));
                 timestamp.set_nanos(0);
            
             Example 2: Compute Timestamp from POSIX `gettimeofday()`.
            
                 struct timeval tv;
                 gettimeofday(&amp;tv, NULL);
            
                 Timestamp timestamp;
                 timestamp.set_seconds(tv.tv_sec);
                 timestamp.set_nanos(tv.tv_usec * 1000);
            
             Example 3: Compute Timestamp from Win32 `GetSystemTimeAsFileTime()`.
            
                 FILETIME ft;
                 GetSystemTimeAsFileTime(&amp;ft);
                 UINT64 ticks = (((UINT64)ft.dwHighDateTime) &lt;&lt; 32) | ft.dwLowDateTime;
            
                 // A Windows tick is 100 nanoseconds. Windows epoch 1601-01-01T00:00:00Z
                 // is 11644473600 seconds before Unix epoch 1970-01-01T00:00:00Z.
                 Timestamp timestamp;
                 timestamp.set_seconds((INT64) ((ticks / 10000000) - 11644473600LL));
                 timestamp.set_nanos((INT32) ((ticks % 10000000) * 100));
            
             Example 4: Compute Timestamp from Java `System.currentTimeMillis()`.
            
                 long millis = System.currentTimeMillis();
            
                 Timestamp timestamp = Timestamp.newBuilder().setSeconds(millis / 1000)
                     .setNanos((int) ((millis % 1000) * 1000000)).build();
            
             Example 5: Compute Timestamp from current time in Python.
            
                 timestamp = Timestamp()
                 timestamp.GetCurrentTime()
            
             # JSON Mapping
            
             In JSON format, the Timestamp type is encoded as a string in the
             [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format. That is, the
             format is "{year}-{month}-{day}T{hour}:{min}:{sec}[.{frac_sec}]Z"
             where {year} is always expressed using four digits while {month}, {day},
             {hour}, {min}, and {sec} are zero-padded to two digits each. The fractional
             seconds, which can go up to 9 digits (i.e. up to 1 nanosecond resolution),
             are optional. The "Z" suffix indicates the timezone ("UTC"); the timezone
             is required. A proto3 JSON serializer should always use UTC (as indicated by
             "Z") when printing the Timestamp type and a proto3 JSON parser should be
             able to accept both UTC and other timezones (as indicated by an offset).
            
             For example, "2017-01-15T01:30:15.01Z" encodes 15.01 seconds past
             01:30 UTC on January 15, 2017.
            
             In JavaScript, one can convert a Date object to this format using the
             standard [toISOString()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/toISOString)
             method. In Python, a standard `datetime.datetime` object can be converted
             to this format using [`strftime`](https://docs.python.org/2/library/time.html#time.strftime)
             with the time format spec '%Y-%m-%dT%H:%M:%S.%fZ'. Likewise, in Java, one
             can use the Joda Time's [`ISODateTimeFormat.dateTime()`](
             http://www.joda.org/joda-time/apidocs/org/joda/time/format/ISODateTimeFormat.html#dateTime%2D%2D
             ) to obtain a formatter capable of generating timestamps in this format.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Timestamp.SecondsFieldNumber">
            <summary>Field number for the "seconds" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Timestamp.Seconds">
            <summary>
            Represents seconds of UTC time since Unix epoch
            1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
            9999-12-31T23:59:59Z inclusive.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Timestamp.NanosFieldNumber">
            <summary>Field number for the "nanos" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Timestamp.Nanos">
            <summary>
            Non-negative fractions of a second at nanosecond resolution. Negative
            second values with fractions must still have non-negative nanos values
            that count forward in time. Must be from 0 to 999,999,999
            inclusive.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_Subtraction(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Returns the difference between one <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/> and another, as a <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/>.
            </summary>
            <param name="lhs">The timestamp to subtract from. Must not be null.</param>
            <param name="rhs">The timestamp to subtract. Must not be null.</param>
            <returns>The difference between the two specified timestamps.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_Addition(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Duration)">
            <summary>
            Adds a <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/> to a <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/>, to obtain another <c>Timestamp</c>.
            </summary>
            <param name="lhs">The timestamp to add the duration to. Must not be null.</param>
            <param name="rhs">The duration to add. Must not be null.</param>
            <returns>The result of adding the duration to the timestamp.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_Subtraction(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Duration)">
            <summary>
            Subtracts a <see cref="T:Google.Protobuf.WellKnownTypes.Duration"/> from a <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/>, to obtain another <c>Timestamp</c>.
            </summary>
            <param name="lhs">The timestamp to subtract the duration from. Must not be null.</param>
            <param name="rhs">The duration to subtract.</param>
            <returns>The result of subtracting the duration from the timestamp.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.ToDateTime">
            <summary>
            Converts this timestamp into a <see cref="T:System.DateTime"/>.
            </summary>
            <remarks>
            The resulting <c>DateTime</c> will always have a <c>Kind</c> of <c>Utc</c>.
            If the timestamp is not a precise number of ticks, it will be truncated towards the start
            of time. For example, a timestamp with a <see cref="P:Google.Protobuf.WellKnownTypes.Timestamp.Nanos"/> value of 99 will result in a
            <see cref="T:System.DateTime"/> value precisely on a second.
            </remarks>
            <returns>This timestamp as a <c>DateTime</c>.</returns>
            <exception cref="T:System.InvalidOperationException">The timestamp contains invalid values; either it is
            incorrectly normalized or is outside the valid range.</exception>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.ToDateTimeOffset">
            <summary>
            Converts this timestamp into a <see cref="T:System.DateTimeOffset"/>.
            </summary>
            <remarks>
            The resulting <c>DateTimeOffset</c> will always have an <c>Offset</c> of zero.
            If the timestamp is not a precise number of ticks, it will be truncated towards the start
            of time. For example, a timestamp with a <see cref="P:Google.Protobuf.WellKnownTypes.Timestamp.Nanos"/> value of 99 will result in a
            <see cref="T:System.DateTimeOffset"/> value precisely on a second.
            </remarks>
            <returns>This timestamp as a <c>DateTimeOffset</c>.</returns>
            <exception cref="T:System.InvalidOperationException">The timestamp contains invalid values; either it is
            incorrectly normalized or is outside the valid range.</exception>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.FromDateTime(System.DateTime)">
            <summary>
            Converts the specified <see cref="T:System.DateTime"/> to a <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/>.
            </summary>
            <param name="dateTime"></param>
            <exception cref="T:System.ArgumentException">The <c>Kind</c> of <paramref name="dateTime"/> is not <c>DateTimeKind.Utc</c>.</exception>
            <returns>The converted timestamp.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.FromDateTimeOffset(System.DateTimeOffset)">
            <summary>
            Converts the given <see cref="T:System.DateTimeOffset"/> to a <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/>
            </summary>
            <remarks>The offset is taken into consideration when converting the value (so the same instant in time
            is represented) but is not a separate part of the resulting value. In other words, there is no
            roundtrip operation to retrieve the original <c>DateTimeOffset</c>.</remarks>
            <param name="dateTimeOffset">The date and time (with UTC offset) to convert to a timestamp.</param>
            <returns>The converted timestamp.</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.ToJson(System.Int64,System.Int32,System.Boolean)">
            <summary>
            Converts a timestamp specified in seconds/nanoseconds to a string.
            </summary>
            <remarks>
            If the value is a normalized duration in the range described in <c>timestamp.proto</c>,
            <paramref name="diagnosticOnly"/> is ignored. Otherwise, if the parameter is <c>true</c>,
            a JSON object with a warning is returned; if it is <c>false</c>, an <see cref="T:System.InvalidOperationException"/> is thrown.
            </remarks>
            <param name="seconds">Seconds portion of the duration.</param>
            <param name="nanoseconds">Nanoseconds portion of the duration.</param>
            <param name="diagnosticOnly">Determines the handling of non-normalized values</param>
            <exception cref="T:System.InvalidOperationException">The represented duration is invalid, and <paramref name="diagnosticOnly"/> is <c>false</c>.</exception>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.CompareTo(Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Given another timestamp, returns 0 if the timestamps are equivalent, -1 if this timestamp precedes the other, and 1 otherwise
            </summary>
            <remarks>
            Make sure the timestamps are normalized. Comparing non-normalized timestamps is not specified and may give unexpected results.
            </remarks>
            <param name="other">Timestamp to compare</param>
            <returns>an integer indicating whether this timestamp precedes or follows the other</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_LessThan(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Compares two timestamps and returns whether the first is less than (chronologically precedes) the second
            </summary>
            <remarks>
            Make sure the timestamps are normalized. Comparing non-normalized timestamps is not specified and may give unexpected results.
            </remarks>
            <param name="a"></param>
            <param name="b"></param>
            <returns>true if a precedes b</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_GreaterThan(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Compares two timestamps and returns whether the first is greater than (chronologically follows) the second
            </summary>
            <remarks>
            Make sure the timestamps are normalized. Comparing non-normalized timestamps is not specified and may give unexpected results.
            </remarks>
            <param name="a"></param>
            <param name="b"></param>
            <returns>true if a follows b</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_LessThanOrEqual(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Compares two timestamps and returns whether the first is less than (chronologically precedes) the second
            </summary>
            <remarks>
            Make sure the timestamps are normalized. Comparing non-normalized timestamps is not specified and may give unexpected results.
            </remarks>
            <param name="a"></param>
            <param name="b"></param>
            <returns>true if a precedes b</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_GreaterThanOrEqual(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Compares two timestamps and returns whether the first is greater than (chronologically follows) the second
            </summary>
            <remarks>
            Make sure the timestamps are normalized. Comparing non-normalized timestamps is not specified and may give unexpected results.
            </remarks>
            <param name="a"></param>
            <param name="b"></param>
            <returns>true if a follows b</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_Equality(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Returns whether two timestamps are equivalent
            </summary>
            <remarks>
            Make sure the timestamps are normalized. Comparing non-normalized timestamps is not specified and may give unexpected results.
            </remarks>
            <param name="a"></param>
            <param name="b"></param>
            <returns>true if the two timestamps refer to the same nanosecond</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.op_Inequality(Google.Protobuf.WellKnownTypes.Timestamp,Google.Protobuf.WellKnownTypes.Timestamp)">
            <summary>
            Returns whether two timestamps differ
            </summary>
            <remarks>
            Make sure the timestamps are normalized. Comparing non-normalized timestamps is not specified and may give unexpected results.
            </remarks>
            <param name="a"></param>
            <param name="b"></param>
            <returns>true if the two timestamps differ</returns>
        </member>
        <member name="M:Google.Protobuf.WellKnownTypes.Timestamp.ToDiagnosticString">
            <summary>
            Returns a string representation of this <see cref="T:Google.Protobuf.WellKnownTypes.Timestamp"/> for diagnostic purposes.
            </summary>
            <remarks>
            Normally the returned value will be a JSON string value (including leading and trailing quotes) but
            when the value is non-normalized or out of range, a JSON object representation will be returned
            instead, including a warning. This is to avoid exceptions being thrown when trying to
            diagnose problems - the regular JSON formatter will still throw an exception for non-normalized
            values.
            </remarks>
            <returns>A string representation of this value.</returns>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.TypeReflection">
            <summary>Holder for reflection information generated from google/protobuf/type.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.TypeReflection.Descriptor">
            <summary>File descriptor for google/protobuf/type.proto</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Syntax">
            <summary>
            The syntax in which a protocol buffer element is defined.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Syntax.Proto2">
            <summary>
            Syntax `proto2`.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Syntax.Proto3">
            <summary>
            Syntax `proto3`.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Type">
            <summary>
            A protocol buffer message type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Type.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Type.Name">
            <summary>
            The fully qualified message name.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Type.FieldsFieldNumber">
            <summary>Field number for the "fields" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Type.Fields">
            <summary>
            The list of fields.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Type.OneofsFieldNumber">
            <summary>Field number for the "oneofs" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Type.Oneofs">
            <summary>
            The list of types appearing in `oneof` definitions in this type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Type.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Type.Options">
            <summary>
            The protocol buffer options.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Type.SourceContextFieldNumber">
            <summary>Field number for the "source_context" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Type.SourceContext">
            <summary>
            The source context.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Type.SyntaxFieldNumber">
            <summary>Field number for the "syntax" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Type.Syntax">
            <summary>
            The source syntax.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Field">
            <summary>
            A single field of a message type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.KindFieldNumber">
            <summary>Field number for the "kind" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.Kind">
            <summary>
            The field type.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.CardinalityFieldNumber">
            <summary>Field number for the "cardinality" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.Cardinality">
            <summary>
            The field cardinality.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.NumberFieldNumber">
            <summary>Field number for the "number" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.Number">
            <summary>
            The field number.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.Name">
            <summary>
            The field name.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.TypeUrlFieldNumber">
            <summary>Field number for the "type_url" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.TypeUrl">
            <summary>
            The field type URL, without the scheme, for message or enumeration
            types. Example: `"type.googleapis.com/google.protobuf.Timestamp"`.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.OneofIndexFieldNumber">
            <summary>Field number for the "oneof_index" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.OneofIndex">
            <summary>
            The index of the field type in `Type.oneofs`, for message or enumeration
            types. The first type has index 1; zero means the type is not in the list.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.PackedFieldNumber">
            <summary>Field number for the "packed" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.Packed">
            <summary>
            Whether to use alternative packed wire representation.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.Options">
            <summary>
            The protocol buffer options.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.JsonNameFieldNumber">
            <summary>Field number for the "json_name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.JsonName">
            <summary>
            The field JSON name.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.DefaultValueFieldNumber">
            <summary>Field number for the "default_value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Field.DefaultValue">
            <summary>
            The string value of the default value of this field. Proto2 syntax only.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Field.Types">
            <summary>Container for nested types declared in the Field message type.</summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Field.Types.Kind">
            <summary>
            Basic field types.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeUnknown">
            <summary>
            Field type unknown.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeDouble">
            <summary>
            Field type double.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeFloat">
            <summary>
            Field type float.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeInt64">
            <summary>
            Field type int64.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeUint64">
            <summary>
            Field type uint64.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeInt32">
            <summary>
            Field type int32.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeFixed64">
            <summary>
            Field type fixed64.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeFixed32">
            <summary>
            Field type fixed32.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeBool">
            <summary>
            Field type bool.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeString">
            <summary>
            Field type string.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeGroup">
            <summary>
            Field type group. Proto2 syntax only, and deprecated.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeMessage">
            <summary>
            Field type message.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeBytes">
            <summary>
            Field type bytes.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeUint32">
            <summary>
            Field type uint32.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeEnum">
            <summary>
            Field type enum.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeSfixed32">
            <summary>
            Field type sfixed32.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeSfixed64">
            <summary>
            Field type sfixed64.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeSint32">
            <summary>
            Field type sint32.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Kind.TypeSint64">
            <summary>
            Field type sint64.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Field.Types.Cardinality">
            <summary>
            Whether a field is optional, required, or repeated.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Cardinality.Unknown">
            <summary>
            For fields with unknown cardinality.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Cardinality.Optional">
            <summary>
            For optional fields.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Cardinality.Required">
            <summary>
            For required fields. Proto2 syntax only.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Field.Types.Cardinality.Repeated">
            <summary>
            For repeated fields.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Enum">
            <summary>
            Enum type definition.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Enum.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Enum.Name">
            <summary>
            Enum type name.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Enum.EnumvalueFieldNumber">
            <summary>Field number for the "enumvalue" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Enum.Enumvalue">
            <summary>
            Enum value definitions.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Enum.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Enum.Options">
            <summary>
            Protocol buffer options.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Enum.SourceContextFieldNumber">
            <summary>Field number for the "source_context" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Enum.SourceContext">
            <summary>
            The source context.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Enum.SyntaxFieldNumber">
            <summary>Field number for the "syntax" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Enum.Syntax">
            <summary>
            The source syntax.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.EnumValue">
            <summary>
            Enum value definition.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.EnumValue.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.EnumValue.Name">
            <summary>
            Enum value name.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.EnumValue.NumberFieldNumber">
            <summary>Field number for the "number" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.EnumValue.Number">
            <summary>
            Enum value number.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.EnumValue.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.EnumValue.Options">
            <summary>
            Protocol buffer options.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Option">
            <summary>
            A protocol buffer option, which can be attached to a message, field,
            enumeration, etc.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Option.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Option.Name">
            <summary>
            The option's name. For protobuf built-in options (options defined in
            descriptor.proto), this is the short name. For example, `"map_entry"`.
            For custom options, it should be the fully-qualified name. For example,
            `"google.api.http"`.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Option.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Option.Value">
            <summary>
            The option's value packed in an Any message. If the value is a primitive,
            the corresponding wrapper type defined in google/protobuf/wrappers.proto
            should be used. If the value is an enum, it should be stored as an int32
            value using the google.protobuf.Int32Value type.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.WrappersReflection">
            <summary>Holder for reflection information generated from google/protobuf/wrappers.proto</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.WrappersReflection.Descriptor">
            <summary>File descriptor for google/protobuf/wrappers.proto</summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.WrappersReflection.WrapperValueFieldNumber">
            <summary>
            Field number for the single "value" field in all wrapper types.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.DoubleValue">
             <summary>
             Wrapper message for `double`.
            
             The JSON representation for `DoubleValue` is JSON number.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.DoubleValue.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.DoubleValue.Value">
            <summary>
            The double value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.FloatValue">
             <summary>
             Wrapper message for `float`.
            
             The JSON representation for `FloatValue` is JSON number.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.FloatValue.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.FloatValue.Value">
            <summary>
            The float value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Int64Value">
             <summary>
             Wrapper message for `int64`.
            
             The JSON representation for `Int64Value` is JSON string.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Int64Value.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Int64Value.Value">
            <summary>
            The int64 value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.UInt64Value">
             <summary>
             Wrapper message for `uint64`.
            
             The JSON representation for `UInt64Value` is JSON string.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.UInt64Value.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.UInt64Value.Value">
            <summary>
            The uint64 value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.Int32Value">
             <summary>
             Wrapper message for `int32`.
            
             The JSON representation for `Int32Value` is JSON number.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.Int32Value.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.Int32Value.Value">
            <summary>
            The int32 value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.UInt32Value">
             <summary>
             Wrapper message for `uint32`.
            
             The JSON representation for `UInt32Value` is JSON number.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.UInt32Value.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.UInt32Value.Value">
            <summary>
            The uint32 value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.BoolValue">
             <summary>
             Wrapper message for `bool`.
            
             The JSON representation for `BoolValue` is JSON `true` and `false`.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.BoolValue.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.BoolValue.Value">
            <summary>
            The bool value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.StringValue">
             <summary>
             Wrapper message for `string`.
            
             The JSON representation for `StringValue` is JSON string.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.StringValue.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.StringValue.Value">
            <summary>
            The string value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WellKnownTypes.BytesValue">
             <summary>
             Wrapper message for `bytes`.
            
             The JSON representation for `BytesValue` is JSON string.
             </summary>
        </member>
        <member name="F:Google.Protobuf.WellKnownTypes.BytesValue.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Protobuf.WellKnownTypes.BytesValue.Value">
            <summary>
            The bytes value.
            </summary>
        </member>
        <member name="T:Google.Protobuf.WireFormat">
            <summary>
            This class is used internally by the Protocol Buffer Library and generated
            message implementations. It is public only for the sake of those generated
            messages. Others should not use this class directly.
            <para>
            This class contains constants and helper functions useful for dealing with
            the Protocol Buffer wire format.
            </para>
            </summary>
        </member>
        <member name="T:Google.Protobuf.WireFormat.WireType">
            <summary>
            Wire types within protobuf encoding.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WireFormat.WireType.Varint">
            <summary>
            Variable-length integer.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WireFormat.WireType.Fixed64">
            <summary>
            A fixed-length 64-bit value.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WireFormat.WireType.LengthDelimited">
            <summary>
            A length-delimited value, i.e. a length followed by that many bytes of data.
            </summary>
        </member>
        <member name="F:Google.Protobuf.WireFormat.WireType.StartGroup">
            <summary>
            A "start group" value
            </summary>
        </member>
        <member name="F:Google.Protobuf.WireFormat.WireType.EndGroup">
            <summary>
            An "end group" value
            </summary>
        </member>
        <member name="F:Google.Protobuf.WireFormat.WireType.Fixed32">
            <summary>
            A fixed-length 32-bit value.
            </summary>
        </member>
        <member name="M:Google.Protobuf.WireFormat.GetTagWireType(System.UInt32)">
            <summary>
            Given a tag value, determines the wire type (lower 3 bits).
            </summary>
        </member>
        <member name="M:Google.Protobuf.WireFormat.GetTagFieldNumber(System.UInt32)">
            <summary>
            Given a tag value, determines the field number (the upper 29 bits).
            </summary>
        </member>
        <member name="M:Google.Protobuf.WireFormat.MakeTag(System.Int32,Google.Protobuf.WireFormat.WireType)">
            <summary>
            Makes a tag value given a field number and wire type.
            </summary>
        </member>
    </members>
</doc>
