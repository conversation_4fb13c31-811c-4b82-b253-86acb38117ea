﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_resine_cout_historique")>
<ProtoContract>
Partial Public Class resine_cout_historique
    <ProtoMember(1)> Public Property id_resine_cout_historique As Integer
    <ProtoMember(2)> Public Property id_resine As Integer
    <ProtoMember(3)> Public Property date_debut As DateTime
    <ProtoMember(4)> Public Property date_fin As DateTime
    <ProtoMember(5)> Public Property date_taux As DateTime?
    <ProtoMember(6)> Public Property cout_us As Decimal
    <ProtoMember(7)> Public Property cout_can As Decimal
    <ProtoMember(8)> Public Property taux_change As Decimal
End Class