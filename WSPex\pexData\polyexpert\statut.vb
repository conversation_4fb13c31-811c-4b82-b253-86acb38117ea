﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_STATUT")>
<ProtoContract>
Partial Public Class statut
    <ProtoMember(1)> Public Property id_statut As Integer
    <ProtoMember(2)> Public Property nom As String
    'Public Property nom_anglais As String
    'Public Property dans_commande As Boolean
    'Public Property dans_usine As Boolean
    'Public Property priorite As Integer?
    'Public Property nom_bilingue As String
    'Public Property rowversion As Byte()
End Class