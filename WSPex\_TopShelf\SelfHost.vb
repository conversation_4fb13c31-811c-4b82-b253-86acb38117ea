﻿Imports System.Threading
Imports Topshelf
Imports Utilities


Public Class SelfHost
    Implements ServiceControl

    ' reset:
    ' netsh http delete urlacl url=http://+:<port>/
    ' netsh http delete urlacl url=https://+:<port>/
    ' netsh http delete sslcert ipport=0.0.0.0:<port>

    ' for HTTP:
    ' netsh http add urlacl url=http://+:<port>/ user=Everyone

    ' for HTTPS:
    ' netsh http add urlacl url=https://+:<port>/ user=Everyone
    ' netsh http add sslcert ipport=0.0.0.0:<port> certhash=1cf0fe7a46f07576acfdd5126e9aeeefe2457d46 appid={5569FCF5-FE36-4ADB-8AC4-1402817705CC}

    'Public Shared WCF_PORT As Integer = 8184


    Public Function Start(hostControl As HostControl) As Boolean Implements ServiceControl.Start

        Log("", False)

        ' Report which db connection we're using
        Dim ppdb = GetDB()
        Console.WriteLine($"
Environment: {DB_ENV}{If(DB_ENV = "ini", " -> " & INI.ReadKey("Config", "Env", "?"), "")}
Using DB: {ppdb.GetConnectionString.RemoveToken("password", ";"c).RemoveToken("User ID", ";"c).RemoveToken("app", ";"c)}
")
        'If USE_NEW Then
        '    ThreadPool.QueueUserWorkItem(AddressOf InitAndStart_NEW) ' start on separate thread to prevent service start timeout error
        'Else
        '    ThreadPool.QueueUserWorkItem(AddressOf InitAndStart_OLD) ' start on separate thread to prevent service start timeout error
        'End If
        ThreadPool.QueueUserWorkItem(AddressOf InitAndStart)

        Return True

    End Function

    Public Sub InitAndStart(state As Object)

#If False Then ' put DEBUG here to debug WSPex 
        InitCaches()
        If DataOK() Then
            BuildCommandeMachine()
            BuildCommande()
            BuildFacture()
            BuildRequisitionFacture()
            CalculFinal()
            Log($"Calcul terminé à {Now}", color:=ConsoleColor.Green)
        Else
            Log($"Les données ne sont pas complètes, ou invalide.  Le processus est interrompu.", color:=ConsoleColor.Red)
        End If
#End If

        WCFStart(WCF_PORT)
        Log($"{WCF_NAME} started successfully at : {Now}", color:=ConsoleColor.Green)

    End Sub

    Public Function [Stop](hostControl As HostControl) As Boolean Implements ServiceControl.Stop

        '-todo- stop any running threads here?

        WCFStop()
        Log("Web service stopped.", color:=ConsoleColor.Red)

        Return True

    End Function

End Class
