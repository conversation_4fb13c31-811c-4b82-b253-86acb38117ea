809d43ac3cbf19bfb66c6bbf2279f4c4dc41a0e1 116
809d43ac3cbf19bfb66c6bbf2279f4c4dc41a0e1 o MASTER
73bb21cb3b2b90080ccd292789100439f6ea6b64 o New Calcul avec tarif/redevance/commssion
456ce3aecbb09a548941989a7ccdb0fe6aceb37f o Rajouter le qte choisi pour les cmd
019f4a2674e455ca6bbada85721423a39a795d40 o RequisitionFacture_calcul_rouleau
3ff22f4e40b377022a2f05fcbcf2c96c3de99744 o Suite du nouveau calcul
f19233baa8703484b86aa7f1c3939fb5e438172f o ajout Calcul
159a13855c06abe39271d2f3bc7685e9628161d1 o default
7ba3c7d26ca9fd29430f7ca577eef51da44afc00 o optimisation_vitesse
15b174da8d48f2548bb8c70c130e9cfaf5660864 o optimisation_vitesse
112ca0295c1fb1cc79056fced51c0c102efdab02 o optimisation_vitesse_double
aebb32e2df95324751784b097f612e6900a18ac8 o test DualIDKeys vs Tuples
