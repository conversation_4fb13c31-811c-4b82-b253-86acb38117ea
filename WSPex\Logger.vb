﻿Imports System.Net.Mail
Imports System.Runtime.Caching

Module Logger
    Private ReadOnly _LogLock As New Object

    Private _prevText As String = "~"
    Private _prevCount As Integer = 0

    Private _lock As New Object

    Public Sub Log(Text As String, Optional addTimeStamp As Boolean = True, Optional sendEmail As Boolean = False, Optional color As ConsoleColor = ConsoleColor.Gray,
                   Optional fileNamePrefix As String = "wsPex")

        SyncLock _lock
#If DEBUG Then
            Console.ForegroundColor = ConsoleColor.Yellow
            Console.Write($"LOG: {Now.ToString("HH:mm:ss")} - ")
            Console.ForegroundColor = color
            Console.WriteLine($"{Text.Substring(0, Math.Min(120, Text.Length))}")
            Console.ForegroundColor = ConsoleColor.Gray ' back to default-
#End If
            Static prevName As String = ""
            Static hs As HashSet(Of String) = New HashSet(Of String)
            Dim logName = IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, $"{fileNamePrefix}_{Now.ToString("dd")}.log")

            Dim lines = Text.Trim.Split(Chr(10))
            Dim title = lines.First
            'Dim abbrev = String.Join(vbCrLf, lines.Take(2)) & " [truncated]" ' keep only first two line
            Dim abbrev = String.Join(vbCrLf, lines.Take(2)) ' keep only first two line

            Dim body = Text ' keep text intact for email...

            If hs.Contains(Text) Then
                Text = abbrev
            Else
                hs.Add(Text)
            End If

            If Text = _prevText Then ' logging the same thing multiple times...
                _prevCount += 1
            Else
                If _prevCount > 0 Then
                    Dim txt = $"Previous entry occurred {_prevCount} more time{If(_prevCount > 1, "s", "")}."
                    WriteToLogFile(txt, logName, prevName, addTimeStamp)
                    _prevCount = 0
                End If
                WriteToLogFile(Text, logName, prevName, addTimeStamp)
                _prevText = Text
            End If

            ' send email at end; error during sending was preventing log from being written...
            If sendEmail Then Logger.SendEmail(title, "An exception occurred in wsPEX", body)

        End SyncLock

    End Sub

    Private Sub WriteToLogFile(text As String, logName As String, ByRef prevName As String, addTimeStamp As Boolean)
        SyncLock _LogLock
            If addTimeStamp Then text = Now.ToString("yyyy-MM-dd HH:mm:ss ") & text
            Debug.WriteLine(text)
            If logName <> prevName AndAlso prevName <> "" Then
                IO.File.WriteAllText(logName, text & vbCrLf, System.Text.Encoding.UTF8) ' Clear the file
            Else
                IO.File.AppendAllText(logName, text & vbCrLf, System.Text.Encoding.UTF8)
            End If
            prevName = logName
        End SyncLock
    End Sub

    ' If "Key" is provided, then it will be cached for X (default 60) minutes and no more email with the same key will
    ' be sent during that time. If Key is blank the email is always sent
    Private ReadOnly Cache As MemoryCache = MemoryCache.Default
    Public Sub SendEmail(Key As String, Subject As String, Optional Body As String = "", Optional MaxOneEmailEveryXMinutes As Integer = 60, Optional Dest As String = "")

        Try
            Dim obj As Object = Nothing
            If Key <> "" Then obj = Cache(Key)

            If obj Is Nothing Then

                ' Send email
                Dim sb As New System.Text.StringBuilder
                With sb
                    .AppendLine("Poste de travail: " & My.Computer.Name)
                    .AppendLine("Date et heure: " & CStr(Date.Now))
                    .AppendLine("Email key: " & Key)
                    If Body <> "" Then
                        .AppendLine("")
                        .AppendLine(Body)
                    End If
                End With

                Dim SmtpServer As New SmtpClient()
                SmtpServer.UseDefaultCredentials = True
                Using mail As New MailMessage()
                    mail.SubjectEncoding = System.Text.Encoding.UTF8
                    mail.BodyEncoding = System.Text.Encoding.UTF8
                    'SmtpServer.Host = "vmmail01.polyexpert.com"
                    'mail.From = New MailAddress($"{WCF_NAME}@polyexpert.com", WCF_NAME)
                    mail.From = New MailAddress("<EMAIL>")
                    If Dest <> "" Then
                        mail.To.Add(Dest)
                    Else
                        mail.To.Add("<EMAIL>")
                    End If
                    mail.Subject = "wsPEX - " & Subject
                    mail.Body = sb.ToString
                    'SmtpServer.Send(mail)
                    Utilities.SendEmailMessage(mail)
                End Using

                If Key <> "" Then Cache.Add(Key, Key, DateTime.Now.AddMinutes(MaxOneEmailEveryXMinutes)) ' Cache key for X minutes


            Else
                ' Email was already sent previously; don't send again until cache entry expires
            End If
        Catch ex As Exception
            Log("SendEmail failed. " & ex.ToString)
        End Try

    End Sub

End Module
