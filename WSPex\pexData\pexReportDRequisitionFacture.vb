﻿Imports Utilities

Module pexReportDRequisitionFacture

    Public lstPexRF As List(Of pexRequisitionFacture)
    Private dicPexRF As Dictionary(Of Long, pexRequisitionFacture)
    Private dicPexRF_Fac As Dictionary(Of Integer, List(Of pexRequisitionFacture))

    Public Sub CleanMemory()
        lstPexRF.Clear()
        dicPexRF.Clear()
        dicPexRF_Fac.Clear()
    End Sub

    Public Sub BuildRequisitionFacture()

        Using New Chrono(Sub(ms) Log($"BuildRequisitionFacture done. Elapsed: {ms:#,0}ms"), Sub() Log("BuildRequisitionFacture started...", color:=ConsoleColor.White))

            lstPexRF = New List(Of pexRequisitionFacture)
            dicPexRF = New Dictionary(Of Long, pexRequisitionFacture)
            dicPexRF_Fac = New Dictionary(Of Integer, List(Of pexRequisitionFacture))

            ' -reel-new
            Dim lstPal = GetCoutTransportStandard()
            UpdateTransportCommande(lstPal) ' Update revenu transport cmd
            UpdateTransportFacture(lstPal) ' Update revenu transport fac

            '-todo- voir spRemplirRequisitionFacture - le 1er step est un "delete"...
            RequisitionFacture()
            FacturePostRequisition()
            CommandePostRequisition()

        End Using

    End Sub

    Private Sub RequisitionFacture()

        For Each crt In dicCRT.Values
            Dim rf = GetPexRF(dicPexRF, crt)

            Dim rt = dicRT(rf.RequisitionID)
            rf.RequisitionDate = rt.dateCreationAuto.Date
            rf.RequisitionTauxUs = GetTauxChange(rf.RequisitionDate).taux
            rf.DeviseID = rt.id_devise



            If rf.DeviseID = 3 Then ' CAN
                rf.RequisitionCanMontant = rt.montant
                rf.RequisitionUsMontant = rt.montant / rf.RequisitionTauxUs
            Else ' US
                rf.RequisitionCanMontant = rt.montant * rf.RequisitionTauxUs
                rf.RequisitionUsMontant = rt.montant
            End If

            Dim lcp As List(Of commande_palette) = Nothing
            Dim sumPoidsNet = 0D
            If dicCmdPal_Req.TryGetValue(rt.id_requisition_transport, lcp) Then
                For Each cp In lcp
                    ' Demande de Donald Décembre 2022 - pour les commande facturée en "rouleau", on prend le poids rouleau calculé dans la commande au lieu du poids net palette
                    If cp.fac_itm_unite = "ROL" Then
                        If cp.nb_rouleau.HasValue AndAlso cp.cmd_poids_rou_cal.HasValue Then
                            sumPoidsNet += cp.nb_rouleau.Value * CDec(cp.cmd_poids_rou_cal.Value)
                        Else
                            ' fallback sur méthode habituelle.
                            sumPoidsNet += cp.poids_net
                        End If
                    Else
                        sumPoidsNet += cp.poids_net
                    End If
                    If cp.id_commande = rf.CommandeID Then
                        Dim itm As facture_item = Nothing
                        If dicFactureItem.TryGetValue(cp.id_facture_item, itm) Then
                            rf.FactureID = itm.id_facture
                        End If
                    End If
                Next
            End If


            If sumPoidsNet > 0 Then
                rf.RequisitionCanMontantLbs = rf.RequisitionCanMontant / sumPoidsNet
                rf.RequisitionUsMontantLbs = rf.RequisitionUsMontant / sumPoidsNet
            End If

            Dim pf As pexFacture
            If rf.FactureID > 0 Then
                pf = dicPexFac(rf.FactureID)
                rf.RequisitionFactureLbs = pf.FactureLbs
                rf.RequisitionFactureCanMontant = If(rf.RequisitionCanMontantLbs, 0) * rf.RequisitionFactureLbs
                rf.RequisitionFactureUsMontant = rf.RequisitionUsMontantLbs * rf.RequisitionFactureLbs

                Dim lst As List(Of pexRequisitionFacture) = Nothing
                If Not dicPexRF_Fac.TryGetValue(rf.FactureID, lst) Then
                    lst = New List(Of pexRequisitionFacture)
                    dicPexRF_Fac.Add(rf.FactureID, lst)
                End If


                lst.Add(rf)
            End If
        Next

    End Sub

    Private Sub FacturePostRequisition()

        For Each pf In lstPexFac

            ' Requisition facture
            Dim lrf As List(Of pexRequisitionFacture) = Nothing


            Dim rf As pexRequisitionFacture
            Dim tauxUs As Decimal = 0

            Dim RequisitionCanMontantLbs_Prorata As Decimal? = Nothing 'c'est tres important le ? et le nothing
            If dicPexRF_Fac.TryGetValue(pf.FactureID, lrf) Then
                'commande en probleme 220047, 298279 et 791798 (surement une correction manuelle de la commande)
                'ils ont 2 requisiton et le First prennais une requisition au hasard, alors quand on appelais une 2 fois, c'etait pas les meme chiffres
                'maintenant on prends un prorata pour le RequisitionCanMontantLbs et RequisitionCanMontantLbs des requisitions.
                rf = lrf.First

                Dim total_lbs_for_all_requisition As Decimal? = lrf.Sum(Function(x) x.RequisitionFactureLbs)
                If total_lbs_for_all_requisition.HasValue AndAlso total_lbs_for_all_requisition.Value <> 0 Then
                    Dim REELVariableClientTauxUS_Prorata As Decimal = 0

                    For Each rf In lrf
                        Dim Calcul As Decimal? = 0 'c'est important le ? et le nothing pour que le calcul soit comem avant.
                        REELVariableClientTauxUS_Prorata += (rf.RequisitionFactureLbs / total_lbs_for_all_requisition.Value) * If(NotZero(rf.RequisitionTauxUs), rf.RequisitionTauxUs, pf.FactureTaux)

                        Calcul = (rf.RequisitionFactureLbs / total_lbs_for_all_requisition.Value) * rf.RequisitionCanMontantLbs
                        If Not RequisitionCanMontantLbs_Prorata.HasValue Then
                            RequisitionCanMontantLbs_Prorata = Calcul
                        Else
                            RequisitionCanMontantLbs_Prorata += Calcul
                        End If

                    Next

                    'pf.REELVariableClientTauxUS = REELVariableClientTauxUS_Prorata
                    tauxUs = REELVariableClientTauxUS_Prorata

                Else
                    'pf.REELVariableClientTauxUS = If(NotZero(rf.RequisitionTauxUs), rf.RequisitionTauxUs, pf.FactureTaux)
                    tauxUs = If(NotZero(rf.RequisitionTauxUs), rf.RequisitionTauxUs, pf.FactureTaux)
                    RequisitionCanMontantLbs_Prorata = rf.RequisitionCanMontantLbs
                End If


            Else
                '                pf.REELVariableClientTauxUS = pf.FactureTaux
                TauxUS = pf.FactureTaux
                rf = New pexRequisitionFacture
            End If

            pf.REELVariableClientTauxUS = tauxUs



            Dim dat = pf.FactureDate
            Dim svg = GetVarGlob(dat)
            Dim pc = TryGetValue(dicPexCmd, pf.CommandeID)
            Dim lsh = GetListeSecteurHistorique(pc.CommandeSecteurID, dat)
            Dim ch = GetClientHistorique(pf.ClientID, dat, rabais:=True)
            Dim ach = GetAdresseClientHistorique(GetDualIdKey(pc.ClientID, pc.CommandeAdresseID), dat)
            Dim tc = GetTauxChange(dat)

            Dim expId = 0
            Dim itms As List(Of facture_item) = Nothing
            If dicFactureItem_Fac.TryGetValue(pf.FactureID, itms) Then
                For Each itm In itms
                    Dim lcp As List(Of commande_palette) = Nothing
                    If dicCmdPal_Itm.TryGetValue(itm.id_facture_item, lcp) Then
                        For Each cp In lcp
                            If cp.id_expedition > 0 Then
                                expId = cp.id_expedition
                                Exit For
                            End If
                        Next
                    End If
                    If expId > 0 Then Exit For
                Next
            End If
            Dim exped = TryGetValue(dicExpedition, expId)
            Dim transport = TryGetValue(dicTransport, exped.id_transport)

            Debug.Assert(exped IsNot Nothing)
            Debug.Assert(transport IsNot Nothing)

            'If If(rf.RequisitionCanMontantLbs, lsh.cout_transport) <> If(RequisitionCanMontantLbs_Prorata, lsh.cout_transport) Then
            '    Debug.WriteLine("")
            'End If
            'pf.REELVCTransportCanMontantLbs = If(rf.RequisitionCanMontantLbs, lsh.cout_transport)
            pf.REELVCTransportCanMontantLbs = If(RequisitionCanMontantLbs_Prorata, lsh.cout_transport)
            pf.REELVCDouaneCanMontantLbs = lsh.douane_assurance
            pf.REELVCEntreposageCanMontantLbs = svg.entreposage
            pf.REELVCRabaisClientCanMontantLbs = pf.REELRabaisTransportFactureCanMontantLbs '-reel-new-
            pf.REELVCEntrepotExterneCanMontantLbs = CDec(ach.cout_entrepo_ext_us) * If(tc.taux > 0, tc.taux, 1)

            pf.REELVariableClientCanMontantLbs = pf.REELVCTransportCanMontantLbs _
                                               + pf.REELVCDouaneCanMontantLbs _
                                               + pf.REELVCEntreposageCanMontantLbs _
                                               + pf.REELVCRabaisClientCanMontantLbs _
                                               + pf.REELVCEntrepotExterneCanMontantLbs

            'Dim taux = If(NotZero(rf.RequisitionTauxUs), rf.RequisitionTauxUs, pf.FactureTaux)
            Dim taux = tauxUs



            If taux = 0 Then
                pf.REELVariableClientUsMontantLbs = 99999
            Else 'cou_std ok

                pf.REELVCTransportUsMontantLbs = pf.REELVCTransportCanMontantLbs / taux
                pf.REELVCDouaneUsMontantLbs = pf.REELVCDouaneCanMontantLbs / taux
                pf.REELVCEntreposageUsMontantLbs = pf.REELVCEntreposageCanMontantLbs / taux
                pf.REELVCRabaisClientUsMontantLbs = pf.REELVCRabaisClientCanMontantLbs / taux
                pf.REELVCEntrepotExterneUsMontantLbs = pf.REELVCEntrepotExterneCanMontantLbs / taux

                pf.REELVariableClientUsMontantLbs = pf.REELVariableClientCanMontantLbs / taux

            End If

        Next

    End Sub

    Private Sub CommandePostRequisition()

        Dim dicTmp = lstPexFac.GroupBy(Function(x) x.CommandeID).ToDictionary(Function(k) k.Key, Function(v) v.ToList) '-todo- keep around?

        For Each pc In lstPexCmd

            Dim lpf As List(Of pexFacture) = Nothing
            If dicTmp.TryGetValue(pc.CommandeID, lpf) Then

                Dim sumFacLbs = lpf.Sum(Function(f) f.FactureLbs)
                Dim sumFacVarCliCanLbs = lpf.Sum(Function(f) f.REELVariableClientCanMontantLbs * f.FactureLbs)
                Dim sumFacVarCliUSLbs = lpf.Sum(Function(f) f.REELVariableClientUsMontantLbs * f.FactureLbs)
                Dim sumFacVarCliTauxUS = lpf.Sum(Function(f) f.REELVariableClientTauxUS * f.FactureLbs)

                Dim sumFacVCTransportCanMontant = lpf.Sum(Function(f) f.REELVCTransportCanMontantLbs * f.FactureLbs)
                Dim sumFacVCDouaneCanMontant = lpf.Sum(Function(f) f.REELVCDouaneCanMontantLbs * f.FactureLbs)
                Dim sumFacVCEntreposageCanMontant = lpf.Sum(Function(f) f.REELVCEntreposageCanMontantLbs * f.FactureLbs)
                Dim sumFacVCRabaisClientCanMontant = lpf.Sum(Function(f) f.REELVCRabaisClientCanMontantLbs * f.FactureLbs)
                Dim sumFacVCEntrepotExterneCanMontant = lpf.Sum(Function(f) f.REELVCEntrepotExterneCanMontantLbs * f.FactureLbs)

                Dim sumFacVCTransportUSMontant = lpf.Sum(Function(f) f.REELVCTransportUsMontantLbs * f.FactureLbs)
                Dim sumFacVCDouaneUSMontant = lpf.Sum(Function(f) f.REELVCDouaneUsMontantLbs * f.FactureLbs)
                Dim sumFacVCEntreposageUSMontant = lpf.Sum(Function(f) f.REELVCEntreposageUsMontantLbs * f.FactureLbs)
                Dim sumFacVCRabaisClientUSMontant = lpf.Sum(Function(f) f.REELVCRabaisClientUsMontantLbs * f.FactureLbs)
                Dim sumFacVCEntrepotExterneUsMontant = lpf.Sum(Function(f) f.REELVCEntrepotExterneUsMontantLbs * f.FactureLbs)

                If sumFacLbs > 0 Then

                    Dim douaneCan = 0D, douaneUS = 0D
                    For Each pf In lpf
                        Dim lsh = GetListeSecteurHistorique(pc.CommandeSecteurID, pf.FactureDate)
                        douaneCan += lsh.douane_assurance * pf.FactureLbs
                        douaneUS += lsh.douane_assurance * If(pf.REELVariableClientTauxUS = 0, 0, pf.FactureLbs / pf.REELVariableClientTauxUS)
                    Next



                    pc.REELVariableClientCanMontantLbs = sumFacVarCliCanLbs / sumFacLbs
                    pc.REELVariableClientUsMontantLbs = sumFacVarCliUSLbs / sumFacLbs
                    pc.REELVariableClientTauxUS = sumFacVarCliTauxUS / sumFacLbs

                    pc.REELDouaneAssuranceCanMontantLbs = douaneCan / sumFacLbs
                    pc.REELDouaneAssuranceUsMontantLbs = douaneUS / sumFacLbs

                    pc.REELVCTransportCanMontantLbs = sumFacVCTransportCanMontant / sumFacLbs
                    pc.REELVCDouaneCanMontantLbs = sumFacVCDouaneCanMontant / sumFacLbs
                    pc.REELVCEntreposageCanMontantLbs = sumFacVCEntreposageCanMontant / sumFacLbs
                    pc.REELVCRabaisClientCanMontantLbs = sumFacVCRabaisClientCanMontant / sumFacLbs
                    pc.REELVCEntrepotExterneCanMontantLbs = sumFacVCEntrepotExterneCanMontant / sumFacLbs

                    pc.REELVCTransportUsMontantLbs = sumFacVCTransportUSMontant / sumFacLbs
                    pc.REELVCDouaneUsMontantLbs = sumFacVCDouaneUSMontant / sumFacLbs
                    pc.REELVCEntreposageUsMontantLbs = sumFacVCEntreposageUSMontant / sumFacLbs
                    pc.REELVCRabaisClientUsMontantLbs = sumFacVCRabaisClientUSMontant / sumFacLbs
                    pc.REELVCEntrepotExterneUsMontantLbs = sumFacVCEntrepotExterneUsMontant / sumFacLbs

                End If
            End If
        Next

    End Sub

    Function GetPexRF(dic As Dictionary(Of Long, pexRequisitionFacture), crt As commande_requisition_transport) As pexRequisitionFacture

        'Static identity As Integer = 0

        Dim key = GetDualIdKey(crt.id_commande_requisition_transport, crt.id_commande)
        Dim prf As pexRequisitionFacture = Nothing
        If Not dic.TryGetValue(key, prf) Then
            'identity += 1
            prf = New pexRequisitionFacture With {'.RequisitionFactureId = identity,
                                                  .RequisitionID = crt.id_requisition_transport,
                                                  .CommandeID = crt.id_commande
                                                  }
            dic.Add(key, prf)
            lstPexRF.Add(prf)
        End If

        Return prf

    End Function

    Private Sub UpdateTransportCommande(lstPal As List(Of commande_palette_work))

        Dim lst = lstPal.Where(Function(x) x._fac_trs_mnt_can <> 0 OrElse x._fac_trs_rbs_can <> 0)
        Dim grp = lst.GroupBy(Function(x) x.id_commande)
        Dim dic = grp.ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        Dim lp As List(Of commande_palette_work) = Nothing
        For Each pc In lstPexCmd

            'Debug.Assert(pc.CommandeID <> 794904)

            If dic.TryGetValue(pc.CommandeID, lp) Then

                Dim totLb = lp.Sum(Function(x) x._fac_trs_qte_lb)
                Dim totMnt = lp.Sum(Function(x) x._fac_trs_mnt_can)
                Dim totRab = lp.Sum(Function(x) x._fac_trs_rbs_can) * -1 ' remettre les rabais en positif

                pc.REELRevenuTransportCanMontantLbs = R4(totMnt / totLb)
                pc.REELRevenuTransportUSMontantLbs = R4(pc.REELRevenuTransportCanMontantLbs / pc.STDCMDTauxUS)
                pc.REELRabaisTransportFactureCanMontantLbs = R4(totRab / totLb)
                pc.REELRabaisTransportFactureUSMontantLbs = R4(pc.REELRabaisTransportFactureCanMontantLbs / pc.STDCMDTauxUS)

            End If
        Next

    End Sub

    Private Sub UpdateTransportFacture(lstPal As List(Of commande_palette_work))

        Dim lst = lstPal.Where(Function(x) x._fac_trs_mnt_can <> 0 OrElse x._fac_trs_rbs_can <> 0)
        Dim grp = lst.GroupBy(Function(x) x._fac_id_facture)
        Dim dic = grp.ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        Dim lp As List(Of commande_palette_work) = Nothing
        For Each pf In lstPexFac

            If dic.TryGetValue(pf.FactureID, lp) Then

                Dim totLb = lp.Sum(Function(x) x._fac_trs_qte_lb)
                Dim totMnt = lp.Sum(Function(x) x._fac_trs_mnt_can)
                Dim totRab = lp.Sum(Function(x) x._fac_trs_rbs_can) * -1 ' remettre les rabais en positif

                pf.REELRevenuTransportCanMontantLbs = R4(totMnt / totLb)
                pf.REELRevenuTransportUSMontantLbs = R4(pf.REELRevenuTransportCanMontantLbs / pf.FactureTaux)
                pf.REELRabaisTransportFactureCanMontantLbs = R4(totRab / totLb)
                pf.REELRabaisTransportFactureUSMontantLbs = R4(pf.REELRabaisTransportFactureCanMontantLbs / pf.FactureTaux)

            End If
        Next

    End Sub

End Module
