﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_produit")>
<ProtoContract>
Partial Public Class produit
    <ProtoMember(1)> Public Property id_produit As Integer
    <ProtoMember(2)> Public Property nom As String
    <ProtoMember(3)> Public Property id_marche As Integer
    <ProtoMember(4)> Public Property id_produit_categ As Integer
    <ProtoMember(5)> Public Property marche_strategique As String
    <ProtoMember(6)> Public Property marche As String
    <ProtoMember(7)> Public Property sous_marche As String
    <ProtoMember(8)> Public Property sous_marche_secondaire As String
    <ProtoMember(9)> Public Property regroupement As String
End Class